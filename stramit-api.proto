// Stramit Data Export gRPC Service Definition
syntax = "proto3";

package stramit.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

// Main Data Export Service
service DataExportService {
  // Get sales data with streaming support for large datasets
  rpc GetSalesData(SalesDataRequest) returns (stream SalesDataResponse);
  
  // Get customer data
  rpc GetCustomerData(CustomerDataRequest) returns (stream CustomerDataResponse);
  
  // Get leads data
  rpc GetLeadsData(LeadsDataRequest) returns (stream LeadsDataResponse);
  
  // Start background export job
  rpc StartExport(ExportJobRequest) returns (ExportJobResponse);
  
  // Get export job status
  rpc GetExportStatus(ExportStatusRequest) returns (ExportStatusResponse);
  
  // Download completed export file
  rpc DownloadExport(DownloadRequest) returns (stream FileChunk);
}

// Map Data Service for Australian map visualization
service MapDataService {
  // Get distributor locations for map markers
  rpc GetDistributorLocations(LocationRequest) returns (LocationResponse);
  
  // Get heatmap data for sales visualization
  rpc GetHeatmapData(HeatmapRequest) returns (HeatmapResponse);
  
  // Get Australian postcodes
  rpc GetPostcodes(PostcodeRequest) returns (PostcodeResponse);
}

// Request/Response Messages
message SalesDataRequest {
  google.protobuf.Timestamp start_date = 1;
  google.protobuf.Timestamp end_date = 2;
  repeated string categories = 3;
  string distributor_id = 4;
  string area = 5;
}

message SalesDataResponse {
  string order_id = 1;
  string dist_id = 2;
  double amount = 3;
  string category = 4;
  string network_id = 5;
  google.protobuf.Timestamp status_date = 6;
  string business_name = 7;
}

message CustomerDataRequest {
  google.protobuf.Timestamp start_date = 1;
  google.protobuf.Timestamp end_date = 2;
  string state = 3;
  string postcode = 4;
}

message CustomerDataResponse {
  string customer_id = 1;
  string town = 2;
  string postcode = 3;
  string state = 4;
  double total_price = 5;
  google.protobuf.Timestamp order_date = 6;
  double lat_precise = 7;
  double long_precise = 8;
}

message LeadsDataRequest {
  google.protobuf.Timestamp start_date = 1;
  google.protobuf.Timestamp end_date = 2;
  string lead_source = 3;
  string status = 4;
}

message LeadsDataResponse {
  string lead_id = 1;
  string source = 2;
  string status = 3;
  string town = 4;
  string postcode = 5;
  google.protobuf.Timestamp request_time = 6;
  string distributor_id = 7;
}

// Export Job Messages
message ExportJobRequest {
  repeated string export_types = 1; // sales, customer, leads, etc.
  google.protobuf.Timestamp start_date = 2;
  google.protobuf.Timestamp end_date = 3;
  string output_format = 4; // csv, json, excel
}

message ExportJobResponse {
  string job_id = 1;
  string status = 2;
  google.protobuf.Timestamp created_at = 3;
}

message ExportStatusRequest {
  string job_id = 1;
}

message ExportStatusResponse {
  string job_id = 1;
  string status = 2; // pending, running, completed, failed
  int32 progress_percent = 3;
  string message = 4;
  repeated string file_urls = 5;
  google.protobuf.Timestamp completed_at = 6;
}

// Map Data Messages
message LocationRequest {
  string area = 1;
  string network_id = 2;
}

message LocationResponse {
  repeated DistributorLocation locations = 1;
}

message DistributorLocation {
  string distributor_id = 1;
  string business_name = 2;
  double longitude = 3;
  double latitude = 4;
  string network_id = 5;
  string area = 6;
}

message HeatmapRequest {
  google.protobuf.Timestamp start_date = 1;
  google.protobuf.Timestamp end_date = 2;
  string data_type = 3; // sales, frequency, customers
  string area = 4;
}

message HeatmapResponse {
  repeated HeatmapPoint points = 1;
}

message HeatmapPoint {
  double latitude = 1;
  double longitude = 2;
  double intensity = 3;
  string label = 4;
  double value = 5;
}

message PostcodeRequest {
  string state = 1;
}

message PostcodeResponse {
  repeated PostcodeData postcodes = 1;
}

message PostcodeData {
  string postcode = 1;
  string locality = 2;
  string state = 3;
  double lat_precise = 4;
  double long_precise = 5;
}

// File Download Messages
message DownloadRequest {
  string file_id = 1;
}

message FileChunk {
  bytes data = 1;
  int32 chunk_number = 2;
  bool is_last = 3;
}
