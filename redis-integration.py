# Redis Integration for Stramit Data Export Project
import redis
import json
import pickle
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Any, Dict, List
import pandas as pd
import logging
from functools import wraps

logger = logging.getLogger(__name__)

class RedisManager:
    """Centralized Redis management for caching and job tracking."""
    
    def __init__(self, host='localhost', port=6379, db=0, password=None):
        self.redis_client = redis.Redis(
            host=host, 
            port=port, 
            db=db, 
            password=password,
            decode_responses=True
        )
        
        # Test connection
        try:
            self.redis_client.ping()
            logger.info("Redis connection established successfully")
        except redis.ConnectionError as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    def get_cache_key(self, prefix: str, **kwargs) -> str:
        """Generate consistent cache keys."""
        # Create a hash of the parameters for consistent keys
        params_str = json.dumps(kwargs, sort_keys=True, default=str)
        params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
        return f"{prefix}:{params_hash}"
    
    def cache_dataframe(self, key: str, df: pd.DataFrame, ttl: int = 3600):
        """Cache pandas DataFrame with TTL (time to live)."""
        try:
            # Serialize DataFrame to pickle for efficient storage
            pickled_df = pickle.dumps(df)
            self.redis_client.setex(key, ttl, pickled_df)
            logger.info(f"Cached DataFrame with key: {key} (TTL: {ttl}s)")
        except Exception as e:
            logger.error(f"Failed to cache DataFrame: {e}")
    
    def get_cached_dataframe(self, key: str) -> Optional[pd.DataFrame]:
        """Retrieve cached DataFrame."""
        try:
            pickled_df = self.redis_client.get(key)
            if pickled_df:
                df = pickle.loads(pickled_df)
                logger.info(f"Retrieved cached DataFrame: {key}")
                return df
            return None
        except Exception as e:
            logger.error(f"Failed to retrieve cached DataFrame: {e}")
            return None
    
    def cache_json(self, key: str, data: Dict, ttl: int = 3600):
        """Cache JSON data."""
        try:
            json_data = json.dumps(data, default=str)
            self.redis_client.setex(key, ttl, json_data)
            logger.info(f"Cached JSON data: {key}")
        except Exception as e:
            logger.error(f"Failed to cache JSON: {e}")
    
    def get_cached_json(self, key: str) -> Optional[Dict]:
        """Retrieve cached JSON data."""
        try:
            json_data = self.redis_client.get(key)
            if json_data:
                return json.loads(json_data)
            return None
        except Exception as e:
            logger.error(f"Failed to retrieve cached JSON: {e}")
            return None

# Redis Cache Decorator
def redis_cache(ttl: int = 3600, key_prefix: str = "cache"):
    """Decorator to cache function results in Redis."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            redis_manager = RedisManager()
            
            # Generate cache key from function name and parameters
            cache_key = redis_manager.get_cache_key(
                f"{key_prefix}:{func.__name__}",
                args=args,
                kwargs=kwargs
            )
            
            # Try to get from cache first
            if isinstance(args[0], pd.DataFrame) or any(isinstance(arg, pd.DataFrame) for arg in args):
                # Handle DataFrame results
                cached_result = redis_manager.get_cached_dataframe(cache_key)
            else:
                # Handle JSON-serializable results
                cached_result = redis_manager.get_cached_json(cache_key)
            
            if cached_result is not None:
                logger.info(f"Cache hit for {func.__name__}")
                return cached_result
            
            # Execute function and cache result
            logger.info(f"Cache miss for {func.__name__}, executing function")
            result = func(*args, **kwargs)
            
            if result is not None:
                if isinstance(result, pd.DataFrame):
                    redis_manager.cache_dataframe(cache_key, result, ttl)
                else:
                    redis_manager.cache_json(cache_key, result, ttl)
            
            return result
        return wrapper
    return decorator

class ExportJobTracker:
    """Track export job progress using Redis."""
    
    def __init__(self, redis_manager: RedisManager):
        self.redis = redis_manager
        self.job_prefix = "export_job"
    
    def create_job(self, job_id: str, job_data: Dict) -> bool:
        """Create a new export job record."""
        try:
            job_key = f"{self.job_prefix}:{job_id}"
            job_data.update({
                'created_at': datetime.now().isoformat(),
                'status': 'pending',
                'progress': 0
            })
            
            self.redis.cache_json(job_key, job_data, ttl=86400)  # 24 hours
            logger.info(f"Created export job: {job_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to create job {job_id}: {e}")
            return False
    
    def update_job_progress(self, job_id: str, progress: int, message: str = ""):
        """Update job progress."""
        try:
            job_key = f"{self.job_prefix}:{job_id}"
            job_data = self.redis.get_cached_json(job_key)
            
            if job_data:
                job_data.update({
                    'progress': progress,
                    'message': message,
                    'updated_at': datetime.now().isoformat()
                })
                self.redis.cache_json(job_key, job_data, ttl=86400)
                
                # Publish progress update for real-time notifications
                self.redis.redis_client.publish(f"job_progress:{job_id}", json.dumps({
                    'job_id': job_id,
                    'progress': progress,
                    'message': message
                }))
                
                logger.info(f"Updated job {job_id} progress: {progress}%")
        except Exception as e:
            logger.error(f"Failed to update job progress: {e}")
    
    def complete_job(self, job_id: str, file_urls: List[str]):
        """Mark job as completed."""
        try:
            job_key = f"{self.job_prefix}:{job_id}"
            job_data = self.redis.get_cached_json(job_key)
            
            if job_data:
                job_data.update({
                    'status': 'completed',
                    'progress': 100,
                    'completed_at': datetime.now().isoformat(),
                    'file_urls': file_urls
                })
                self.redis.cache_json(job_key, job_data, ttl=86400)
                
                # Publish completion notification
                self.redis.redis_client.publish(f"job_complete:{job_id}", json.dumps({
                    'job_id': job_id,
                    'file_urls': file_urls
                }))
                
                logger.info(f"Completed job: {job_id}")
        except Exception as e:
            logger.error(f"Failed to complete job: {e}")
    
    def get_job_status(self, job_id: str) -> Optional[Dict]:
        """Get current job status."""
        job_key = f"{self.job_prefix}:{job_id}"
        return self.redis.get_cached_json(job_key)

# Enhanced Data Exporters with Redis Caching
class CachedSalesExporter:
    """Sales exporter with Redis caching."""
    
    def __init__(self, redis_manager: RedisManager):
        self.redis = redis_manager
        # Import your existing exporter
        from python_data_exporter.exporters.sales_exporter import SalesExporter
        self.exporter = SalesExporter()
    
    @redis_cache(ttl=1800, key_prefix="sales_data")  # 30 minutes cache
    def get_sales_data(self, start_date: datetime, end_date: datetime, 
                      category: str = None, distributor_id: str = None) -> pd.DataFrame:
        """Get sales data with caching."""
        logger.info(f"Fetching sales data: {start_date} to {end_date}")
        
        # Use your existing export logic
        df = self.exporter.extract_sales_data(start_date, end_date)
        
        if df is not None and not df.empty:
            # Apply filters
            if category and category != 'All':
                df = df[df['Category'] == category]
            
            if distributor_id and distributor_id != 'All':
                df = df[df['dist_id'] == distributor_id]
        
        return df
    
    @redis_cache(ttl=3600, key_prefix="heatmap_data")  # 1 hour cache
    def get_heatmap_data(self, start_date: datetime, end_date: datetime, 
                        data_type: str = 'sales') -> List[Dict]:
        """Get processed heatmap data for map visualization."""
        df = self.get_sales_data(start_date, end_date)
        
        if df is None or df.empty:
            return []
        
        # Process for heatmap (replicating your R logic)
        if data_type == 'sales':
            grouped = df.groupby('dist_id').agg({
                'Ammount': 'sum',
                'BusinessName': 'first',
                'Lo': 'first',  # Longitude
                'La': 'first'   # Latitude
            }).reset_index()
            
            # Calculate heat intensity
            min_val = grouped['Ammount'].min()
            max_val = grouped['Ammount'].max() + 1000
            grouped['heat_intensity'] = (grouped['Ammount'] - min_val) / (max_val - min_val)
            
            # Convert to list of dictionaries for JSON serialization
            heatmap_points = []
            for _, row in grouped.iterrows():
                heatmap_points.append({
                    'latitude': float(row['La']),
                    'longitude': float(row['Lo']),
                    'intensity': float(row['heat_intensity']),
                    'label': f"{row['BusinessName']} $$$: {row['Ammount']:,.0f}",
                    'value': float(row['Ammount'])
                })
            
            return heatmap_points
        
        return []

# Australian Postcodes Cache Manager
class PostcodeCache:
    """Manage Australian postcodes data in Redis."""
    
    def __init__(self, redis_manager: RedisManager):
        self.redis = redis_manager
        self.cache_key = "australian_postcodes"
    
    def load_postcodes(self) -> pd.DataFrame:
        """Load Australian postcodes with caching."""
        # Try cache first
        cached_df = self.redis.get_cached_dataframe(self.cache_key)
        if cached_df is not None:
            return cached_df
        
        # Load from file if not cached
        try:
            from pathlib import Path
            postcodes_path = Path("australian_postcodes.csv")
            
            if postcodes_path.exists():
                df = pd.read_csv(postcodes_path)
                # Cache for 24 hours (postcodes don't change often)
                self.redis.cache_dataframe(self.cache_key, df, ttl=86400)
                logger.info("Loaded and cached Australian postcodes")
                return df
            else:
                logger.error("Australian postcodes file not found")
                return pd.DataFrame()
        except Exception as e:
            logger.error(f"Failed to load postcodes: {e}")
            return pd.DataFrame()
    
    def get_postcodes_by_state(self, state: str) -> List[Dict]:
        """Get postcodes filtered by state."""
        df = self.load_postcodes()
        if df.empty:
            return []
        
        if state and state != 'All':
            df = df[df['state'] == state]
        
        return df[['postcode', 'locality', 'state']].to_dict('records')

# Usage Example
if __name__ == "__main__":
    # Initialize Redis
    redis_manager = RedisManager()
    
    # Initialize cached exporters
    sales_exporter = CachedSalesExporter(redis_manager)
    job_tracker = ExportJobTracker(redis_manager)
    postcode_cache = PostcodeCache(redis_manager)
    
    # Example: Get cached sales data
    from datetime import datetime, timedelta
    
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    # This will cache the result for 30 minutes
    sales_data = sales_exporter.get_sales_data(start_date, end_date)
    print(f"Retrieved {len(sales_data)} sales records")
    
    # Get heatmap data (cached for 1 hour)
    heatmap_data = sales_exporter.get_heatmap_data(start_date, end_date)
    print(f"Generated {len(heatmap_data)} heatmap points")
    
    # Load postcodes (cached for 24 hours)
    nsw_postcodes = postcode_cache.get_postcodes_by_state('NSW')
    print(f"Found {len(nsw_postcodes)} NSW postcodes")
