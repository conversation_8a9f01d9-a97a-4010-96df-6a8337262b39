"""
Helper utility functions for Stramit Data Export Tool.
"""

import pandas as pd
import logging
import re
from typing import Dict, List, Any, Optional
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

def categorize_sales_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Categorize sales data based on NetworkID and OrderIdCode.
    Replicates the category logic from Backend.R
    
    Args:
        df: DataFrame with NetworkID and OrderIdCode columns
        
    Returns:
        DataFrame with added Category column
    """
    df = df.copy()
    
    # Initialize Category column with default
    df['Category'] = 'FDB Sheds'  # Default category
    
    # SB Sheds: NetworkID = "STR"
    df.loc[df['NetworkID'] == 'STR', 'Category'] = 'SB Sheds'
    
    # FDB Patios: NetworkID = "FDBP"
    df.loc[df['NetworkID'] == 'FDBP', 'Category'] = 'FDB Patios'
    
    # Lifestyle: NetworkID = "STO"
    df.loc[df['NetworkID'] == 'STO', 'Category'] = 'Lifestyle'
    
    # NZ Sheds: OrderIdCode starts with "Z"
    df.loc[df['OrderIdCode'].str.startswith('Z', na=False), 'Category'] = 'NZ Sheds'
    
    return df

def categorize_customer_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Categorize customer data based on DistributerCode.
    Replicates the category logic from Backend.R for customer data.
    
    Args:
        df: DataFrame with DistributerCode column
        
    Returns:
        DataFrame with added Category column
    """
    df = df.copy()
    
    # Initialize Category column with default
    df['Category'] = 'FDB Sheds'  # Default category
    
    # SB Sheds: DistributerCode = "STR"
    df.loc[df['DistributerCode'] == 'STR', 'Category'] = 'SB Sheds'
    
    # FDB Patios: DistributerCode = "FDBP"
    df.loc[df['DistributerCode'] == 'FDBP', 'Category'] = 'FDB Patios'
    
    # Lifestyle: DistributerCode = "STO"
    df.loc[df['DistributerCode'] == 'STO', 'Category'] = 'Lifestyle'
    
    # NZ Sheds: DistributerCode starts with "Z"
    df.loc[df['DistributerCode'].str.startswith('Z', na=False), 'Category'] = 'NZ Sheds'
    
    return df

def extract_distributor_id(order_id_code: str) -> str:
    """
    Extract distributor ID from OrderIdCode.
    Replicates the SUBSTRING_INDEX logic from R SQL queries.
    
    Args:
        order_id_code: Full order ID code (e.g., "DIST123\\ORDER456")
        
    Returns:
        Distributor ID (e.g., "DIST123")
    """
    if pd.isna(order_id_code) or not order_id_code:
        return 'Unassigned'
    
    # Split on backslash and take first part
    parts = str(order_id_code).split('\\')
    return parts[0] if parts else 'Unassigned'

def categorize_lead_sources(df: pd.DataFrame) -> pd.DataFrame:
    """
    Categorize lead sources into App and Web categories.
    Based on the lead source logic from Backend.R
    
    Args:
        df: DataFrame with xml_program_name or LeadSource column
        
    Returns:
        DataFrame with added LeadCategory column
    """
    df = df.copy()
    
    # Determine the column name for lead source
    source_col = 'xml_program_name' if 'xml_program_name' in df.columns else 'LeadSource'
    
    if source_col not in df.columns:
        logger.warning(f"No lead source column found in DataFrame")
        df['LeadCategory'] = 'Unknown'
        return df
    
    # App sources
    app_sources = ['designer.ios', 'designer.android', 'designer.web']
    # Web sources  
    web_sources = ['web.toast.au']
    
    df['LeadCategory'] = 'Other'  # Default
    
    # Categorize App leads
    df.loc[df[source_col].isin(app_sources), 'LeadCategory'] = 'App'
    
    # Categorize Web leads
    df.loc[df[source_col].isin(web_sources), 'LeadCategory'] = 'Web'
    
    return df

def clean_address_data(df: pd.DataFrame, address_col: str = 'Address') -> pd.DataFrame:
    """
    Clean address data by removing newlines and formatting.
    Replicates the address cleaning from Backend.R
    
    Args:
        df: DataFrame with address column
        address_col: Name of the address column
        
    Returns:
        DataFrame with cleaned address data
    """
    df = df.copy()
    
    if address_col in df.columns:
        # Remove newlines and extra whitespace
        df[address_col] = df[address_col].astype(str).str.replace('\n', ' ', regex=False)
        df[address_col] = df[address_col].str.replace('\r', ' ', regex=False)
        df[address_col] = df[address_col].str.strip()
        
        # Convert Town to uppercase if it exists
        if 'Town' in df.columns:
            df['Town'] = df['Town'].astype(str).str.upper()
    
    return df

def parse_xml_field(xml_string: str, field_name: str) -> str:
    """
    Parse XML field from string.
    Replicates the SUBSTR/INSTR logic from R for XML parsing.
    
    Args:
        xml_string: XML string to parse
        field_name: Field name to extract (e.g., 'Street', 'Town', 'Postcode')
        
    Returns:
        Extracted field value or empty string if not found
    """
    if pd.isna(xml_string) or not xml_string:
        return ''
    
    xml_str = str(xml_string)
    start_tag = f'<{field_name}>'
    end_tag = f'</{field_name}>'
    
    start_pos = xml_str.find(start_tag)
    if start_pos == -1:
        return ''
    
    start_pos += len(start_tag)
    end_pos = xml_str.find(end_tag, start_pos)
    
    if end_pos == -1:
        return ''
    
    return xml_str[start_pos:end_pos].strip()

def validate_postcode(postcode: str, min_length: int = 3) -> bool:
    """
    Validate postcode format.
    Replicates the postcode filtering logic from Backend.R
    
    Args:
        postcode: Postcode string to validate
        min_length: Minimum length for valid postcode
        
    Returns:
        True if postcode is valid, False otherwise
    """
    if pd.isna(postcode) or not postcode:
        return False
    
    postcode_str = str(postcode).strip()
    return len(postcode_str) >= min_length and postcode_str.isdigit()

def save_dataframe_to_csv(df: pd.DataFrame, filepath: Path, encoding: str = 'utf-8') -> bool:
    """
    Save DataFrame to CSV with error handling.
    
    Args:
        df: DataFrame to save
        filepath: Path to save the CSV file
        encoding: File encoding (default: utf-8)
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Ensure directory exists
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # Save to CSV
        df.to_csv(filepath, index=False, encoding=encoding)
        logger.info(f"Successfully saved {len(df)} records to {filepath}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to save CSV to {filepath}: {e}")
        return False

def get_unique_values(df: pd.DataFrame, column: str) -> List[str]:
    """
    Get unique values from a DataFrame column, excluding NaN.
    
    Args:
        df: DataFrame
        column: Column name
        
    Returns:
        List of unique values
    """
    if column not in df.columns:
        return []
    
    unique_vals = df[column].dropna().unique().tolist()
    return sorted([str(val) for val in unique_vals])

def format_currency(amount: float) -> str:
    """
    Format currency amount with commas.
    
    Args:
        amount: Currency amount
        
    Returns:
        Formatted currency string
    """
    if pd.isna(amount):
        return '$0'
    
    return f"${amount:,.2f}"

def log_dataframe_info(df: pd.DataFrame, name: str) -> None:
    """
    Log information about a DataFrame.
    
    Args:
        df: DataFrame to analyze
        name: Name/description of the DataFrame
    """
    logger.info(f"{name} DataFrame Info:")
    logger.info(f"  - Shape: {df.shape}")
    logger.info(f"  - Columns: {list(df.columns)}")
    logger.info(f"  - Memory usage: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    
    if len(df) > 0:
        logger.info(f"  - Date range: {df.select_dtypes(include=['datetime64']).min().min()} to {df.select_dtypes(include=['datetime64']).max().max()}")
