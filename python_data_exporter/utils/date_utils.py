"""
Date utility functions for Stramit Data Export Tool.
Replicates the date calculation logic from helpers.R
"""

from datetime import datetime, timedelta
import calendar

def get_working_days(year: int, month: int, start_date: int = 1) -> int:
    """
    Calculate the number of working days (Monday-Friday) in a month.
    Replicates the get_working_day function from helpers.R
    
    Args:
        year: Year (e.g., 2024)
        month: Month (1-12)
        start_date: Starting day of month (default: 1)
        
    Returns:
        Number of working days from start_date to end of month
    """
    # Get number of days in the month
    days_in_month = calendar.monthrange(year, month)[1]
    
    working_day_count = 0
    
    for day in range(start_date, days_in_month + 1):
        date_obj = datetime(year, month, day)
        # Monday = 0, Sunday = 6 in <PERSON>'s weekday()
        # We want Monday-Friday (0-4)
        if date_obj.weekday() < 5:  # Monday to Friday
            working_day_count += 1
    
    return working_day_count

def get_working_days_from_date(date_obj: datetime) -> int:
    """
    Calculate working days remaining in month from a specific date.
    Replicates the get_working_day2 function from helpers.R
    
    Args:
        date_obj: Date object
        
    Returns:
        Number of working days from the given date to end of month
    """
    year = date_obj.year
    month = date_obj.month
    day = date_obj.day
    
    return get_working_days(year, month, day)

def get_financial_year_dates(end_date: datetime) -> tuple:
    """
    Get the start and end dates for the financial year.
    Based on the R app logic that uses 360 days back from end_date.
    
    Args:
        end_date: End date for the financial year
        
    Returns:
        Tuple of (start_date, end_date) for the financial year
    """
    start_date = end_date - timedelta(days=360)
    return start_date, end_date

def format_date_for_sql(date_obj: datetime) -> str:
    """
    Format date for SQL queries.
    
    Args:
        date_obj: Date object to format
        
    Returns:
        Date string formatted for SQL (YYYY-MM-DD)
    """
    return date_obj.strftime('%Y-%m-%d')

def get_date_range_string(start_date: datetime, end_date: datetime) -> str:
    """
    Get a string representation of date range for filenames.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        Date range string (YYYYMMDD_to_YYYYMMDD)
    """
    start_str = start_date.strftime('%Y%m%d')
    end_str = end_date.strftime('%Y%m%d')
    return f"{start_str}_to_{end_str}"

def parse_date_string(date_string: str) -> datetime:
    """
    Parse date string in various formats.
    
    Args:
        date_string: Date string (YYYY-MM-DD, YYYY/MM/DD, etc.)
        
    Returns:
        Datetime object
        
    Raises:
        ValueError: If date string cannot be parsed
    """
    formats = ['%Y-%m-%d', '%Y/%m/%d', '%d-%m-%Y', '%d/%m/%Y']
    
    for fmt in formats:
        try:
            return datetime.strptime(date_string, fmt)
        except ValueError:
            continue
    
    raise ValueError(f"Unable to parse date string: {date_string}")

def get_month_year_combinations(start_date: datetime, end_date: datetime) -> list:
    """
    Get all month-year combinations between two dates.
    Useful for time-series data aggregation.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        List of (year, month) tuples
    """
    combinations = []
    current_date = start_date.replace(day=1)  # Start from first day of month
    
    while current_date <= end_date:
        combinations.append((current_date.year, current_date.month))
        
        # Move to next month
        if current_date.month == 12:
            current_date = current_date.replace(year=current_date.year + 1, month=1)
        else:
            current_date = current_date.replace(month=current_date.month + 1)
    
    return combinations
