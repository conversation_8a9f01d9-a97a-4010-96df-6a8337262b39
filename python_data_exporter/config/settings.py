"""
Application settings and configuration for Stramit Data Export Tool.
"""

import os
from datetime import datetime, timedelta
from pathlib import Path

class Settings:
    """Application settings and default values."""
    
    # Project root directory
    PROJECT_ROOT = Path(__file__).parent.parent
    
    # Default directories
    DATA_DIR = PROJECT_ROOT / "data"
    LOGS_DIR = PROJECT_ROOT / "logs"
    
    # Ensure directories exist
    DATA_DIR.mkdir(exist_ok=True)
    LOGS_DIR.mkdir(exist_ok=True)
    
    # Default date range (last 360 days, matching R app)
    DEFAULT_DAYS_BACK = 360
    DEFAULT_START_DATE = datetime.now().date() - timedelta(days=DEFAULT_DAYS_BACK)
    DEFAULT_END_DATE = datetime.now().date()
    
    # Sales categories (matching R app logic)
    SALES_CATEGORIES = {
        'FDB Sheds': 'Default category for Fair Dinkum Builds sheds',
        'SB Sheds': 'Steel Buildings sheds (NetworkID = STR)',
        'FDB Patios': 'Fair Dinkum Builds patios (NetworkID = FDBP)',
        'Lifestyle': 'Lifestyle products (NetworkID = STO)',
        'NZ Sheds': 'New Zealand sheds (OrderIdCode starts with Z)'
    }
    
    # Lead source categories (matching R app)
    LEAD_SOURCES = {
        'app': ['designer.ios', 'designer.android', 'designer.web'],
        'web': ['web.toast.au']
    }
    
    # Default unit price for calculations (matching R app)
    DEFAULT_UNIT_PRICE = 286
    
    # Export file naming
    TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"
    
    # Logging configuration
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # CSV export settings
    CSV_ENCODING = "utf-8"
    CSV_INDEX = False  # Don't include row indices in CSV files
    
    # Query limits for safety
    MAX_RECORDS_PER_QUERY = 100000
    
    @classmethod
    def get_output_filename(cls, data_type: str, start_date: str = None, end_date: str = None) -> str:
        """
        Generate output filename with timestamp.
        
        Args:
            data_type: Type of data being exported
            start_date: Start date for the export (optional)
            end_date: End date for the export (optional)
            
        Returns:
            Filename string with timestamp
        """
        timestamp = datetime.now().strftime(cls.TIMESTAMP_FORMAT)
        
        if start_date and end_date:
            return f"{data_type}_{start_date}_to_{end_date}_{timestamp}.csv"
        else:
            return f"{data_type}_{timestamp}.csv"
    
    @classmethod
    def get_log_filename(cls) -> str:
        """Get log filename with date."""
        date_str = datetime.now().strftime("%Y%m%d")
        return f"stramit_export_{date_str}.log"
