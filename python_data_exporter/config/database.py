"""
Database configuration and connection management for Stramit Data Export Tool.

This module handles connections to the 4 MySQL databases used by the R Shiny application:
1. CDB Database - Sales payments, address, and target data
2. App Database - Leads and quotes data  
3. Reporting Database - Customer and order data
4. App Database (Secondary) - Additional leads processing
"""

import mysql.connector
from mysql.connector import Error
import logging
from contextlib import contextmanager
from typing import Optional, Dict, Any
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseConfig:
    """Database configuration class containing connection parameters."""
    
    # Database connection configurations matching the R Shiny app
    DATABASES = {
        'cdb': {
            'host': 'fdhssql01.stramit.com.au',
            'port': 3306,
            'database': 'cdb',
            'user': 'AccessTwo',
            'password': 'gxm7e25cnw',
            'description': 'CDB Database - Sales payments, address, and target data'
        },
        'app': {
            'host': 'fdhssql01.stramit.com.au',
            'port': 3306,
            'database': 'app',
            'user': 'AccessTwo',
            'password': 'gxm7e25cnw',
            'description': 'App Database - Leads and quotes data'
        },
        'reporting': {
            'host': 'CUSRVMYS01.stramit.com.au',
            'port': 3306,
            'database': 'ReportingDB',
            'user': 'jmtuser',
            'password': 'xd7=fcHV@*V2q7j!',
            'description': 'Reporting Database - Customer and order data'
        },
        'app_secondary': {
            'host': 'fdhssql01.stramit.com.au',
            'port': 3306,
            'database': 'app',
            'user': 'AccessTwo',
            'password': 'gxm7e25cnw',
            'description': 'App Database (Secondary) - Additional leads processing'
        }
    }
    
    # Connection timeout settings
    CONNECTION_TIMEOUT = 30
    AUTOCOMMIT = True
    
    @classmethod
    def get_config(cls, db_name: str) -> Dict[str, Any]:
        """
        Get database configuration for a specific database.
        
        Args:
            db_name: Name of the database ('cdb', 'app', 'reporting', 'app_secondary')
            
        Returns:
            Dictionary containing database connection parameters
            
        Raises:
            ValueError: If database name is not recognized
        """
        if db_name not in cls.DATABASES:
            raise ValueError(f"Unknown database: {db_name}. Available: {list(cls.DATABASES.keys())}")
        
        config = cls.DATABASES[db_name].copy()
        
        # Add connection settings
        config.update({
            'connection_timeout': cls.CONNECTION_TIMEOUT,
            'autocommit': cls.AUTOCOMMIT,
            'charset': 'utf8mb4',
            'use_unicode': True,
            'sql_mode': 'TRADITIONAL'
        })
        
        # Remove description from connection config
        config.pop('description', None)
        
        return config

class DatabaseManager:
    """Database connection manager with context management and error handling."""
    
    def __init__(self):
        self.connections = {}
        
    @contextmanager
    def get_connection(self, db_name: str):
        """
        Context manager for database connections.
        
        Args:
            db_name: Name of the database to connect to
            
        Yields:
            mysql.connector.connection: Database connection object
            
        Example:
            with db_manager.get_connection('cdb') as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM payments LIMIT 1")
                result = cursor.fetchall()
        """
        connection = None
        try:
            connection = self._create_connection(db_name)
            yield connection
        except Error as e:
            logger.error(f"Database error for {db_name}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error for {db_name}: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
                logger.debug(f"Connection to {db_name} closed")
    
    def _create_connection(self, db_name: str) -> mysql.connector.connection:
        """
        Create a new database connection.
        
        Args:
            db_name: Name of the database to connect to
            
        Returns:
            mysql.connector.connection: Database connection object
            
        Raises:
            mysql.connector.Error: If connection fails
        """
        config = DatabaseConfig.get_config(db_name)
        
        try:
            logger.info(f"Connecting to {db_name} database at {config['host']}:{config['port']}")
            connection = mysql.connector.connect(**config)
            
            if connection.is_connected():
                logger.info(f"Successfully connected to {db_name} database")
                return connection
            else:
                raise Error(f"Failed to connect to {db_name} database")
                
        except Error as e:
            logger.error(f"Failed to connect to {db_name}: {e}")
            raise
    
    def test_connections(self) -> Dict[str, bool]:
        """
        Test all database connections.
        
        Returns:
            Dictionary with database names as keys and connection status as values
        """
        results = {}
        
        for db_name in DatabaseConfig.DATABASES.keys():
            try:
                with self.get_connection(db_name) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    results[db_name] = True
                    logger.info(f"✓ {db_name} connection successful")
            except Exception as e:
                results[db_name] = False
                logger.error(f"✗ {db_name} connection failed: {e}")
        
        return results
    
    def get_database_info(self, db_name: str) -> Dict[str, Any]:
        """
        Get information about a specific database.
        
        Args:
            db_name: Name of the database
            
        Returns:
            Dictionary containing database information
        """
        try:
            with self.get_connection(db_name) as conn:
                cursor = conn.cursor()
                
                # Get database version
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                
                # Get current database name
                cursor.execute("SELECT DATABASE()")
                current_db = cursor.fetchone()[0]
                
                # Get table count
                cursor.execute("SHOW TABLES")
                table_count = len(cursor.fetchall())
                
                return {
                    'database': current_db,
                    'version': version,
                    'table_count': table_count,
                    'status': 'connected'
                }
        except Exception as e:
            return {
                'database': db_name,
                'error': str(e),
                'status': 'error'
            }

# Global database manager instance
db_manager = DatabaseManager()

def test_all_connections():
    """Test all database connections and print results."""
    print("Testing database connections...")
    print("=" * 50)
    
    results = db_manager.test_connections()
    
    for db_name, status in results.items():
        config = DatabaseConfig.DATABASES[db_name]
        status_symbol = "✓" if status else "✗"
        print(f"{status_symbol} {db_name.upper()}: {config['description']}")
        print(f"   Host: {config['host']}:{config['port']}")
        print(f"   Database: {config['database']}")
        print()
    
    success_count = sum(results.values())
    total_count = len(results)
    
    print(f"Connection Summary: {success_count}/{total_count} successful")
    
    if success_count == total_count:
        print("🎉 All database connections are working!")
    else:
        print("⚠️  Some database connections failed. Check network access and credentials.")
    
    return results

if __name__ == "__main__":
    # Test connections when run directly
    test_all_connections()
