#!/usr/bin/env python3
"""
Main execution script for Stramit Data Export Tool.

This script orchestrates the extraction and export of all data types from the
Stramit databases, replicating the functionality of the R Shiny dashboard.
"""

import argparse
import logging
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/stramit_export.log', mode='a')
    ]
)

logger = logging.getLogger(__name__)

# Import modules
from config.database import test_all_connections
from config.settings import Settings
from exporters.sales_exporter import export_sales_data
from exporters.customer_exporter import export_customer_data
from exporters.leads_exporter import export_leads_data
from exporters.target_exporter import export_target_data
from exporters.address_exporter import export_address_data, export_location_reference_data
from utils.date_utils import parse_date_string

class StramitDataExporter:
    """Main class for orchestrating data exports."""
    
    def __init__(self):
        self.available_export_types = [
            'sales', 'customer', 'leads', 'targets', 'addresses', 'reference'
        ]
        
    def parse_arguments(self) -> argparse.Namespace:
        """Parse command line arguments."""
        parser = argparse.ArgumentParser(
            description='Stramit Data Export Tool - Export data from Stramit databases to CSV files',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  # Export all data for the last 360 days (default)
  python main.py
  
  # Export data for a specific date range
  python main.py --start-date 2024-01-01 --end-date 2024-12-31
  
  # Export only specific data types
  python main.py --export-types sales,leads --start-date 2024-06-01
  
  # Export with custom output directory
  python main.py --output-dir /path/to/output --export-types customer,targets
  
  # Test database connections only
  python main.py --test-connections
            """
        )
        
        parser.add_argument(
            '--start-date',
            type=str,
            help='Start date for data extraction (YYYY-MM-DD format). Default: 360 days ago'
        )
        
        parser.add_argument(
            '--end-date',
            type=str,
            help='End date for data extraction (YYYY-MM-DD format). Default: today'
        )
        
        parser.add_argument(
            '--export-types',
            type=str,
            help=f'Comma-separated list of data types to export. Options: {", ".join(self.available_export_types)}. Default: all'
        )
        
        parser.add_argument(
            '--output-dir',
            type=str,
            help='Custom output directory for CSV files. Default: ./data'
        )
        
        parser.add_argument(
            '--categories',
            type=str,
            help='Filter sales by categories (comma-separated). Options: FDB Sheds,SB Sheds,FDB Patios,Lifestyle,NZ Sheds'
        )
        
        parser.add_argument(
            '--test-connections',
            action='store_true',
            help='Test database connections and exit'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose logging'
        )
        
        return parser.parse_args()
    
    def setup_logging(self, verbose: bool = False):
        """Setup logging configuration."""
        log_level = logging.DEBUG if verbose else logging.INFO
        
        # Update existing loggers
        logging.getLogger().setLevel(log_level)
        
        # Ensure logs directory exists
        Settings.LOGS_DIR.mkdir(exist_ok=True)
        
        # Add file handler with date
        log_file = Settings.LOGS_DIR / Settings.get_log_filename()
        file_handler = logging.FileHandler(log_file, mode='a')
        file_handler.setLevel(log_level)
        file_handler.setFormatter(logging.Formatter(Settings.LOG_FORMAT))
        
        # Add to root logger if not already present
        root_logger = logging.getLogger()
        if not any(isinstance(h, logging.FileHandler) for h in root_logger.handlers):
            root_logger.addHandler(file_handler)
    
    def parse_date_range(self, start_date_str: Optional[str], end_date_str: Optional[str]) -> tuple:
        """
        Parse and validate date range.
        
        Args:
            start_date_str: Start date string
            end_date_str: End date string
            
        Returns:
            Tuple of (start_date, end_date) as datetime objects
        """
        try:
            if start_date_str:
                start_date = parse_date_string(start_date_str)
            else:
                start_date = Settings.DEFAULT_START_DATE
                
            if end_date_str:
                end_date = parse_date_string(end_date_str)
            else:
                end_date = Settings.DEFAULT_END_DATE
                
            # Validate date range
            if start_date > end_date:
                raise ValueError("Start date must be before end date")
                
            logger.info(f"Date range: {start_date.date()} to {end_date.date()}")
            return start_date, end_date
            
        except Exception as e:
            logger.error(f"Invalid date range: {e}")
            sys.exit(1)
    
    def parse_export_types(self, export_types_str: Optional[str]) -> List[str]:
        """
        Parse and validate export types.
        
        Args:
            export_types_str: Comma-separated export types string
            
        Returns:
            List of valid export types
        """
        if not export_types_str:
            return self.available_export_types
        
        export_types = [t.strip().lower() for t in export_types_str.split(',')]
        
        # Validate export types
        invalid_types = [t for t in export_types if t not in self.available_export_types]
        if invalid_types:
            logger.error(f"Invalid export types: {invalid_types}")
            logger.error(f"Available types: {self.available_export_types}")
            sys.exit(1)
        
        logger.info(f"Export types: {export_types}")
        return export_types
    
    def export_sales_data_wrapper(self, start_date: datetime, end_date: datetime, output_dir: Path) -> bool:
        """Export sales data with error handling."""
        try:
            logger.info("Starting sales data export...")
            result = export_sales_data(start_date, end_date, output_dir)
            if result:
                logger.info(f"✓ Sales data exported successfully: {result}")
                return True
            else:
                logger.error("✗ Sales data export failed")
                return False
        except Exception as e:
            logger.error(f"✗ Sales data export error: {e}")
            return False
    
    def export_customer_data_wrapper(self, start_date: datetime, end_date: datetime, output_dir: Path) -> bool:
        """Export customer data with error handling."""
        try:
            logger.info("Starting customer data export...")
            result = export_customer_data(start_date, end_date, output_dir)
            if result:
                logger.info(f"✓ Customer data exported successfully: {result}")
                return True
            else:
                logger.error("✗ Customer data export failed")
                return False
        except Exception as e:
            logger.error(f"✗ Customer data export error: {e}")
            return False
    
    def export_leads_data_wrapper(self, start_date: datetime, end_date: datetime, output_dir: Path) -> bool:
        """Export leads data with error handling."""
        try:
            logger.info("Starting leads data export...")
            results = export_leads_data(start_date, end_date, output_dir)
            
            success_count = sum(1 for result in results.values() if result is not None)
            total_count = len(results)
            
            if success_count > 0:
                logger.info(f"✓ Leads data export: {success_count}/{total_count} files exported successfully")
                for data_type, result in results.items():
                    if result:
                        logger.info(f"  - {data_type}: {result}")
                    else:
                        logger.warning(f"  - {data_type}: export failed")
                return True
            else:
                logger.error("✗ All leads data exports failed")
                return False
        except Exception as e:
            logger.error(f"✗ Leads data export error: {e}")
            return False
    
    def export_targets_data_wrapper(self, start_date: datetime, end_date: datetime, output_dir: Path) -> bool:
        """Export targets data with error handling."""
        try:
            logger.info("Starting targets data export...")
            result = export_target_data(start_date, end_date, output_dir)
            if result:
                logger.info(f"✓ Targets data exported successfully: {result}")
                return True
            else:
                logger.error("✗ Targets data export failed")
                return False
        except Exception as e:
            logger.error(f"✗ Targets data export error: {e}")
            return False
    
    def export_addresses_data_wrapper(self, output_dir: Path) -> bool:
        """Export addresses data with error handling."""
        try:
            logger.info("Starting addresses data export...")
            result = export_address_data(output_dir)
            if result:
                logger.info(f"✓ Addresses data exported successfully: {result}")
                return True
            else:
                logger.error("✗ Addresses data export failed")
                return False
        except Exception as e:
            logger.error(f"✗ Addresses data export error: {e}")
            return False
    
    def export_reference_data_wrapper(self, output_dir: Path) -> bool:
        """Export reference data with error handling."""
        try:
            logger.info("Starting reference data export...")
            results = export_location_reference_data(output_dir)
            
            success_count = sum(1 for result in results.values() if result is not None)
            total_count = len(results)
            
            if success_count > 0:
                logger.info(f"✓ Reference data export: {success_count}/{total_count} files exported successfully")
                for data_type, result in results.items():
                    if result:
                        logger.info(f"  - {data_type}: {result}")
                    else:
                        logger.warning(f"  - {data_type}: export failed")
                return True
            else:
                logger.error("✗ All reference data exports failed")
                return False
        except Exception as e:
            logger.error(f"✗ Reference data export error: {e}")
            return False
    
    def run_exports(self, export_types: List[str], start_date: datetime, end_date: datetime, output_dir: Path) -> bool:
        """
        Run the specified data exports.
        
        Args:
            export_types: List of export types to run
            start_date: Start date for exports
            end_date: End date for exports
            output_dir: Output directory
            
        Returns:
            True if all exports succeeded, False otherwise
        """
        logger.info("=" * 60)
        logger.info("STARTING STRAMIT DATA EXPORT")
        logger.info("=" * 60)
        
        results = {}
        
        # Run exports based on specified types
        if 'sales' in export_types:
            results['sales'] = self.export_sales_data_wrapper(start_date, end_date, output_dir)
        
        if 'customer' in export_types:
            results['customer'] = self.export_customer_data_wrapper(start_date, end_date, output_dir)
        
        if 'leads' in export_types:
            results['leads'] = self.export_leads_data_wrapper(start_date, end_date, output_dir)
        
        if 'targets' in export_types:
            results['targets'] = self.export_targets_data_wrapper(start_date, end_date, output_dir)
        
        if 'addresses' in export_types:
            results['addresses'] = self.export_addresses_data_wrapper(output_dir)
        
        if 'reference' in export_types:
            results['reference'] = self.export_reference_data_wrapper(output_dir)
        
        # Summary
        logger.info("=" * 60)
        logger.info("EXPORT SUMMARY")
        logger.info("=" * 60)
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        for export_type, success in results.items():
            status = "✓ SUCCESS" if success else "✗ FAILED"
            logger.info(f"{export_type.upper()}: {status}")
        
        logger.info(f"\nOverall: {success_count}/{total_count} exports successful")
        
        if success_count == total_count:
            logger.info("🎉 All exports completed successfully!")
            return True
        else:
            logger.warning("⚠️  Some exports failed. Check logs for details.")
            return False
    
    def main(self):
        """Main execution function."""
        try:
            # Parse arguments
            args = self.parse_arguments()
            
            # Setup logging
            self.setup_logging(args.verbose)
            
            logger.info("Stramit Data Export Tool Starting...")
            
            # Test connections if requested
            if args.test_connections:
                logger.info("Testing database connections...")
                test_all_connections()
                return
            
            # Parse date range
            start_date, end_date = self.parse_date_range(args.start_date, args.end_date)
            
            # Parse export types
            export_types = self.parse_export_types(args.export_types)
            
            # Setup output directory
            output_dir = Path(args.output_dir) if args.output_dir else Settings.DATA_DIR
            output_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Output directory: {output_dir}")
            
            # Test database connections
            logger.info("Testing database connections...")
            connection_results = test_all_connections()
            failed_connections = [db for db, status in connection_results.items() if not status]
            
            if failed_connections:
                logger.error(f"Database connection failures: {failed_connections}")
                logger.error("Cannot proceed with exports. Please check database connectivity.")
                sys.exit(1)
            
            # Run exports
            success = self.run_exports(export_types, start_date, end_date, output_dir)
            
            if success:
                logger.info("Export process completed successfully!")
                sys.exit(0)
            else:
                logger.error("Export process completed with errors!")
                sys.exit(1)
                
        except KeyboardInterrupt:
            logger.info("Export process interrupted by user")
            sys.exit(1)
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            sys.exit(1)

if __name__ == "__main__":
    exporter = StramitDataExporter()
    exporter.main()
