# Changelog

All notable changes to the Stramit Data Export Tool will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-12-01

### Added
- Initial release of Stramit Data Export Tool
- Complete Python replication of R Shiny dashboard functionality
- Database connection management for 4 MySQL databases
- Sales data exporter with automatic category classification
- Customer data exporter with address validation
- Leads data exporter with distributor assignment and source categorization
- Target data exporter for budget and forecast analysis
- Address data exporter with Australian postcode integration
- Reference data exporter for geographic analysis
- Main orchestration script with comprehensive CLI interface
- Automatic timestamped CSV file generation
- Comprehensive error handling and logging
- Database connection testing functionality
- Flexible date range selection (default 360 days)
- Modular architecture for easy maintenance and extension

### Features
- **Sales Data Export**: 
  - Extracts payment records from CDB database
  - Automatic categorization (FDB Sheds, SB Sheds, FDB <PERSON>ios, Lifestyle, NZ Sheds)
  - Distributor network integration
  - Date range filtering with business logic

- **Customer Data Export**:
  - Customer order data from ReportingDB
  - Address and postcode validation
  - Geographic data integration
  - Customer contact information

- **Leads Data Export**:
  - Quote requests from App database
  - Lead source categorization (App vs Web)
  - Distributor assignment logic
  - Time-based aggregation for trend analysis
  - Multiple output formats (raw, aggregated, totals)

- **Target Data Export**:
  - Budget and forecast targets from CDB
  - Performance comparison capabilities
  - Country-specific filtering (AU focus)
  - Monthly and quarterly breakdowns

- **Address & Reference Data**:
  - Site addresses from CDB database
  - Australian postcodes (18,547+ records) with coordinates
  - Distributor locations (748+ records) for mapping
  - Geographic analysis support

- **Technical Features**:
  - Context-managed database connections
  - Comprehensive error handling and recovery
  - Detailed logging with multiple levels
  - Command-line interface with extensive options
  - Modular design for easy extension
  - Windows-optimized setup and usage

### Database Support
- **CDB Database** (fdhssql01.stramit.com.au): Sales, addresses, targets
- **App Database** (CUSRVMYS01.stramit.com.au): Leads and quotes
- **Reporting Database** (CUSRVMYS01.stramit.com.au): Customer orders
- **App Secondary Database**: Additional application data

### Documentation
- Comprehensive README with setup and usage instructions
- Windows-specific setup guide
- Troubleshooting documentation
- Command reference and examples
- Data category explanations
- Performance optimization tips

### Dependencies
- Python 3.8+ support
- MySQL connector libraries
- Pandas for data processing
- Standard library utilities for robust operation

## [Unreleased]

### Planned Features
- Excel export format support
- Enhanced data validation and cleaning
- Performance monitoring and metrics
- Automated scheduling integration
- Data quality reporting
- Enhanced geographic analysis features
- Dashboard integration capabilities

### Known Issues
- None currently identified

### Notes
- This tool replicates the complete functionality of the existing R Shiny dashboard
- All SQL queries and business logic have been faithfully translated from R to Python
- The tool maintains compatibility with existing data analysis workflows
- Performance is optimized for the typical 360-day export window used in business operations
