# Stramit Data Export Tool

A Python-based data extraction tool that replicates the functionality of the R Shiny dashboard for exporting sales, customer, leads, and target data from multiple MySQL databases.

## Project Structure

```
python_data_exporter/
├── config/
│   ├── __init__.py
│   ├── database.py          # Database connection configuration
│   └── settings.py          # Application settings
├── exporters/
│   ├── __init__.py
│   ├── sales_exporter.py    # Sales data extraction
│   ├── customer_exporter.py # Customer data extraction
│   ├── leads_exporter.py    # Leads data extraction
│   ├── target_exporter.py   # Target/budget data extraction
│   └── address_exporter.py  # Address and location data
├── utils/
│   ├── __init__.py
│   ├── helpers.py           # Utility functions
│   └── date_utils.py        # Date calculation helpers
├── data/                    # Output directory for CSV files
├── logs/                    # Log files
├── main.py                  # Main execution script
├── requirements.txt         # Python dependencies
└── README.md               # This file
```

## Features

### Data Export Capabilities

1. **Sales Data Export** (`sales_exporter.py`)
   - Extracts payment data from `cdb.payments` table
   - Categorizes sales into: FDB Sheds, SB Sheds, FDB Patios, Lifestyle, NZ Sheds
   - Filters by date range and payment status
   - Includes distributor network information

2. **Customer Data Export** (`customer_exporter.py`)
   - Extracts customer order data from `ReportingDB.Table_Order` and `Table_Customer`
   - Includes customer address, town, and postcode information
   - Links orders to distributors and categories

3. **Leads Data Export** (`leads_exporter.py`)
   - Extracts leads from `app.quotes` table
   - Aggregates by distributor, lead source, year, and month
   - Separates App leads (designer.ios, designer.android, designer.web) from Web leads (web.toast.au)
   - Provides time-series data for trend analysis

4. **Target Data Export** (`target_exporter.py`)
   - Extracts budget and forecast targets from `order_num_targets` table
   - Filters by country (AU) and date range
   - Used for performance comparison against actual sales

5. **Address Data Export** (`address_exporter.py`)
   - Extracts address data from `cdb.address` table
   - Integrates with Australian postcodes for mapping
   - Provides location data for geographic analysis

## Database Connections

The tool connects to 4 MySQL databases:

1. **CDB Database** (`fdhssql01.stramit.com.au:3306/cdb`)
   - Sales payments data
   - Address information
   - Target/budget data

2. **App Database** (`fdhssql01.stramit.com.au:3306/app`)
   - Leads and quotes data

3. **Reporting Database** (`CUSRVMYS01.stramit.com.au:3306/ReportingDB`)
   - Customer and order data

4. **App Database (Secondary)** (`fdhssql01.stramit.com.au:3306/app`)
   - Additional leads processing

## Installation

1. **Clone or download the project**
   ```bash
   cd python_data_exporter
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure database connections**
   - Edit `config/database.py` to update database credentials if needed
   - Ensure network access to the MySQL servers

## Usage

### Basic Usage

```bash
# Export all data for the last 360 days
python main.py

# Export data for a specific date range
python main.py --start-date 2024-01-01 --end-date 2024-12-31

# Export only specific data types
python main.py --export-types sales,leads --start-date 2024-06-01
```

### Command Line Options

- `--start-date`: Start date for data extraction (YYYY-MM-DD format)
- `--end-date`: End date for data extraction (YYYY-MM-DD format)
- `--export-types`: Comma-separated list of data types to export (sales,customer,leads,targets,addresses)
- `--output-dir`: Custom output directory for CSV files
- `--categories`: Filter sales by categories (FDB Sheds,SB Sheds,FDB Patios,Lifestyle,NZ Sheds)

### Output Files

All CSV files are saved in the `data/` directory with timestamps:

- `sales_data_YYYYMMDD_HHMMSS.csv` - Sales/payments data
- `customer_data_YYYYMMDD_HHMMSS.csv` - Customer orders data
- `leads_raw_data_YYYYMMDD_HHMMSS.csv` - Raw leads data
- `leads_app_total_YYYYMMDD_HHMMSS.csv` - App leads aggregated by distributor
- `leads_web_total_YYYYMMDD_HHMMSS.csv` - Web leads aggregated by distributor
- `leads_distribution_YYYYMMDD_HHMMSS.csv` - Time-series leads data
- `target_data_YYYYMMDD_HHMMSS.csv` - Budget and forecast targets
- `address_data_YYYYMMDD_HHMMSS.csv` - Address and location data

## Data Categories

### Sales Categories
- **FDB Sheds**: Fair Dinkum Builds sheds (default category)
- **SB Sheds**: Steel Buildings sheds (NetworkID = "STR")
- **FDB Patios**: Fair Dinkum Builds patios (NetworkID = "FDBP")
- **Lifestyle**: Lifestyle products (NetworkID = "STO")
- **NZ Sheds**: New Zealand sheds (OrderIdCode starts with "Z")

### Lead Sources
- **App Sources**: designer.ios, designer.android, designer.web
- **Web Sources**: web.toast.au

## Requirements

- Python 3.8+
- MySQL database access
- Network connectivity to Stramit servers
- Required Python packages (see requirements.txt)

## Troubleshooting

### Database Connection Issues
- Verify network access to MySQL servers
- Check database credentials in `config/database.py`
- Ensure MySQL servers are accessible from your network

### Data Export Issues
- Check date ranges are valid
- Verify sufficient disk space in output directory
- Review log files in `logs/` directory for detailed error messages

## Support

This tool replicates the data extraction functionality from the R Shiny dashboard. For questions about data structure or business logic, refer to the original R code in `Backend.R` and `Frontend.R`.
