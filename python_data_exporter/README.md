# Stramit Data Export Tool

A comprehensive Python tool for extracting and exporting data from Stramit databases to CSV files, replicating the functionality of the R Shiny dashboard with enhanced automation capabilities.

## 🎯 Overview

This tool connects to multiple MySQL databases used by Stramit and exports various types of business intelligence data for analysis, reporting, and dashboard creation. It provides the same data extraction capabilities as the R Shiny application but in a standalone, automated format.

### Data Export Types

1. **Sales Data**: Payment records with automatic category classification (FDB Sheds, SB Sheds, FDB Patios, Lifestyle, NZ Sheds)
2. **Customer Data**: Order and customer information with addresses and postcode validation
3. **Leads Data**: Quote requests with distributor assignments, lead source categorization, and time-based aggregation
4. **Target Data**: Budget and forecast targets for performance comparison and KPI tracking
5. **Address Data**: Location data integrated with Australian postcodes for mapping functionality
6. **Reference Data**: Australian postcodes and distributor locations for geospatial analysis

## Project Structure

```
python_data_exporter/
├── config/
│   ├── __init__.py
│   ├── database.py          # Database connection configuration
│   └── settings.py          # Application settings
├── exporters/
│   ├── __init__.py
│   ├── sales_exporter.py    # Sales data extraction
│   ├── customer_exporter.py # Customer data extraction
│   ├── leads_exporter.py    # Leads data extraction
│   ├── target_exporter.py   # Target/budget data extraction
│   └── address_exporter.py  # Address and location data
├── utils/
│   ├── __init__.py
│   ├── helpers.py           # Utility functions
│   └── date_utils.py        # Date calculation helpers
├── data/                    # Output directory for CSV files
├── logs/                    # Log files
├── main.py                  # Main execution script
├── requirements.txt         # Python dependencies
└── README.md               # This file
```

## Features

### Data Export Capabilities

1. **Sales Data Export** (`sales_exporter.py`)
   - Extracts payment data from `cdb.payments` table
   - Categorizes sales into: FDB Sheds, SB Sheds, FDB Patios, Lifestyle, NZ Sheds
   - Filters by date range and payment status
   - Includes distributor network information

2. **Customer Data Export** (`customer_exporter.py`)
   - Extracts customer order data from `ReportingDB.Table_Order` and `Table_Customer`
   - Includes customer address, town, and postcode information
   - Links orders to distributors and categories

3. **Leads Data Export** (`leads_exporter.py`)
   - Extracts leads from `app.quotes` table
   - Aggregates by distributor, lead source, year, and month
   - Separates App leads (designer.ios, designer.android, designer.web) from Web leads (web.toast.au)
   - Provides time-series data for trend analysis

4. **Target Data Export** (`target_exporter.py`)
   - Extracts budget and forecast targets from `order_num_targets` table
   - Filters by country (AU) and date range
   - Used for performance comparison against actual sales

5. **Address Data Export** (`address_exporter.py`)
   - Extracts address data from `cdb.address` table
   - Integrates with Australian postcodes for mapping
   - Provides location data for geographic analysis

## 🗄️ Database Connections

The tool connects to 4 MySQL databases with automatic connection management and error handling:

### Primary Databases
- **CDB Database** (`fdhssql01.stramit.com.au:3306`)
  - **Purpose**: Core business data including payments, addresses, distributorships, and targets
  - **User**: AccessTwo / **Password**: gxm7e25cnw
  - **Tables**: payments, address, distributorship, order_num_targets

- **App Database** (`CUSRVMYS01.stramit.com.au:3306`)
  - **Purpose**: Application data including quote requests and leads
  - **User**: jmtuser / **Password**: xd7=fcHV@*V2q7j!
  - **Tables**: quotes (leads data)

- **Reporting Database** (`CUSRVMYS01.stramit.com.au:3306`)
  - **Purpose**: Reporting and analytics data
  - **User**: jmtuser / **Password**: xd7=fcHV@*V2q7j!
  - **Tables**: Table_Order, Table_Customer

- **App Secondary Database** (`CUSRVMYS01.stramit.com.au:3306`)
  - **Purpose**: Additional application-related data
  - **User**: jmtuser / **Password**: xd7=fcHV@*V2q7j!
  - **Tables**: Secondary app tables

## 🚀 Installation & Setup

### Prerequisites
- **Python 3.8 or higher** (recommended: Python 3.9+)
- **Network access** to Stramit database servers
- **MySQL client libraries** (installed automatically with requirements)

### Installation Steps

1. **Clone or download the project**:
   ```bash
   cd python_data_exporter
   ```

2. **Create a virtual environment** (recommended):
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate

   # macOS/Linux
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **Install required packages**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Verify installation**:
   ```bash
   python main.py --test-connections
   ```

## 📊 Usage Guide

### Quick Start

**Export all data for the default period (last 360 days)**:
```bash
python main.py
```

This will create timestamped CSV files in the `data/` directory with all available data types.

### Advanced Usage Examples

**Export specific data types**:
```bash
# Export only sales and customer data
python main.py --export-types sales,customer

# Export only leads data
python main.py --export-types leads
```

**Custom date ranges**:
```bash
# Export data for 2024
python main.py --start-date 2024-01-01 --end-date 2024-12-31

# Export last 30 days of sales data
python main.py --export-types sales --start-date 2024-11-01
```

**Custom output directory**:
```bash
# Export to specific directory
python main.py --output-dir /path/to/custom/output

# Export to network drive (Windows)
python main.py --output-dir "\\server\share\stramit_exports"
```

**Diagnostic and testing**:
```bash
# Test all database connections
python main.py --test-connections

# Verbose logging for troubleshooting
python main.py --verbose --export-types sales
```

### Complete Command Reference

| Option | Description | Example |
|--------|-------------|---------|
| `--start-date` | Start date (YYYY-MM-DD) | `--start-date 2024-01-01` |
| `--end-date` | End date (YYYY-MM-DD) | `--end-date 2024-12-31` |
| `--export-types` | Data types to export | `--export-types sales,leads,customer` |
| `--output-dir` | Custom output directory | `--output-dir /exports` |
| `--test-connections` | Test database connections only | `--test-connections` |
| `--verbose` | Enable detailed logging | `--verbose` |

### Available Export Types
- `sales` - Sales/payment data with category classification
- `customer` - Customer and order data with addresses
- `leads` - Lead/quote data with distributor assignments
- `targets` - Budget and forecast targets
- `addresses` - Address data for mapping
- `reference` - Australian postcodes and distributor locations

## 📁 Output Files

The tool generates timestamped CSV files with consistent naming conventions:

### Sales Data Files
- `sales_data_20240101_20241231_20241201_143022.csv` - Main sales data with categories

### Customer Data Files
- `customer_data_20240101_20241231_20241201_143022.csv` - Customer orders with addresses

### Leads Data Files
- `leads_raw_data_20240101_20241231_20241201_143022.csv` - Raw leads with full details
- `leads_distribution_20240101_20241231_20241201_143022.csv` - Aggregated leads by distributor
- `leads_app_total_20240101_20241231_20241201_143022.csv` - App leads summary
- `leads_web_total_20240101_20241231_20241201_143022.csv` - Web leads summary

### Target Data Files
- `target_data_20240101_20241231_20241201_143022.csv` - Budget and forecast targets

### Address and Reference Files
- `address_data_20241201_143022.csv` - Site addresses from CDB
- `australian_postcodes_20241201_143022.csv` - Australian postcode coordinates
- `distributor_locations_20241201_143022.csv` - Distributor location data

## Data Categories

### Sales Categories
- **FDB Sheds**: Fair Dinkum Builds sheds (default category)
- **SB Sheds**: Steel Buildings sheds (NetworkID = "STR")
- **FDB Patios**: Fair Dinkum Builds patios (NetworkID = "FDBP")
- **Lifestyle**: Lifestyle products (NetworkID = "STO")
- **NZ Sheds**: New Zealand sheds (OrderIdCode starts with "Z")

### Lead Sources
- **App Sources**: designer.ios, designer.android, designer.web
- **Web Sources**: web.toast.au

## 🏷️ Data Categories & Classifications

### Sales Categories (Automatic Classification)
- **FDB Sheds** - Flat Deck Building sheds
- **SB Sheds** - Steel Building sheds
- **FDB Patios** - Flat Deck Building patios
- **Lifestyle** - Lifestyle products
- **NZ Sheds** - New Zealand shed products

### Lead Sources (Automatic Categorization)
- **App Leads** - Leads generated through mobile/web applications
- **Web Leads** - Leads from website forms and online sources

### Geographic Data
- **Australian Postcodes** - 18,547+ postcode records with precise coordinates
- **Distributor Locations** - 748+ distributor locations with business details

## 🔧 Troubleshooting

### Database Connection Issues

**Problem**: Connection timeouts or "Can't connect to MySQL server"
```bash
# Test specific connections
python main.py --test-connections --verbose
```
**Solutions**:
- Verify network connectivity to `fdhssql01.stramit.com.au` and `CUSRVMYS01.stramit.com.au`
- Check firewall settings (MySQL port 3306)
- Confirm VPN connection if required

**Problem**: "Access denied for user"
**Solutions**:
- Verify database credentials in `config/database.py`
- Check if passwords have changed
- Confirm user permissions on database servers

### Data Export Issues

**Problem**: "No data found for specified date range"
**Solutions**:
- Verify date range is reasonable (try last 30 days)
- Check if databases contain data for the specified period
- Use `--verbose` flag to see detailed query information

**Problem**: Export files are empty or very small
**Solutions**:
- Check database connectivity during export
- Verify date range includes business days
- Review log files for specific error messages

### Performance Issues

**Problem**: Exports are very slow
**Solutions**:
- Reduce date range for initial testing
- Export specific data types instead of all types
- Check network latency to database servers
- Run during off-peak hours

## 📋 Logging & Monitoring

### Log Files
Detailed logs are automatically created in the `logs/` directory:
- **Filename**: `stramit_export_YYYYMMDD.log`
- **Content**: Database connections, query execution, export progress, errors
- **Rotation**: New log file created daily

### Log Levels
- **INFO**: Normal operation progress and results
- **WARNING**: Non-critical issues (e.g., empty result sets)
- **ERROR**: Critical errors that prevent exports
- **DEBUG**: Detailed technical information (use `--verbose`)

### Monitoring Export Success
```bash
# Check recent log for errors
tail -50 logs/stramit_export_$(date +%Y%m%d).log

# Search for specific issues
grep "ERROR" logs/stramit_export_*.log
grep "SUCCESS" logs/stramit_export_*.log
```

## 📋 Requirements

- **Python 3.8+** (recommended: Python 3.9+)
- **MySQL database access** to Stramit servers
- **Network connectivity** to Stramit database servers
- **Required Python packages** (see requirements.txt)

## 🤝 Support & Maintenance

### Getting Help
1. Check the troubleshooting section above
2. Review log files for specific error messages
3. Test database connections with `--test-connections`
4. Use `--verbose` flag for detailed debugging information

### Regular Maintenance
- Monitor log files for recurring issues
- Update database credentials if they change
- Test exports regularly to ensure data quality
- Archive old CSV files to manage disk space

### Version Information
- **Current Version**: 1.0.0
- **Python Compatibility**: 3.8+
- **Database Support**: MySQL 5.7+
- Verify network access to MySQL servers
- Check database credentials in `config/database.py`
- Ensure MySQL servers are accessible from your network

### Data Export Issues
- Check date ranges are valid
- Verify sufficient disk space in output directory
- Review log files in `logs/` directory for detailed error messages

## Support

This tool replicates the data extraction functionality from the R Shiny dashboard. For questions about data structure or business logic, refer to the original R code in `Backend.R` and `Frontend.R`.
