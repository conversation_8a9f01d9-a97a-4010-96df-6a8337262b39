# Stramit Data Export Tool - Setup Guide

This guide provides step-by-step instructions for setting up and running the Stramit Data Export Tool on Windows systems.

## 🖥️ Windows Setup Instructions

### Step 1: Install Python

1. **Download Python 3.9 or higher** from [python.org](https://www.python.org/downloads/)
2. **Run the installer** and make sure to:
   - ✅ Check "Add Python to PATH"
   - ✅ Check "Install pip"
   - Choose "Install Now"

3. **Verify installation** by opening Command Prompt and running:
   ```cmd
   python --version
   pip --version
   ```

### Step 2: Download the Project

1. **Download** the `python_data_exporter` folder to your computer
2. **Place it** in a location like `C:\Stramit\python_data_exporter`

### Step 3: Install Dependencies

1. **Open Command Prompt as Administrator**
2. **Navigate** to the project directory:
   ```cmd
   cd C:\Stramit\python_data_exporter
   ```

3. **Install required packages**:
   ```cmd
   pip install -r requirements.txt
   ```

### Step 4: Test Database Connections

1. **Test connections** to ensure everything is working:
   ```cmd
   python main.py --test-connections
   ```

2. **Expected output**: You should see connection test results for all 4 databases

### Step 5: Run Your First Export

1. **Export all data** for the last 360 days:
   ```cmd
   python main.py
   ```

2. **Check results**: Look in the `data\` folder for CSV files

## 🚀 Quick Start Commands

### Export All Data (Default)
```cmd
python main.py
```

### Export Specific Data Types
```cmd
# Sales data only
python main.py --export-types sales

# Sales and customer data
python main.py --export-types sales,customer

# Leads data only
python main.py --export-types leads
```

### Custom Date Ranges
```cmd
# Export 2024 data
python main.py --start-date 2024-01-01 --end-date 2024-12-31

# Export last 30 days
python main.py --start-date 2024-11-01 --end-date 2024-11-30
```

### Custom Output Location
```cmd
# Export to specific folder
python main.py --output-dir "C:\Stramit\Exports"

# Export to network drive
python main.py --output-dir "\\server\share\stramit_data"
```

## 📁 Understanding Output Files

After running exports, you'll find CSV files in the `data\` folder:

### Sales Files
- `sales_data_20240101_20241231_20241201_143022.csv`
  - Contains all sales/payment data with categories
  - Includes distributor information and amounts

### Customer Files
- `customer_data_20240101_20241231_20241201_143022.csv`
  - Customer order information
  - Includes addresses and contact details

### Leads Files
- `leads_raw_data_20240101_20241231_20241201_143022.csv` - Detailed leads data
- `leads_app_total_20240101_20241231_20241201_143022.csv` - App leads summary
- `leads_web_total_20240101_20241231_20241201_143022.csv` - Web leads summary

### Reference Files
- `australian_postcodes_20241201_143022.csv` - Postcode coordinates
- `distributor_locations_20241201_143022.csv` - Distributor locations

## 🔧 Troubleshooting Common Issues

### "Python is not recognized"
**Problem**: Windows can't find Python
**Solution**: 
1. Reinstall Python with "Add to PATH" checked
2. Or manually add Python to your PATH environment variable

### "No module named 'mysql'"
**Problem**: MySQL connector not installed
**Solution**:
```cmd
pip install mysql-connector-python
```

### "Connection failed" errors
**Problem**: Can't connect to databases
**Solutions**:
1. Check your internet connection
2. Verify VPN connection if required
3. Check firewall settings
4. Contact IT support if needed

### "Permission denied" when saving files
**Problem**: Can't write to output directory
**Solutions**:
1. Run Command Prompt as Administrator
2. Choose a different output directory with `--output-dir`
3. Check folder permissions

### Empty or small CSV files
**Problem**: No data in exports
**Solutions**:
1. Check date range (try last 30 days)
2. Verify database contains data for that period
3. Use `--verbose` flag to see detailed information

## 📅 Scheduling Automated Exports

### Using Windows Task Scheduler

1. **Open Task Scheduler** (search in Start menu)
2. **Create Basic Task**
3. **Set trigger** (daily, weekly, etc.)
4. **Set action** to start a program:
   - **Program**: `python`
   - **Arguments**: `main.py --export-types sales,customer`
   - **Start in**: `C:\Stramit\python_data_exporter`

### Using Batch Files

Create a batch file `run_export.bat`:
```batch
@echo off
cd /d "C:\Stramit\python_data_exporter"
python main.py --export-types sales,customer,leads
pause
```

Double-click the batch file to run exports easily.

## 📊 Working with Exported Data

### Opening CSV Files
- **Excel**: Double-click CSV files to open in Excel
- **Power BI**: Import CSV files for dashboard creation
- **Python/R**: Use pandas or data.table to analyze data

### Data Analysis Tips
1. **Sales Data**: Analyze by category, distributor, and time period
2. **Customer Data**: Geographic analysis using postcodes
3. **Leads Data**: Compare App vs Web lead sources
4. **Target Data**: Performance analysis against budgets

## 🔄 Regular Maintenance

### Weekly Tasks
- Run exports to get latest data
- Check log files for any errors
- Archive old CSV files

### Monthly Tasks
- Review export performance
- Update date ranges for historical analysis
- Clean up old log files

### As Needed
- Update database credentials if they change
- Install Python package updates
- Test connections after network changes

## 📞 Getting Help

### Log Files
Check `logs\stramit_export_YYYYMMDD.log` for detailed information about:
- What the tool is doing
- Any errors that occur
- Export results and file locations

### Verbose Mode
Use `--verbose` flag for detailed debugging:
```cmd
python main.py --verbose --export-types sales
```

### Testing Connections
Always test connections first when troubleshooting:
```cmd
python main.py --test-connections --verbose
```

## 🎯 Best Practices

### For Regular Use
1. **Start small**: Test with one data type first
2. **Use reasonable date ranges**: Don't export years of data at once
3. **Check logs**: Always review logs after exports
4. **Organize outputs**: Use custom output directories for different purposes

### For Performance
1. **Export during off-peak hours**: Better database performance
2. **Use specific data types**: Don't export everything if you only need sales data
3. **Limit date ranges**: Shorter ranges export faster
4. **Monitor disk space**: CSV files can be large

### For Reliability
1. **Test connections regularly**: Ensure database access is working
2. **Keep backups**: Archive important export files
3. **Document your process**: Note which exports you run and when
4. **Update regularly**: Keep Python and packages up to date
