# Stramit Data Export Tool - Python Dependencies
# 
# This file contains all the Python packages required to run the Stramit Data Export Tool.
# Install with: pip install -r requirements.txt

# Database connectivity
mysql-connector-python>=8.0.33
PyMySQL>=1.0.3

# Data processing and analysis
pandas>=2.0.0
numpy>=1.24.0

# Date and time utilities
python-dateutil>=2.8.2

# Logging and configuration
python-dotenv>=1.0.0

# File and path handling (built into Python 3.4+)
# pathlib - included in standard library

# XML processing (built into Python)
# xml.etree.ElementTree - included in standard library

# Optional: Enhanced data processing
openpyxl>=3.1.0  # For Excel file support if needed in future
xlsxwriter>=3.1.0  # For creating Excel files if needed

# Optional: Progress bars and CLI enhancements
tqdm>=4.65.0  # For progress bars during large exports
click>=8.1.0  # Alternative CLI framework if argparse becomes insufficient

# Development and testing dependencies (optional)
pytest>=7.3.0
pytest-cov>=4.1.0
black>=23.3.0  # Code formatting
flake8>=6.0.0  # Code linting

# Documentation (optional)
sphinx>=6.2.0
sphinx-rtd-theme>=1.2.0
