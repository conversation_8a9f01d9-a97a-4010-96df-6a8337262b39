"""
Sales Data Exporter for Stramit Data Export Tool.

This module extracts sales/payments data from the CDB database,
replicating the main sales query from Backend.R
"""

import pandas as pd
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

from ..config.database import db_manager
from ..config.settings import Settings
from ..utils.helpers import categorize_sales_data, extract_distributor_id, save_dataframe_to_csv, log_dataframe_info
from ..utils.date_utils import format_date_for_sql

logger = logging.getLogger(__name__)

class SalesExporter:
    """Handles extraction and export of sales/payments data."""
    
    def __init__(self):
        self.db_name = 'cdb'
        
    def build_sales_query(self, start_date: datetime, end_date: datetime) -> str:
        """
        Build the sales data SQL query.
        Replicates the complex query from Backend.R (xstring1 + xstring2 + xstring3)
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            
        Returns:
            SQL query string
        """
        start_date_str = format_date_for_sql(start_date)
        end_date_str = format_date_for_sql(end_date)
        
        query = f"""
        SELECT 
            d.NetworkID, 
            p.*,
            SUBSTRING_INDEX(p.OrderIdCode, '\\\\', 1) AS dist_id
        FROM cdb.distributorship d, (
            SELECT 
                SUBSTRING_INDEX(OrderIdCode, '\\\\', 1) AS dist_id, 
                p1.* 
            FROM cdb.payments p1 
            WHERE 
                (PaymentTo = 'FeeAGS' OR PaymentTo = 'FeeMB') 
                AND StatusDate BETWEEN '{start_date_str} 00:00:00' AND '{end_date_str} 23:59:59'
                AND Ammount > 0 
                AND NOT EXISTS (
                    SELECT * FROM cdb.payments p2 
                    WHERE p1.OrderIdCode = p2.OrderIdCode 
                    AND p2.PaymentTo = 'FeeCancel'
                ) 
                AND (
                    'SOLAU' = (
                        SELECT Country FROM cdb.address 
                        WHERE SUBSTRING_INDEX(OrderIdCode, '\\\\', 1) = AddressID 
                        AND AddressType = 'Site'
                    ) 
                    OR 'AU' = (
                        SELECT Country FROM cdb.address 
                        WHERE SUBSTRING_INDEX(OrderIdCode, '\\\\', 1) = AddressID 
                        AND AddressType = 'Site'
                    ) 
                    OR 'NZ' = (
                        SELECT Country FROM cdb.address 
                        WHERE SUBSTRING_INDEX(OrderIdCode, '\\\\', 1) = AddressID 
                        AND AddressType = 'Site'
                    )
                )
        ) AS p 
        WHERE d.distributorshipid = p.dist_id
        ORDER BY p.StatusDate DESC
        """
        
        return query
    
    def extract_sales_data(self, start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """
        Extract sales data from the database.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            
        Returns:
            DataFrame with sales data or None if extraction fails
        """
        try:
            logger.info(f"Extracting sales data from {start_date.date()} to {end_date.date()}")
            
            query = self.build_sales_query(start_date, end_date)
            
            with db_manager.get_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)
                
            if df.empty:
                logger.warning("No sales data found for the specified date range")
                return df
            
            # Process the data
            df = self._process_sales_data(df)
            
            log_dataframe_info(df, "Sales Data")
            logger.info(f"Successfully extracted {len(df)} sales records")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to extract sales data: {e}")
            return None
    
    def _process_sales_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process and clean the sales data.
        
        Args:
            df: Raw sales DataFrame
            
        Returns:
            Processed DataFrame
        """
        # Convert date columns
        date_columns = ['StatusDate', 'CreatedDate']
        for col in date_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # Extract distributor ID if not already present
        if 'dist_id' not in df.columns and 'OrderIdCode' in df.columns:
            df['dist_id'] = df['OrderIdCode'].apply(extract_distributor_id)
        
        # Categorize sales data
        df = categorize_sales_data(df)
        
        # Clean and format numeric columns
        numeric_columns = ['Ammount', 'Amount']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        # Add calculated fields
        df['Year'] = df['StatusDate'].dt.year
        df['Month'] = df['StatusDate'].dt.month
        df['Quarter'] = df['StatusDate'].dt.quarter
        
        # Sort by date
        df = df.sort_values('StatusDate', ascending=False)
        
        return df
    
    def export_sales_data(self, start_date: datetime, end_date: datetime, 
                         output_dir: Optional[Path] = None) -> Optional[Path]:
        """
        Export sales data to CSV file.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            output_dir: Output directory (uses default if None)
            
        Returns:
            Path to exported CSV file or None if export fails
        """
        try:
            # Extract data
            df = self.extract_sales_data(start_date, end_date)
            if df is None or df.empty:
                logger.warning("No sales data to export")
                return None
            
            # Determine output path
            if output_dir is None:
                output_dir = Settings.DATA_DIR
            
            filename = Settings.get_output_filename(
                'sales_data', 
                start_date.strftime('%Y%m%d'), 
                end_date.strftime('%Y%m%d')
            )
            output_path = Path(output_dir) / filename
            
            # Save to CSV
            if save_dataframe_to_csv(df, output_path):
                logger.info(f"Sales data exported to: {output_path}")
                return output_path
            else:
                return None
                
        except Exception as e:
            logger.error(f"Failed to export sales data: {e}")
            return None
    
    def get_sales_summary(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Get summary statistics for sales data.
        
        Args:
            start_date: Start date for analysis
            end_date: End date for analysis
            
        Returns:
            Dictionary with summary statistics
        """
        try:
            df = self.extract_sales_data(start_date, end_date)
            if df is None or df.empty:
                return {}
            
            # Calculate summary statistics
            summary = {
                'total_records': len(df),
                'date_range': {
                    'start': start_date.strftime('%Y-%m-%d'),
                    'end': end_date.strftime('%Y-%m-%d')
                },
                'total_amount': df['Ammount'].sum() if 'Ammount' in df.columns else 0,
                'average_amount': df['Ammount'].mean() if 'Ammount' in df.columns else 0,
                'categories': df['Category'].value_counts().to_dict() if 'Category' in df.columns else {},
                'distributors': df['dist_id'].nunique() if 'dist_id' in df.columns else 0,
                'networks': df['NetworkID'].value_counts().to_dict() if 'NetworkID' in df.columns else {},
                'monthly_totals': df.groupby(['Year', 'Month'])['Ammount'].sum().to_dict() if 'Ammount' in df.columns else {}
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate sales summary: {e}")
            return {}

# Convenience function for direct usage
def export_sales_data(start_date: datetime, end_date: datetime, 
                     output_dir: Optional[Path] = None) -> Optional[Path]:
    """
    Export sales data to CSV file.
    
    Args:
        start_date: Start date for data extraction
        end_date: End date for data extraction
        output_dir: Output directory (uses default if None)
        
    Returns:
        Path to exported CSV file or None if export fails
    """
    exporter = SalesExporter()
    return exporter.export_sales_data(start_date, end_date, output_dir)
