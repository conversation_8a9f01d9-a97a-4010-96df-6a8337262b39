"""
Leads Data Exporter for Stramit Data Export Tool.

This module extracts leads data from the app.quotes database,
replicating the leads queries from Backend.R
"""

import pandas as pd
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from pathlib import Path

from ..config.database import db_manager
from ..config.settings import Settings
from ..utils.helpers import categorize_lead_sources, parse_xml_field, validate_postcode, save_dataframe_to_csv, log_dataframe_info
from ..utils.date_utils import format_date_for_sql

logger = logging.getLogger(__name__)

class LeadsExporter:
    """Handles extraction and export of leads data."""
    
    def __init__(self):
        self.db_name = 'app'
        
    def build_leads_raw_query(self, start_date: datetime, end_date: datetime) -> str:
        """
        Build the raw leads data SQL query.
        Replicates the leads query from Backend.R (xstring_lead1 + xstring_lead2 + xstring_lead3)
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            
        Returns:
            SQL query string
        """
        start_date_str = format_date_for_sql(start_date)
        end_date_str = format_date_for_sql(end_date)
        
        query = f"""
        SELECT
            distid,
            xml_program_name,
            request_time,
            status,
            COUNT(*) as cnt,
            SUBSTR(xml, INSTR(xml, '<Street>')+8, INSTR(xml, '</Street>')-8 - INSTR(xml, '<Street>')) AS Street,
            SUBSTR(xml, INSTR(xml, '<Town>')+6, INSTR(xml, '</Town>')-6 - INSTR(xml, '<Town>')) AS Town,
            SUBSTR(xml, INSTR(xml, '<Postcode>')+10, INSTR(xml, '</Postcode>')-10 - INSTR(xml, '<Postcode>')) AS PostCode,
            xml
        FROM quotes
        WHERE request_time BETWEEN '{start_date_str} 00:00:00' AND '{end_date_str} 23:59:59'
        GROUP BY distid, xml_program_name, status, Street, Town, PostCode
        ORDER BY request_time DESC
        """
        
        return query
    
    def build_leads_aggregated_query(self, start_date: datetime, end_date: datetime) -> str:
        """
        Build the aggregated leads query for time-series analysis.
        Replicates the query_base logic from Backend.R
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            
        Returns:
            SQL query string
        """
        start_date_str = format_date_for_sql(start_date)
        end_date_str = format_date_for_sql(end_date)
        
        query = f"""
        SELECT
            CASE WHEN (distid IS NULL OR distid = 'NONE') THEN 'Unassigned' ELSE distid END AS distid,
            xml_program_name AS LeadSource,
            YEAR(request_time) AS `Year`,
            MONTH(request_time) AS `Month`,
            COUNT(*) AS cnt
        FROM quotes
        WHERE request_time BETWEEN '{start_date_str} 00:00:00' AND '{end_date_str} 23:59:59'
        GROUP BY distid, xml_program_name, YEAR(request_time), MONTH(request_time)
        ORDER BY `Year` DESC, `Month` DESC, distid, xml_program_name
        """
        
        return query
    
    def extract_leads_raw_data(self, start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """
        Extract raw leads data from the database.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            
        Returns:
            DataFrame with raw leads data or None if extraction fails
        """
        try:
            logger.info(f"Extracting raw leads data from {start_date.date()} to {end_date.date()}")
            
            query = self.build_leads_raw_query(start_date, end_date)
            
            with db_manager.get_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)
                
            if df.empty:
                logger.warning("No raw leads data found for the specified date range")
                return df
            
            # Process the data
            df = self._process_leads_raw_data(df)
            
            log_dataframe_info(df, "Raw Leads Data")
            logger.info(f"Successfully extracted {len(df)} raw leads records")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to extract raw leads data: {e}")
            return None
    
    def extract_leads_aggregated_data(self, start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """
        Extract aggregated leads data for time-series analysis.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            
        Returns:
            DataFrame with aggregated leads data or None if extraction fails
        """
        try:
            logger.info(f"Extracting aggregated leads data from {start_date.date()} to {end_date.date()}")
            
            query = self.build_leads_aggregated_query(start_date, end_date)
            
            with db_manager.get_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)
                
            if df.empty:
                logger.warning("No aggregated leads data found for the specified date range")
                return df
            
            # Process the data
            df = self._process_leads_aggregated_data(df)
            
            log_dataframe_info(df, "Aggregated Leads Data")
            logger.info(f"Successfully extracted {len(df)} aggregated leads records")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to extract aggregated leads data: {e}")
            return None
    
    def _process_leads_raw_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process and clean the raw leads data.
        
        Args:
            df: Raw leads DataFrame
            
        Returns:
            Processed DataFrame
        """
        # Convert date columns
        if 'request_time' in df.columns:
            df['request_time'] = pd.to_datetime(df['request_time'], errors='coerce')
        
        # Clean address fields
        if 'Town' in df.columns:
            df['Town'] = df['Town'].astype(str).str.upper().str.strip()
        
        # Validate and clean postcodes
        if 'PostCode' in df.columns:
            df['PostCode'] = df['PostCode'].astype(str).str.strip()
            # Filter out invalid postcodes (less than 3 characters)
            df = df[df['PostCode'].apply(lambda x: validate_postcode(x, min_length=3))]
        
        # Handle distributor ID
        if 'distid' in df.columns:
            df['distid'] = df['distid'].fillna('Unassigned')
            df.loc[df['distid'] == 'NONE', 'distid'] = 'Unassigned'
        
        # Categorize lead sources
        df = categorize_lead_sources(df)
        
        # Add calculated fields
        if 'request_time' in df.columns:
            df['Year'] = df['request_time'].dt.year
            df['Month'] = df['request_time'].dt.month
            df['Quarter'] = df['request_time'].dt.quarter
        
        return df
    
    def _process_leads_aggregated_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process and clean the aggregated leads data.
        
        Args:
            df: Aggregated leads DataFrame
            
        Returns:
            Processed DataFrame
        """
        # Categorize lead sources
        df = categorize_lead_sources(df)
        
        # Add quarter calculation
        if 'Month' in df.columns:
            df['Quarter'] = ((df['Month'] - 1) // 3) + 1
        
        return df
    
    def export_leads_data(self, start_date: datetime, end_date: datetime, 
                         output_dir: Optional[Path] = None) -> Dict[str, Optional[Path]]:
        """
        Export all leads data to CSV files.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            output_dir: Output directory (uses default if None)
            
        Returns:
            Dictionary with export results for each data type
        """
        results = {}
        
        if output_dir is None:
            output_dir = Settings.DATA_DIR
        
        # Export raw leads data
        try:
            df_raw = self.extract_leads_raw_data(start_date, end_date)
            if df_raw is not None and not df_raw.empty:
                filename = Settings.get_output_filename(
                    'leads_raw_data', 
                    start_date.strftime('%Y%m%d'), 
                    end_date.strftime('%Y%m%d')
                )
                output_path = Path(output_dir) / filename
                
                if save_dataframe_to_csv(df_raw, output_path):
                    results['raw'] = output_path
                    logger.info(f"Raw leads data exported to: {output_path}")
                else:
                    results['raw'] = None
            else:
                results['raw'] = None
        except Exception as e:
            logger.error(f"Failed to export raw leads data: {e}")
            results['raw'] = None
        
        # Export aggregated leads data
        try:
            df_agg = self.extract_leads_aggregated_data(start_date, end_date)
            if df_agg is not None and not df_agg.empty:
                filename = Settings.get_output_filename(
                    'leads_distribution', 
                    start_date.strftime('%Y%m%d'), 
                    end_date.strftime('%Y%m%d')
                )
                output_path = Path(output_dir) / filename
                
                if save_dataframe_to_csv(df_agg, output_path):
                    results['aggregated'] = output_path
                    logger.info(f"Aggregated leads data exported to: {output_path}")
                else:
                    results['aggregated'] = None
                
                # Export App and Web totals separately
                results.update(self._export_leads_by_category(df_agg, start_date, end_date, output_dir))
            else:
                results['aggregated'] = None
        except Exception as e:
            logger.error(f"Failed to export aggregated leads data: {e}")
            results['aggregated'] = None
        
        return results
    
    def _export_leads_by_category(self, df: pd.DataFrame, start_date: datetime, end_date: datetime, 
                                 output_dir: Path) -> Dict[str, Optional[Path]]:
        """
        Export leads data separated by App and Web categories.
        
        Args:
            df: Aggregated leads DataFrame
            start_date: Start date for filename
            end_date: End date for filename
            output_dir: Output directory
            
        Returns:
            Dictionary with export results for App and Web categories
        """
        results = {}
        
        try:
            # App leads total
            app_leads = df[df['LeadCategory'] == 'App'].groupby('distid')['cnt'].sum().reset_index()
            app_leads.columns = ['Distributor', 'AppLeadsTotal']
            
            filename = Settings.get_output_filename(
                'leads_app_total', 
                start_date.strftime('%Y%m%d'), 
                end_date.strftime('%Y%m%d')
            )
            output_path = Path(output_dir) / filename
            
            if save_dataframe_to_csv(app_leads, output_path):
                results['app_total'] = output_path
                logger.info(f"App leads total exported to: {output_path}")
            else:
                results['app_total'] = None
                
        except Exception as e:
            logger.error(f"Failed to export App leads total: {e}")
            results['app_total'] = None
        
        try:
            # Web leads total
            web_leads = df[df['LeadCategory'] == 'Web'].groupby('distid')['cnt'].sum().reset_index()
            web_leads.columns = ['Distributor', 'WebLeadsTotal']
            
            filename = Settings.get_output_filename(
                'leads_web_total', 
                start_date.strftime('%Y%m%d'), 
                end_date.strftime('%Y%m%d')
            )
            output_path = Path(output_dir) / filename
            
            if save_dataframe_to_csv(web_leads, output_path):
                results['web_total'] = output_path
                logger.info(f"Web leads total exported to: {output_path}")
            else:
                results['web_total'] = None
                
        except Exception as e:
            logger.error(f"Failed to export Web leads total: {e}")
            results['web_total'] = None
        
        return results

# Convenience function for direct usage
def export_leads_data(start_date: datetime, end_date: datetime, 
                     output_dir: Optional[Path] = None) -> Dict[str, Optional[Path]]:
    """
    Export leads data to CSV files.
    
    Args:
        start_date: Start date for data extraction
        end_date: End date for data extraction
        output_dir: Output directory (uses default if None)
        
    Returns:
        Dictionary with export results for each data type
    """
    exporter = LeadsExporter()
    return exporter.export_leads_data(start_date, end_date, output_dir)
