"""
Target Data Exporter for Stramit Data Export Tool.

This module extracts budget and forecast target data from the CDB database,
replicating the target data query from Backend.R
"""

import pandas as pd
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

from ..config.database import db_manager
from ..config.settings import Settings
from ..utils.helpers import save_dataframe_to_csv, log_dataframe_info
from ..utils.date_utils import format_date_for_sql

logger = logging.getLogger(__name__)

class TargetExporter:
    """Handles extraction and export of target/budget data."""
    
    def __init__(self):
        self.db_name = 'cdb'
        
    def build_target_query(self, start_date: datetime, end_date: datetime) -> str:
        """
        Build the target data SQL query.
        Replicates the target data extraction from Backend.R
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            
        Returns:
            SQL query string
        """
        start_date_str = format_date_for_sql(start_date)
        end_date_str = format_date_for_sql(end_date)
        
        query = f"""
        SELECT 
            *
        FROM order_num_targets 
        WHERE Country = 'AU' 
        AND date BETWEEN '{start_date_str}' AND '{end_date_str}'
        ORDER BY date DESC, year DESC, month DESC
        """
        
        return query
    
    def extract_target_data(self, start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """
        Extract target data from the database.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            
        Returns:
            DataFrame with target data or None if extraction fails
        """
        try:
            logger.info(f"Extracting target data from {start_date.date()} to {end_date.date()}")
            
            query = self.build_target_query(start_date, end_date)
            
            with db_manager.get_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)
                
            if df.empty:
                logger.warning("No target data found for the specified date range")
                return df
            
            # Process the data
            df = self._process_target_data(df)
            
            log_dataframe_info(df, "Target Data")
            logger.info(f"Successfully extracted {len(df)} target records")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to extract target data: {e}")
            return None
    
    def _process_target_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process and clean the target data.
        
        Args:
            df: Raw target DataFrame
            
        Returns:
            Processed DataFrame
        """
        # Convert date columns
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'], errors='coerce')
        
        # Clean and format numeric columns
        numeric_columns = ['budget', 'forecast', 'target', 'actual']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        # Ensure year and month are integers
        if 'year' in df.columns:
            df['year'] = pd.to_numeric(df['year'], errors='coerce').fillna(0).astype(int)
        if 'month' in df.columns:
            df['month'] = pd.to_numeric(df['month'], errors='coerce').fillna(0).astype(int)
        
        # Add quarter calculation
        if 'month' in df.columns:
            df['quarter'] = ((df['month'] - 1) // 3) + 1
        
        # Sort by date
        df = df.sort_values(['year', 'month'], ascending=[False, False])
        
        return df
    
    def export_target_data(self, start_date: datetime, end_date: datetime, 
                          output_dir: Optional[Path] = None) -> Optional[Path]:
        """
        Export target data to CSV file.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            output_dir: Output directory (uses default if None)
            
        Returns:
            Path to exported CSV file or None if export fails
        """
        try:
            # Extract data
            df = self.extract_target_data(start_date, end_date)
            if df is None or df.empty:
                logger.warning("No target data to export")
                return None
            
            # Determine output path
            if output_dir is None:
                output_dir = Settings.DATA_DIR
            
            filename = Settings.get_output_filename(
                'target_data', 
                start_date.strftime('%Y%m%d'), 
                end_date.strftime('%Y%m%d')
            )
            output_path = Path(output_dir) / filename
            
            # Save to CSV
            if save_dataframe_to_csv(df, output_path):
                logger.info(f"Target data exported to: {output_path}")
                return output_path
            else:
                return None
                
        except Exception as e:
            logger.error(f"Failed to export target data: {e}")
            return None
    
    def get_target_summary(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Get summary statistics for target data.
        
        Args:
            start_date: Start date for analysis
            end_date: End date for analysis
            
        Returns:
            Dictionary with summary statistics
        """
        try:
            df = self.extract_target_data(start_date, end_date)
            if df is None or df.empty:
                return {}
            
            # Calculate summary statistics
            summary = {
                'total_records': len(df),
                'date_range': {
                    'start': start_date.strftime('%Y-%m-%d'),
                    'end': end_date.strftime('%Y-%m-%d')
                },
                'total_budget': df['budget'].sum() if 'budget' in df.columns else 0,
                'total_forecast': df['forecast'].sum() if 'forecast' in df.columns else 0,
                'total_target': df['target'].sum() if 'target' in df.columns else 0,
                'total_actual': df['actual'].sum() if 'actual' in df.columns else 0,
                'countries': df['Country'].value_counts().to_dict() if 'Country' in df.columns else {},
                'years': df['year'].value_counts().to_dict() if 'year' in df.columns else {},
                'monthly_budgets': df.groupby(['year', 'month'])['budget'].sum().to_dict() if 'budget' in df.columns else {},
                'monthly_forecasts': df.groupby(['year', 'month'])['forecast'].sum().to_dict() if 'forecast' in df.columns else {}
            }
            
            # Calculate performance metrics if both budget and actual exist
            if 'budget' in df.columns and 'actual' in df.columns:
                total_budget = df['budget'].sum()
                total_actual = df['actual'].sum()
                if total_budget > 0:
                    summary['budget_performance'] = (total_actual / total_budget) * 100
                else:
                    summary['budget_performance'] = 0
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate target summary: {e}")
            return {}

# Convenience function for direct usage
def export_target_data(start_date: datetime, end_date: datetime, 
                      output_dir: Optional[Path] = None) -> Optional[Path]:
    """
    Export target data to CSV file.
    
    Args:
        start_date: Start date for data extraction
        end_date: End date for data extraction
        output_dir: Output directory (uses default if None)
        
    Returns:
        Path to exported CSV file or None if export fails
    """
    exporter = TargetExporter()
    return exporter.export_target_data(start_date, end_date, output_dir)
