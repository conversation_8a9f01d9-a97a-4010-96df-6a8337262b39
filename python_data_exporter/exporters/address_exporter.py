"""
Address and Location Data Exporter for Stramit Data Export Tool.

This module extracts address data from the CDB database and integrates with
Australian postcodes for mapping functionality, replicating the address logic from Backend.R
"""

import pandas as pd
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

from ..config.database import db_manager
from ..config.settings import Settings
from ..utils.helpers import save_dataframe_to_csv, log_dataframe_info

logger = logging.getLogger(__name__)

class AddressExporter:
    """Handles extraction and export of address and location data."""
    
    def __init__(self):
        self.db_name = 'cdb'
        
    def build_address_query(self) -> str:
        """
        Build the address data SQL query.
        Replicates the address query from Backend.R (sheddata_address)
        
        Returns:
            SQL query string
        """
        query = """
        SELECT * 
        FROM cdb.address 
        WHERE AddressType = 'Site'
        ORDER BY AddressID
        """
        
        return query
    
    def extract_address_data(self) -> Optional[pd.DataFrame]:
        """
        Extract address data from the database.
        
        Returns:
            DataFrame with address data or None if extraction fails
        """
        try:
            logger.info("Extracting address data from CDB database")
            
            query = self.build_address_query()
            
            with db_manager.get_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)
                
            if df.empty:
                logger.warning("No address data found")
                return df
            
            # Process the data
            df = self._process_address_data(df)
            
            log_dataframe_info(df, "Address Data")
            logger.info(f"Successfully extracted {len(df)} address records")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to extract address data: {e}")
            return None
    
    def load_australian_postcodes(self) -> Optional[pd.DataFrame]:
        """
        Load Australian postcodes data from CSV file.
        
        Returns:
            DataFrame with Australian postcodes or None if loading fails
        """
        try:
            # Look for the Australian postcodes CSV in the parent directory
            postcodes_path = Path(__file__).parent.parent.parent / "australian_postcodes.csv"
            
            if not postcodes_path.exists():
                logger.error(f"Australian postcodes file not found: {postcodes_path}")
                return None
            
            logger.info(f"Loading Australian postcodes from: {postcodes_path}")
            
            # Load only the required columns
            df = pd.read_csv(postcodes_path, usecols=[
                'postcode', 'locality', 'state', 'Lat_precise', 'Long_precise'
            ])
            
            logger.info(f"Loaded {len(df)} Australian postcode records")
            return df
            
        except Exception as e:
            logger.error(f"Failed to load Australian postcodes: {e}")
            return None
    
    def load_distributor_locations(self) -> Optional[pd.DataFrame]:
        """
        Load distributor locations data from CSV file.
        
        Returns:
            DataFrame with distributor locations or None if loading fails
        """
        try:
            # Look for the distributor locations CSV in the parent directory
            distributors_path = Path(__file__).parent.parent.parent / "with-intersections-sos-2021.csv"
            
            if not distributors_path.exists():
                logger.error(f"Distributor locations file not found: {distributors_path}")
                return None
            
            logger.info(f"Loading distributor locations from: {distributors_path}")
            
            df = pd.read_csv(distributors_path)
            
            logger.info(f"Loaded {len(df)} distributor location records")
            return df
            
        except Exception as e:
            logger.error(f"Failed to load distributor locations: {e}")
            return None
    
    def _process_address_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process and clean the address data.
        
        Args:
            df: Raw address DataFrame
            
        Returns:
            Processed DataFrame
        """
        # Clean address fields
        text_columns = ['Address1', 'Address2', 'Town', 'State']
        for col in text_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()
        
        # Clean postcode
        if 'PostCode' in df.columns:
            df['PostCode'] = df['PostCode'].astype(str).str.strip()
        
        # Convert coordinates if they exist
        coord_columns = ['Latitude', 'Longitude', 'Lat', 'Long']
        for col in coord_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df
    
    def export_address_data(self, output_dir: Optional[Path] = None) -> Optional[Path]:
        """
        Export address data to CSV file.
        
        Args:
            output_dir: Output directory (uses default if None)
            
        Returns:
            Path to exported CSV file or None if export fails
        """
        try:
            # Extract data
            df = self.extract_address_data()
            if df is None or df.empty:
                logger.warning("No address data to export")
                return None
            
            # Determine output path
            if output_dir is None:
                output_dir = Settings.DATA_DIR
            
            filename = Settings.get_output_filename('address_data')
            output_path = Path(output_dir) / filename
            
            # Save to CSV
            if save_dataframe_to_csv(df, output_path):
                logger.info(f"Address data exported to: {output_path}")
                return output_path
            else:
                return None
                
        except Exception as e:
            logger.error(f"Failed to export address data: {e}")
            return None
    
    def export_location_reference_data(self, output_dir: Optional[Path] = None) -> Dict[str, Optional[Path]]:
        """
        Export location reference data (Australian postcodes and distributor locations).
        
        Args:
            output_dir: Output directory (uses default if None)
            
        Returns:
            Dictionary with export results for each reference data type
        """
        results = {}
        
        if output_dir is None:
            output_dir = Settings.DATA_DIR
        
        # Export Australian postcodes
        try:
            postcodes_df = self.load_australian_postcodes()
            if postcodes_df is not None and not postcodes_df.empty:
                filename = Settings.get_output_filename('australian_postcodes')
                output_path = Path(output_dir) / filename
                
                if save_dataframe_to_csv(postcodes_df, output_path):
                    results['postcodes'] = output_path
                    logger.info(f"Australian postcodes exported to: {output_path}")
                else:
                    results['postcodes'] = None
            else:
                results['postcodes'] = None
        except Exception as e:
            logger.error(f"Failed to export Australian postcodes: {e}")
            results['postcodes'] = None
        
        # Export distributor locations
        try:
            distributors_df = self.load_distributor_locations()
            if distributors_df is not None and not distributors_df.empty:
                filename = Settings.get_output_filename('distributor_locations')
                output_path = Path(output_dir) / filename
                
                if save_dataframe_to_csv(distributors_df, output_path):
                    results['distributors'] = output_path
                    logger.info(f"Distributor locations exported to: {output_path}")
                else:
                    results['distributors'] = None
            else:
                results['distributors'] = None
        except Exception as e:
            logger.error(f"Failed to export distributor locations: {e}")
            results['distributors'] = None
        
        return results
    
    def get_address_summary(self) -> Dict[str, Any]:
        """
        Get summary statistics for address data.
        
        Returns:
            Dictionary with summary statistics
        """
        try:
            df = self.extract_address_data()
            if df is None or df.empty:
                return {}
            
            # Calculate summary statistics
            summary = {
                'total_records': len(df),
                'countries': df['Country'].value_counts().to_dict() if 'Country' in df.columns else {},
                'states': df['State'].value_counts().to_dict() if 'State' in df.columns else {},
                'address_types': df['AddressType'].value_counts().to_dict() if 'AddressType' in df.columns else {},
                'unique_postcodes': df['PostCode'].nunique() if 'PostCode' in df.columns else 0,
                'records_with_coordinates': 0
            }
            
            # Count records with coordinates
            if 'Latitude' in df.columns and 'Longitude' in df.columns:
                summary['records_with_coordinates'] = len(df.dropna(subset=['Latitude', 'Longitude']))
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate address summary: {e}")
            return {}

# Convenience functions for direct usage
def export_address_data(output_dir: Optional[Path] = None) -> Optional[Path]:
    """
    Export address data to CSV file.
    
    Args:
        output_dir: Output directory (uses default if None)
        
    Returns:
        Path to exported CSV file or None if export fails
    """
    exporter = AddressExporter()
    return exporter.export_address_data(output_dir)

def export_location_reference_data(output_dir: Optional[Path] = None) -> Dict[str, Optional[Path]]:
    """
    Export location reference data to CSV files.
    
    Args:
        output_dir: Output directory (uses default if None)
        
    Returns:
        Dictionary with export results for each reference data type
    """
    exporter = AddressExporter()
    return exporter.export_location_reference_data(output_dir)
