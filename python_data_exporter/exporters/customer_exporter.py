"""
Customer Data Exporter for Stramit Data Export Tool.

This module extracts customer order data from the ReportingDB database,
replicating the customer data query from Backend.R
"""

import pandas as pd
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

from ..config.database import db_manager
from ..config.settings import Settings
from ..utils.helpers import categorize_customer_data, clean_address_data, save_dataframe_to_csv, log_dataframe_info
from ..utils.date_utils import format_date_for_sql

logger = logging.getLogger(__name__)

class CustomerExporter:
    """Handles extraction and export of customer order data."""
    
    def __init__(self):
        self.db_name = 'reporting'
        
    def build_customer_query(self, start_date: datetime, end_date: datetime) -> str:
        """
        Build the customer data SQL query.
        Replicates the customer query from Backend.R (xstring_cust1 + xstring_cust2 + xstring_cust3)
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            
        Returns:
            SQL query string
        """
        start_date_str = format_date_for_sql(start_date)
        end_date_str = format_date_for_sql(end_date)
        
        query = f"""
        SELECT
            o.OrderSentToSupplier,
            o.DistributerCode,
            o.TotalPrice,
            o.Customer_Code,
            o.OrderID,
            o.OrderDate,
            c.Address,
            c.Town,
            c.PostCode,
            c.CustomerName,
            c.Phone,
            c.Email
        FROM
            Table_Order o
        JOIN Table_Customer c ON c.Code = o.Customer_Code
        WHERE o.OrderSentToSupplier BETWEEN '{start_date_str}' AND '{end_date_str}'
        ORDER BY o.OrderSentToSupplier DESC
        """
        
        return query
    
    def extract_customer_data(self, start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """
        Extract customer data from the database.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            
        Returns:
            DataFrame with customer data or None if extraction fails
        """
        try:
            logger.info(f"Extracting customer data from {start_date.date()} to {end_date.date()}")
            
            query = self.build_customer_query(start_date, end_date)
            
            with db_manager.get_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)
                
            if df.empty:
                logger.warning("No customer data found for the specified date range")
                return df
            
            # Process the data
            df = self._process_customer_data(df)
            
            log_dataframe_info(df, "Customer Data")
            logger.info(f"Successfully extracted {len(df)} customer records")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to extract customer data: {e}")
            return None
    
    def _process_customer_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process and clean the customer data.
        
        Args:
            df: Raw customer DataFrame
            
        Returns:
            Processed DataFrame
        """
        # Convert date columns
        date_columns = ['OrderSentToSupplier', 'OrderDate']
        for col in date_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # Clean address data (removes newlines, formats Town)
        df = clean_address_data(df, 'Address')
        
        # Categorize customer data based on DistributerCode
        df = categorize_customer_data(df)
        
        # Clean and format numeric columns
        if 'TotalPrice' in df.columns:
            df['TotalPrice'] = pd.to_numeric(df['TotalPrice'], errors='coerce').fillna(0)
        
        # Clean postcode data
        if 'PostCode' in df.columns:
            df['PostCode'] = df['PostCode'].astype(str).str.strip()
            # Remove invalid postcodes (less than 3 characters or non-numeric)
            df = df[df['PostCode'].str.len() >= 3]
            df = df[df['PostCode'].str.isdigit()]
        
        # Add calculated fields
        if 'OrderSentToSupplier' in df.columns:
            df['Year'] = df['OrderSentToSupplier'].dt.year
            df['Month'] = df['OrderSentToSupplier'].dt.month
            df['Quarter'] = df['OrderSentToSupplier'].dt.quarter
        
        # Sort by date
        df = df.sort_values('OrderSentToSupplier', ascending=False)
        
        return df
    
    def export_customer_data(self, start_date: datetime, end_date: datetime, 
                           output_dir: Optional[Path] = None) -> Optional[Path]:
        """
        Export customer data to CSV file.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            output_dir: Output directory (uses default if None)
            
        Returns:
            Path to exported CSV file or None if export fails
        """
        try:
            # Extract data
            df = self.extract_customer_data(start_date, end_date)
            if df is None or df.empty:
                logger.warning("No customer data to export")
                return None
            
            # Determine output path
            if output_dir is None:
                output_dir = Settings.DATA_DIR
            
            filename = Settings.get_output_filename(
                'customer_data', 
                start_date.strftime('%Y%m%d'), 
                end_date.strftime('%Y%m%d')
            )
            output_path = Path(output_dir) / filename
            
            # Save to CSV
            if save_dataframe_to_csv(df, output_path):
                logger.info(f"Customer data exported to: {output_path}")
                return output_path
            else:
                return None
                
        except Exception as e:
            logger.error(f"Failed to export customer data: {e}")
            return None
    
    def get_customer_summary(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        Get summary statistics for customer data.
        
        Args:
            start_date: Start date for analysis
            end_date: End date for analysis
            
        Returns:
            Dictionary with summary statistics
        """
        try:
            df = self.extract_customer_data(start_date, end_date)
            if df is None or df.empty:
                return {}
            
            # Calculate summary statistics
            summary = {
                'total_records': len(df),
                'date_range': {
                    'start': start_date.strftime('%Y-%m-%d'),
                    'end': end_date.strftime('%Y-%m-%d')
                },
                'total_value': df['TotalPrice'].sum() if 'TotalPrice' in df.columns else 0,
                'average_order_value': df['TotalPrice'].mean() if 'TotalPrice' in df.columns else 0,
                'unique_customers': df['Customer_Code'].nunique() if 'Customer_Code' in df.columns else 0,
                'categories': df['Category'].value_counts().to_dict() if 'Category' in df.columns else {},
                'distributors': df['DistributerCode'].value_counts().to_dict() if 'DistributerCode' in df.columns else {},
                'states': df['Town'].value_counts().head(10).to_dict() if 'Town' in df.columns else {},
                'postcodes': df['PostCode'].value_counts().head(20).to_dict() if 'PostCode' in df.columns else {},
                'monthly_totals': df.groupby(['Year', 'Month'])['TotalPrice'].sum().to_dict() if 'TotalPrice' in df.columns else {}
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate customer summary: {e}")
            return {}

# Convenience function for direct usage
def export_customer_data(start_date: datetime, end_date: datetime, 
                        output_dir: Optional[Path] = None) -> Optional[Path]:
    """
    Export customer data to CSV file.
    
    Args:
        start_date: Start date for data extraction
        end_date: End date for data extraction
        output_dir: Output directory (uses default if None)
        
    Returns:
        Path to exported CSV file or None if export fails
    """
    exporter = CustomerExporter()
    return exporter.export_customer_data(start_date, end_date, output_dir)
