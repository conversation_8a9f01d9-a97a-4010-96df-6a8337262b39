# Docker Compose for Python + gRPC + Redis + React Stack
version: '3.8'

services:
  # Frontend - React with Leaflet Maps
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_GRPC_WEB_URL=http://localhost:8080
      - REACT_APP_API_URL=http://localhost:8080
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - envoy-proxy
    networks:
      - stramit-network

  # Envoy Proxy - gRPC-Web Bridge
  envoy-proxy:
    image: envoyproxy/envoy:v1.24-latest
    ports:
      - "8080:8080"
      - "9901:9901"  # Admin interface
    volumes:
      - ./envoy.yaml:/etc/envoy/envoy.yaml
    depends_on:
      - grpc-server
    networks:
      - stramit-network

  # Python gRPC Server
  grpc-server:
    build:
      context: ./backend
      dockerfile: Dockerfile.grpc
    ports:
      - "50051:50051"
    environment:
      - REDIS_URL=redis://redis:6379
      - DB_CDB_HOST=mysql-cdb
      - DB_CDB_USER=AccessTwo
      - DB_CDB_PASSWORD=gxm7e25cnw
      - DB_APP_HOST=mysql-app
      - DB_APP_USER=jmtuser
      - DB_APP_PASSWORD=xd7=fcHV@*V2q7j!
      - PYTHONPATH=/app
    volumes:
      - ./python_data_exporter:/app
      - ./exports:/app/exports
    depends_on:
      - redis
      - mysql-cdb
      - mysql-app
    networks:
      - stramit-network

  # Export Service (Background Jobs)
  export-service:
    build:
      context: ./backend
      dockerfile: Dockerfile.export
    environment:
      - REDIS_URL=redis://redis:6379
      - DB_CDB_HOST=mysql-cdb
      - DB_CDB_USER=AccessTwo
      - DB_CDB_PASSWORD=gxm7e25cnw
      - DB_APP_HOST=mysql-app
      - DB_APP_USER=jmtuser
      - DB_APP_PASSWORD=xd7=fcHV@*V2q7j!
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./python_data_exporter:/app
      - ./exports:/app/exports
    depends_on:
      - redis
      - mysql-cdb
      - mysql-app
    networks:
      - stramit-network

  # Redis - Caching and Job Queue
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - stramit-network

  # Redis Commander - Redis GUI
  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - stramit-network

  # MySQL CDB Database
  mysql-cdb:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=cdb
      - MYSQL_USER=AccessTwo
      - MYSQL_PASSWORD=gxm7e25cnw
    volumes:
      - mysql_cdb_data:/var/lib/mysql
      - ./database/cdb_init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - stramit-network

  # MySQL App Database
  mysql-app:
    image: mysql:8.0
    ports:
      - "3307:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=app
      - MYSQL_USER=jmtuser
      - MYSQL_PASSWORD=xd7=fcHV@*V2q7j!
    volumes:
      - mysql_app_data:/var/lib/mysql
      - ./database/app_init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - stramit-network

  # Nginx - Static File Server
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./exports:/usr/share/nginx/html/exports
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - envoy-proxy
    networks:
      - stramit-network

  # Prometheus - Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - stramit-network

  # Grafana - Monitoring Dashboard
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - stramit-network

  # Celery Worker - Background Task Processing
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.celery
    environment:
      - REDIS_URL=redis://redis:6379
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DB_CDB_HOST=mysql-cdb
      - DB_APP_HOST=mysql-app
    volumes:
      - ./python_data_exporter:/app
      - ./exports:/app/exports
    depends_on:
      - redis
      - mysql-cdb
      - mysql-app
    command: celery -A export_tasks worker --loglevel=info
    networks:
      - stramit-network

  # Celery Beat - Scheduled Tasks
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.celery
    environment:
      - REDIS_URL=redis://redis:6379
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./python_data_exporter:/app
    depends_on:
      - redis
    command: celery -A export_tasks beat --loglevel=info
    networks:
      - stramit-network

  # Flower - Celery Monitoring
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile.celery
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
    command: celery -A export_tasks flower
    networks:
      - stramit-network

volumes:
  redis_data:
  mysql_cdb_data:
  mysql_app_data:
  prometheus_data:
  grafana_data:

networks:
  stramit-network:
    driver: bridge
