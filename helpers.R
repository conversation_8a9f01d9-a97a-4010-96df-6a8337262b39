library(lubridate)

weekday_list <- c("Monday", "Tuesday", "Wednesday", "Thursday", "Friday")

get_working_day <- function(year, month, start_date=NULL) {
  number_days <- days_in_month(as.Date(paste(year, month, "1", sep="-")))

  wkday_cnt <- 0

  if (is.null(start_date)) {
    start_date <- 1
  }

  for (day in start_date:number_days) {
    if (weekdays(as.Date(paste(year, month, day, sep="-"))) %in% weekday_list) {
      wkday_cnt <- wkday_cnt + 1
    }
  }

  return (wkday_cnt)
}

get_working_day2 <- function(date) {
  year <- year(date)
  month <- month(date)
  day <- day(date)

  result <- get_working_day(year, month, day)

  return (result)
}
