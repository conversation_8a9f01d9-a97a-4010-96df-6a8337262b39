# finqty=10000
# finbudget=1500000 # are we using this?




backend <- shinyServer(function(input,output,session){
  #### Database connection ####
  mysqlconnection <- dbConnect(RMySQL::MySQL(),
                               dbname='cdb',
                               host='fdhssql01.stramit.com.au',
                               port=3306,
                               user='AccessTwo',
                               password='gxm7e25cnw')

  mysqlconnection1 <- dbConnect(RMySQL::MySQL(),
                               dbname='app',
                               host='fdhssql01.stramit.com.au',
                               port=3306,
                               user='AccessTwo',
                               password='gxm7e25cnw')

  mysqlconnection2 <- dbConnect(RMySQL::MySQL(),
                               dbname='ReportingDB',
                               host='CUSRVMYS01.stramit.com.au',
                               port=3306,
                               user='jmtuser',
                               password='xd7=fcHV@*V2q7j!')

  mysqlconnection3 <- dbConnect(RMySQL::MySQL(),
                                dbname='app',
                                host='fdhssql01.stramit.com.au',
                                port=3306,
                                user='AccessTwo',
                                password='gxm7e25cnw')



  session$onSessionEnded(function(){
    dbDisconnect(mysqlconnection)
    dbDisconnect(mysqlconnection2)
    dbDisconnect(mysqlconnection3)
  })

  #### Login Modal tab
  # tab_login$server(input, output)
  distributor_df <- read.csv("with-intersections-sos-2021.csv")
  au_postcodes_df <- read.csv("australian_postcodes.csv") %>% select(postcode, locality, state, Lat_precise, Long_precise)

  #### Sale value of this month: used for gauge plot, daily forecast and budget and number of sheds plot ####
  thisMonthSaleValue <- reactive({
    todayDate <- input$daterange1[2]
    this_month_start <- floor_date(todayDate, "month")

    fetchdata <- myFilterData() %>% filter(as.Date(StatusDate) >= this_month_start & as.Date(StatusDate) <= todayDate)

    fetchdata
  })

  lastyear_mtd <- reactive({
    todayDate <- input$daterange1[2]

    startDate <- as.Date(paste(year(todayDate)-1, month(todayDate),"1", sep="-"))
    endDate <- as.Date(paste(year(todayDate)-1, month(todayDate), day(todayDate), sep="-"))

    fetchdata <- myFilterData() %>% filter(as.Date(StatusDate) >= startDate & as.Date(StatusDate) <= endDate)

    fetchdata %>% filter(PaymentTo == "FeeAGS") %>% nrow()
  })


  #### Pulling the 1 year of sales data upon loading or refresh
  xstring1 <-"SELECT d.NetworkID, p.* FROM cdb.distributorship d, (

            SELECT SUBSTRING_INDEX(OrderIdCode, '\\\\', 1) AS dist_id, p1.* FROM cdb.payments p1 WHERE (PaymentTo = 'FeeAGS' OR PaymentTo = 'FeeMB') AND StatusDate BETWEEN '"
  xstring2 <-" 00:00:000' AND '"
  xstring3 <- " 23:59:59.999' AND Ammount > 0 AND NOT EXISTS (SELECT * FROM cdb.payments p2 WHERE p1.OrderIdCode = p2.OrderIdCode AND p2.PaymentTo = 'FeeCancel' ) AND ('SOLAU' = ( SELECT Country FROM cdb.address WHERE SUBSTRING_INDEX(OrderIdCode, '\\\\', 1) = AddressID AND AddressType = 'Site' ) OR 'AU' = ( SELECT Country FROM cdb.address WHERE SUBSTRING_INDEX(OrderIdCode, '\\\\', 1) = AddressID AND AddressType = 'Site' ) OR 'NZ' = ( SELECT Country FROM cdb.address WHERE SUBSTRING_INDEX(OrderIdCode, '\\\\', 1) = AddressID AND AddressType = 'Site' ))
                ) AS p WHERE d.distributorshipid = p.dist_id"



  myData <- eventReactive(input$refresh, {

    start_date <- input$daterange1[1]
    end_date <- input$daterange1[2]

    if (end_date - start_date < 400) {
      start_date <- end_date - 400
    }

    combstring <- paste(xstring1, start_date, xstring2, end_date,xstring3,sep="")

    sqlquery <- dbSendQuery(mysqlconnection, combstring)
    print("MyData SQL running...")
    fetchdata <- fetch(sqlquery, n=-1)
    # SB Sheds: SBshedsrows
    #    fetchdata %>% filter(NetworkID == "STR")
    fetchdata$Category[fetchdata$NetworkID == "STR"] = "SB Sheds"

    # FDB Patios
    fetchdata$Category[fetchdata$NetworkID == "FDBP"] <- "FDB Patios"
    # FDPatiorows <- fetchdata %>% filter(NetworkID == "FDBP")
    # Lifestyle
    fetchdata$Category[fetchdata$NetworkID == "STO"] <- "Lifestyle"
    # Lifestylerows <- fetchdata %>% filter(NetworkID == "STO")
    # NZ Sheds
    fetchdata$Category[str_detect(fetchdata$OrderIdCode, "^Z")] <- "NZ Sheds"
    # NZshedsrows <- fetchdata %>% filter(str_detect(OrderIdCode, "^Z"))
    # FDB Sheds
    # FDShedsrows <- fetchdata %>% filter(NetworkID != "STR" & NetworkID != "FDBP" & NetworkID != "STO" & str_detect(OrderIdCode, "^Z", negate=TRUE))
    fetchdata$Category[!(fetchdata$NetworkID %in% c("STR", "FDBP", "STO")) & str_detect(fetchdata$OrderIdCode, "^Z", negate=TRUE)] <- "FDB Sheds"

    fetchdata
    },ignoreNULL = FALSE)



  # Base SQL query template
  query_base <- "
    SELECT
      CASE WHEN (distid IS NULL OR distid = 'NONE') THEN 'Unaassigned' ELSE distid END AS distid,
      xml_program_name AS LeadSource,
      YEAR(request_time) AS `Year`,
      MONTH(request_time) AS `Month`,
      COUNT(*) AS Leads
    FROM
      app.quotes
    WHERE
      request_time BETWEEN '{start_date3} 00:00:00.000' AND '{end_date3} 23:59:59.999'
      AND (distid IS NULL OR (distid IS NOT NULL AND distid NOT LIKE 'Z%' AND distid NOT LIKE 'CY%' AND distid NOT IN ('AFDS', 'CFDS')))
      AND xml NOT LIKE '%<Country>NewZealand</Country>%'
    GROUP BY 1, 2, 3, 4
    ORDER BY 1, 2, 3, 4
  "

  # Fetch data function
  fetchLeadsData <- function(start_date3, end_date3) {
    combquery <- glue(query_base, start_date3 = start_date3, end_date3 = end_date3)
    tryCatch({
      res <- dbSendQuery(mysqlconnection3, combquery)
      data <- dbFetch(res, n = -1)
      dbClearResult(res)
      data
    }, error = function(e) {
      showNotification("Error fetching data from the database", type = "error")
      data.frame()
    })
  }

  # Reactive data based on date range
  leadsData <- reactive({
    start_date3 <- ifelse(!is.null(input$startDate), as.character(input$startDate), "2024-01-01")
    end_date3 <- ifelse(!is.null(input$endDate), as.character(input$endDate), as.character(Sys.Date()))
 #fetchLeadsData(start_date3,end_date3)
    #Below fixes the 'Unaass' as 'Unassigned'
   data <- fetchLeadsData(start_date3,end_date3)
   data$distid <- ifelse(data$distid=='Unaass','Unassigned',data$distid)
   data
  })

  # Function to aggregate data by LeadSource category
  aggregateByCategory <- function(data, lead_sources) {
    category_data <- subset(data, LeadSource %in% lead_sources)
    aggregated_data <- aggregate(Leads ~ distid, category_data, sum)

    aggregated_data
  }

  # Tab 1: Uncategorized Data
  output$rawdata <- renderDT({
    datatable(leadsData(), options = list(pageLength = 20, lengthMenu = c(10, 20, 50, 100),autoWidth=TRUE))

  })

  # Tab 2: App Total Data Table
  output$appTotal <- renderDT({
    data <- leadsData()
    if (nrow(data) > 0) {
      app_total <- aggregateByCategory(data, c('designer.ios', 'designer.android', 'designer.web'))
      datatable(app_total, options = list(pageLength = 20))
    }
  })

  #####################

  # Render combined line chart for App and Web Leads
  output$leads_combined_line_chart <- renderPlot({
    leads_data_distribution <- leadsData()

    if (nrow(leads_data_distribution) > 0) {
      # Define categories for App and Web
      leads_app_categories <- c('designer.ios', 'designer.android', 'designer.web')
      leads_web_categories <- c('web.toast.au')

      # Aggregate leads for App
      leads_app_data <- leads_data_distribution[leads_data_distribution$LeadSource %in% leads_app_categories, ]
      leads_app_summary <- aggregate(Leads ~ Year + Month, data = leads_app_data, FUN = sum)
      leads_app_summary$Category <- "App"

      # Aggregate leads for Web
      leads_web_data <- leads_data_distribution[leads_data_distribution$LeadSource %in% leads_web_categories, ]
      leads_web_summary <- aggregate(Leads ~ Year + Month, data = leads_web_data, FUN = sum)
      leads_web_summary$Category <- "Web"

      # Combine App and Web summaries
      leads_combined_summary <- rbind(leads_app_summary, leads_web_summary)

      # Create a datetime column for x-axis
      leads_combined_summary$Date <- as.Date(paste(leads_combined_summary$Year, leads_combined_summary$Month, "01", sep = "-"))




      # Plot combined line graph
      ggplot(leads_combined_summary, aes(x = Date, y = Leads, color = Category, group = Category)) +
        geom_line(size = 1) +
        geom_point(size = 2) +
        labs(title = "App and Web Leads Over Time", x = "Date", y = "Total Leads", color = "Category") +
        theme_minimal() +
        scale_x_date(date_labels = "%b %Y", date_breaks = "1 month") +
        theme(axis.text.x = element_text(angle = 45, hjust = 1),
              legend.title = element_text(size = 20), #adjust legend title size
              legend.text = element_text(size = 15)
              )
    }
  })

  ##########################################
  # NEW: Download handler for the leads data
  output$download_leads_data <- downloadHandler(
    filename = function() {
      paste("leads_distribution_", input$startDate,"_and_", input$endDate, ".csv", sep = "")
    },
    content = function(file) {
      leads_data_distribution <- leadsData() # Use the existing reactive function

      if (nrow(leads_data_distribution) > 0) {
        # Define categories for App and Web
        leads_app_categories <- c('designer.ios', 'designer.android', 'designer.web')
        leads_web_categories <- c('web.toast.au')

        # Aggregate leads for App
        leads_app_data <- leads_data_distribution[leads_data_distribution$LeadSource %in% leads_app_categories, ]
        leads_app_summary <- aggregate(Leads ~ Year + Month, data = leads_app_data, FUN = sum)
        leads_app_summary$Category <- "App"

        # Aggregate leads for Web
        leads_web_data <- leads_data_distribution[leads_data_distribution$LeadSource %in% leads_web_categories, ]
        leads_web_summary <- aggregate(Leads ~ Year + Month, data = leads_web_data, FUN = sum)
        leads_web_summary$Category <- "Web"

        # Combine App and Web summaries
        leads_combined_summary <- rbind(leads_app_summary, leads_web_summary)

        # Create a datetime column for x-axis
        leads_combined_summary$Date <- as.Date(paste(leads_combined_summary$Year, leads_combined_summary$Month, "01", sep = "-"))

        # Write data to CSV
        write.csv(leads_combined_summary, file, row.names = FALSE)
      }
    }
  )
  ###################################################

  # Tab 3: Web Total Data Table
  output$webTotal <- renderDT({
    data <- leadsData()
    if (nrow(data) > 0) {
      web_total <- aggregateByCategory(data, c('web.toast.au'))
      datatable(web_total, options = list(pageLength = 20))
    }
  })

  # Tab 4: Bar Charts for App and Web Total (Top 25 Distributors)
  # App Total Bar Chart
  output$appBarChart <- renderPlot({
    data <- leadsData()
    if (nrow(data) > 0) {
      app_total <- aggregateByCategory(data, c('designer.ios', 'designer.android', 'designer.web'))
      app_top25 <- app_total[order(-app_total$Leads), ][1:min(25, nrow(app_total)), ]
      ggplot(app_top25, aes(x = reorder(distid, -Leads), y = Leads)) +
        geom_bar(stat = "identity", fill = "skyblue") +
        geom_text(aes(label = Leads), vjust = -0.5, size = 4) +
        labs(title = "Top 25 App Total Leads by Distributor", x = "Distributor", y = "Total Leads") +
        theme_minimal() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1.3))
    }
  })

  # Web Total Bar Chart
  output$webBarChart <- renderPlot({
    data <- leadsData()
    if (nrow(data) > 0) {
      web_total <- aggregateByCategory(data, c('web.toast.au'))
      web_top25 <- web_total[order(-web_total$Leads), ][1:min(25, nrow(web_total)), ]
      ggplot(web_top25, aes(x = reorder(distid, -Leads), y = Leads)) +
        geom_bar(stat = "identity", fill = "lightgreen") +
        geom_text(aes(label = Leads), vjust = -0.5, size = 4) +
        labs(title = "Top 25 Web Total Leads by Distributor", x = "Distributor", y = "Total Leads") +
        theme_minimal() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1.3))
    }
  })

  # Download handler for all data
  output$downloadDataFranchisee <- downloadHandler(
    filename = function() { paste("leads_data_", input$startDate,"_and_", input$endDate, ".csv", sep = "") },
    content = function(file) { write.csv(leadsData(), file, row.names = FALSE) }
  )

  # Download handler for all App Total data
  output$downloadAppData <- downloadHandler(
    filename = function() { paste("app_total_",input$startDate,"_and_", input$endDate, ".csv", sep = "") },
    content = function(file) {
      data <- leadsData()
      if (nrow(data) > 0) {
        app_total <- aggregateByCategory(data, c('designer.ios', 'designer.android', 'designer.web'))
        write.csv(app_total, file, row.names = FALSE)
      }
    }
  )

  # Download handler for all Web Total data
  output$downloadWebData <- downloadHandler(
    filename = function() { paste("web_total_",input$startDate,"_and_", input$endDate, ".csv", sep = "") },
    content = function(file) {
      data <- leadsData()
      if (nrow(data) > 0) {
        web_total <- aggregateByCategory(data, c('web.toast.au'))
        write.csv(web_total, file, row.names = FALSE)
      }
    }
  )







  #### Pulling Customer Data for Customer Sales Map
  xstring_cust1 <- "SELECT
                        o.OrderSentToSupplier,
                        o.DistributerCode,
                        o.TotalPrice,
                        c.Address,
                        c.Town,
                        c.PostCode
                  FROM
                      Table_Order o
                  JOIN Table_Customer c ON c.Code = o.Customer_Code
                  WHERE o.OrderSentToSupplier BETWEEN '"

  xstring_cust2 <- " 00:00:000' AND '"
  xstring_cust3 <- " 23:59:59.999'"

  customerData <- eventReactive(input$refresh, {

    start_date <- input$daterange1[1]
    end_date <- input$daterange1[2]

    if (end_date - start_date < 360) {
      start_date <- end_date - 360
    }

    combstring <- paste(xstring_cust1, start_date, xstring_cust2, end_date, xstring_cust3,sep="")
    print("MyData SQL2 running...")
    sqlquery <- dbSendQuery(mysqlconnection2, combstring)

    fetchdata <- fetch(sqlquery, n=-1)
    fetchdata$Address <- gsub("\n","",fetchdata$Address)
    fetchdata$Town <- toupper(fetchdata$Town)

    # SB Sheds
    fetchdata$Category[fetchdata$DistributerCode == "STR"] = "SB Sheds"
    # FDB Patios
    fetchdata$Category[fetchdata$DistributerCode == "FDBP"] <- "FDB Patios"
    # Lifestyle
    fetchdata$Category[fetchdata$DistributerCode == "STO"] <- "Lifestyle"
    # NZ Sheds
    fetchdata$Category[str_detect(fetchdata$DistributerCode, "^Z")] <- "NZ Sheds"
    # FDB Sheds
    fetchdata$Category[!(fetchdata$DistributerCode %in% c("STR", "FDBP", "STO")) & str_detect(fetchdata$DistributerCode, "^Z", negate=TRUE)] <- "FDB Sheds"

    unique_postcode <- unique(fetchdata$PostCode)

    updateSelectizeInput(inputId = "PostCodeID", choices= c("All", unique_postcode), selected='All', server=TRUE)
    fetchdata
  },ignoreNULL = FALSE)


  leadData <- eventReactive(input$refresh, {
    xstring_lead1 <- "SELECT
                        distid,
                        request_time,
                        status,
                        COUNT(*) as cnt,
                        SUBSTR(xml, INSTR(xml, '<Street>')+8, INSTR(xml, '</Street>')-8 - INSTR(xml, '<Street>')) AS Street,
                        SUBSTR(xml, INSTR(xml, '<Town>')+6, INSTR(xml, '</Town>')-6 - INSTR(xml, '<Town>')) AS Town,
                        SUBSTR(xml, INSTR(xml, '<Postcode>')+10, INSTR(xml, '</Postcode>')-10 - INSTR(xml, '<Postcode>')) AS PostCode
                    FROM quotes
                    WHERE request_time BETWEEN '"

    xstring_lead2 <- " 00:00:000' AND '"
    xstring_lead3 <- " 23:59:59.999' GROUP BY 1, 3, 6, 7"

    start_date <- input$daterange1[1]
    end_date <- input$daterange1[2]

    if (end_date - start_date < 360) {
      start_date <- end_date - 360
    }

    combstring <- paste(xstring_lead1, start_date, xstring_lead2, end_date, xstring_lead3,sep="")
    print("MyData SQL1 running...")
    sqlquery <- dbSendQuery(mysqlconnection1, combstring)

    fetchdata <- fetch(sqlquery, n=-1)
    fetchdata$Town <- toupper(fetchdata$Town)
    fetchdata <- fetchdata %>% filter(nchar(PostCode) >= 3)

    unique_postcode <- unique(fetchdata$PostCode)
    updateSelectizeInput(inputId = "LeadPostCodeID", choices= c("All", unique_postcode), selected='All', server=TRUE)

    unique_status <- unique(fetchdata$status)
    updateSelectizeInput(inputId = "LeadStatus", choices= c("All", unique_status), selected='All', server=TRUE)

    fetchdata

    }, ignoreNULL = FALSE)


  myFilterData <- reactive({
    #### Filter data based on selected categories ####
    filterData <- myData() %>% filter(Category %in% input$Category)
    unique_dist <- unique(filterData$dist_id)
    updateSelectInput(inputId = "DistributorID", choices= c("All", unique_dist), selected = "All")

    filterData

  })

  #### Pulling Sheds data

  # Extract actual values
  xstring_shed1 <- "SELECT OrderIdCode, PaymentTo, YEAR(StatusDate) AS year, MONTH(StatusDate) AS month, Ammount AS Amount FROM cdb.payments p1 WHERE (PaymentTo = 'FeeAGS' OR PaymentTo = 'FeeMB') AND StatusDate BETWEEN '"
  xstring_shed2 <- "' AND Ammount > 0
  AND NOT EXISTS ( SELECT * FROM cdb.payments p2 WHERE p1.OrderIdCode = p2.OrderIdCode AND p2.PaymentTo = 'FeeCancel' )
;"



  sheddata <- eventReactive(input$refresh, {

    start_date <- input$daterange1[1]
    end_date <- input$daterange1[2]


    start_date <- end_date - 2*360


    combstringshed <- paste(xstring_shed1, floor_date(start_date, "month"), "' AND '", end_date, xstring_shed2, sep="")
    sqlquery <- dbSendQuery(mysqlconnection, combstringshed)
    fetchdata <- fetch(sqlquery, n=-1)
    fetchdata
  }, ignoreNULL = FALSE)


  sheddata_address <- eventReactive(input$refresh, {
    xstring_address <- "SELECT * FROM cdb.address WHERE AddressType='Site'";
    sqlquery_address <- dbSendQuery(mysqlconnection, xstring_address)
    fetchdata_address <- fetch(sqlquery_address, n=-1)
    fetchdata_address
  }, ignoreNULL = FALSE)


  # Extract target values for the last 360 days
  target_data <- eventReactive(input$refresh, {
    xstring_target1 <- "SELECT *
                      FROM `order_num_targets`
                      WHERE (`year`="
    xstring_target2 <- ") AND country='AU'
                      ORDER BY id DESC
                      ;"
    mid_str <- " OR `year`="

    start_date <- input$daterange1[1]
    end_date <- input$daterange1[2]

    if (end_date - start_date < 360) {
      start_date <- end_date - 360
    }
    combstringtarget <- paste(xstring_target1, year(start_date), mid_str, year(end_date), xstring_target2, sep="")


    sqlquery_target <- dbSendQuery(mysqlconnection, combstringtarget)
    fetchdata_target <- fetch(sqlquery_target, n=-1)
    fetchdata_target

    clean_target <- fetchdata_target[match(unique(paste(as.character(fetchdata_target$month),as.character(fetchdata_target$year))), paste(as.character(fetchdata_target$month),as.character(fetchdata_target$year))), ]
    clean_target
  }, ignoreNULL = FALSE)



  output$result <- renderDT({
    myFilterData()
    })


  #### Gauge plots ####
  output$TodaySale <- renderGauge({

    toDayDate <- input$daterange1[2]

    fetchdata <- myFilterData() %>% filter(as.Date(StatusDate) == toDayDate)

    salesqty <- fetchdata %>% filter(PaymentTo == "FeeAGS") %>% nrow()

    salesvalue <- ceiling(sum(fetchdata$Ammount))
    monthSaleQty <- thisMonthSaleValue() %>% filter(PaymentTo == "FeeAGS") %>% nrow()
    # budget and forecast
    month_val = month(toDayDate)
    year_val = year(toDayDate)

    budget_forecast = target_data() %>% filter(year == year_val & month == month_val)
    remaining_work_day <- get_working_day2(toDayDate)
    budget <- ceiling((budget_forecast$budget - monthSaleQty)/ remaining_work_day)
    forecast <- ceiling((budget_forecast$forecast - monthSaleQty)/ remaining_work_day)

    gauge(salesqty,
          min = 0,
          max=budget, label=paste(strwrap(paste("$", salesvalue, "\n", str_interp("\nF: ${forecast}, B: ${budget}")), width = 20), collapse = "\n"),
          sectors = gaugeSectors(success = c(0.5*budget, 1*budget),
                                 warning = c(0.3*budget, 0.5*budget),
                                 danger = c(0, 0.3*budget)))
  })


  output$YesterdaySale<-renderGauge({

    toDayDate <- input$daterange1[2]

    fetchdata <- myFilterData() %>% filter(as.Date(StatusDate) == toDayDate - 1)

    salesqty <- fetchdata %>% filter(PaymentTo == "FeeAGS") %>% nrow()

    salesvalue <- round(sum(fetchdata$Ammount))

    # budget and forecast
    month_val = month(toDayDate-1)
    year_val = year(toDayDate-1)

    budget_forecast = target_data() %>% filter(year == year_val & month == month_val)

    remaining_work_day <- get_working_day2(floor_date(toDayDate, "month"))
    budget <- ceiling(budget_forecast$budget / remaining_work_day)
    forecast <- ceiling(budget_forecast$forecast / remaining_work_day)

    gauge(salesqty,
          min = 0,
          max=budget, label=paste(strwrap(paste("$", salesvalue, "\n", str_interp("\nF: ${forecast}, B: ${budget}")), width = 20), collapse = "\n"),
          sectors = gaugeSectors(success = c(0.5*budget, 1*budget),
                                 warning = c(0.3*budget, 0.5*budget),
                                 danger = c(0, 0.3*budget)))

  })

  output$ThisWeekSale<-renderGauge({

    toDayDate <- input$daterange1[2]

    monday_start <- floor_date(toDayDate, "week") + 1

    fetchdata <- myFilterData() %>% filter(as.Date(StatusDate) >= monday_start & as.Date(StatusDate) <= toDayDate)

    salesqty <- fetchdata %>% filter(PaymentTo == "FeeAGS") %>% nrow()

    salesvalue <- round(sum(fetchdata$Ammount))


    # budget and forecast
    month_val = month(toDayDate)
    year_val = year(toDayDate)
    monthSaleQty <- thisMonthSaleValue() %>% filter(PaymentTo == "FeeAGS") %>% nrow()

    budget_forecast = target_data() %>% filter(year == year_val & month == month_val)
    remaining_work_day <- get_working_day2(toDayDate)
    budget <- round((budget_forecast$budget - monthSaleQty)/ remaining_work_day * 5)
    forecast <- round((budget_forecast$forecast - monthSaleQty)/ remaining_work_day * 5)


    gauge(salesqty,
          min = 0,
          max=budget, label=paste(strwrap(paste("$", salesvalue, "\n", str_interp("\nF: ${forecast}, B: ${budget}")), width = 20), collapse = "\n"),
          sectors = gaugeSectors(success = c(0.5*budget, 1*budget),
                                 warning = c(0.3*budget, 0.5*budget),
                                 danger = c(0, 0.3*budget)))
  })


  output$LastWeekSale <- renderGauge({
    toDayDate <- input$daterange1[2]
    monday_start_last_week <- floor_date(toDayDate - 7, "week") + 1
    sunday_end_last_week <- monday_start_last_week + 6

    fetchdata <- myFilterData() %>% filter(as.Date(StatusDate) >= monday_start_last_week & as.Date(StatusDate) <= sunday_end_last_week)

    salesqty <- fetchdata %>% filter(PaymentTo == "FeeAGS") %>% nrow()

    salesvalue <- round(sum(fetchdata$Ammount))

    monthSaleQty <- thisMonthSaleValue() %>% filter(PaymentTo == "FeeAGS") %>% nrow()


    # budget and forecast
    month_val = month(toDayDate - 7)
    year_val = year(toDayDate - 7)

    budget_forecast = target_data() %>% filter(year == year_val & month == month_val)
    remaining_work_day <- get_working_day2(toDayDate-7)
    budget <- round((budget_forecast$budget - monthSaleQty)/ remaining_work_day * 5)
    forecast <- round((budget_forecast$forecast - monthSaleQty)/ remaining_work_day * 5)

    gauge(salesqty,
          min = 0,
          max=budget, label=paste(strwrap(paste("$", salesvalue, "\n", str_interp("\nF: ${forecast}, B: ${budget}")), width = 20), collapse = "\n"),
          sectors = gaugeSectors(success = c(0.5*budget, 1*budget),
                                 warning = c(0.3*budget, 0.5*budget),
                                 danger = c(0, 0.3*budget)))
  })



  output$ThisMonthSale <- renderGauge({
    todayDate <- input$daterange1[2]

    fetchdata <- thisMonthSaleValue()

    salesqty <- fetchdata %>% filter(PaymentTo == "FeeAGS") %>% nrow()

    salesvalue <- round(sum(fetchdata$Ammount))

    # budget and forecast
    budget_forecast = target_data() %>% filter(year == year(todayDate) & month == month(todayDate))
    budget <- budget_forecast$budget
    forecast <- budget_forecast$forecast

    gauge(salesqty,
          min = 0,
          max=budget, label=paste(strwrap(paste("$", salesvalue, "\n", str_interp("\nF: ${forecast}, B: ${budget}")), width = 20), collapse = "\n"),
          sectors = gaugeSectors(success = c(0.5*budget, 1*budget),
                                 warning = c(0.3*budget, 0.5*budget),
                                 danger = c(0, 0.3*budget)))
  })


  output$LastMonthSale<-renderGauge({
    todayDate <- input$daterange1[2]
    last_month_start <- floor_date(todayDate, "month") %m-% months(1)
    this_month_start <- floor_date(todayDate, "month")

    fetchdata <- myFilterData() %>% filter(as.Date(StatusDate) >= last_month_start & as.Date(StatusDate) < this_month_start)

    salesqty <- fetchdata %>% filter(PaymentTo == "FeeAGS") %>% nrow()

    salesvalue <- round(sum(fetchdata$Ammount))

    # budget and forecast
    last_month <- month(todayDate) - 1
    year_val <- year(todayDate)

    if (last_month == 0) {
      last_month <- 12
      year_val <- year_val - 1
    }

    budget_forecast = target_data() %>% filter(year == year_val & month == last_month)
    budget <- budget_forecast$budget
    forecast <- budget_forecast$forecast

    gauge(salesqty,
          min = 0,
          max=budget, label=paste(strwrap(paste("$", salesvalue, "\n", str_interp("\nF: ${forecast}, B: ${budget}")), width = 20), collapse = "\n"),
          sectors = gaugeSectors(success = c(0.5*budget, 1*budget),
                                 warning = c(0.3*budget, 0.5*budget),
                                 danger = c(0, 0.3*budget)))
  })

  output$Commission <- renderText({
    todayDate <- input$daterange1[2]

    if (month(todayDate) < 7) {
      temp <- paste("01-07-", as.character(year(todayDate)-1), sep="")
      fin_year_start <- as.Date(temp, format="%d-%m-%Y")
    } else {
      temp <- paste("01-07-", as.character(year(todayDate)), sep="")
      fin_year_start <- as.Date(temp, format="%d-%m-%Y")
    }

    fetchdata <- myFilterData() %>% filter(as.Date(StatusDate) >= fin_year_start & as.Date(StatusDate) <= todayDate)

    salesqty <- fetchdata %>% filter(PaymentTo == "FeeAGS") %>% nrow()

    salesvalue <- round(sum(fetchdata$Ammount))

    paste("$",  prettyNum(salesvalue, big.mark = ","))
  })

  output$FinYearSale <- renderGauge({
    todayDate <- input$daterange1[2]
    # if 07 <= month <= 12 -> year is the same
    # else year = current year - 1
    if (month(todayDate) < 7) {
      temp <- paste("01-07-", as.character(year(todayDate)-1), sep="")
      fin_year_start <- as.Date(temp, format="%d-%m-%Y")
    } else {
      temp <- paste("01-07-", as.character(year(todayDate)), sep="")
      fin_year_start <- as.Date(temp, format="%d-%m-%Y")
    }


    fetchdata <- myFilterData() %>% filter(as.Date(StatusDate) >= fin_year_start & as.Date(StatusDate) <= todayDate)

    salesqty <- fetchdata %>% filter(PaymentTo == "FeeAGS") %>% nrow()

    salesvalue <- round(sum(fetchdata$Ammount))

    current_month <- month(todayDate)
    current_year <- year(todayDate)
    # cond <- (year < current_year & month >= current_month) | (year == current_year & month <= current_month)

    # budget and forecast
    budget_forecast = target_data() %>% filter((year < current_year & month >= current_month) | (year == current_year & month <= current_month))
    budget <- sum(budget_forecast$budget)
    forecast <- sum(budget_forecast$forecast)

    gauge(salesqty,
          min = 0,
          max=budget, label=paste(strwrap(paste("$", salesvalue, "\n", str_interp("\nF: ${forecast}, B: ${budget}")), width = 20), collapse = "\n"),
          sectors = gaugeSectors(success = c(0.5*budget, 1*budget),
                                 warning = c(0.3*budget, 0.5*budget),
                                 danger = c(0, 0.3*budget)))
  })

  #### display map
  # NOTE: leaflet map does not display when using Stramit network
  output$MapSale <- renderLeaflet({

    fetchdata <- myFilterData() %>% filter( as.Date(StatusDate) >= input$daterange1[1] & as.Date(StatusDate) <= input$daterange1[2])

    merged_df <- merge(fetchdata, distributor_df, by.x="dist_id", by.y="DistributorshipID")

    temp_grp <- merged_df %>% group_by(dist_id) %>% mutate(sum_amount = sum(Ammount)) %>% ungroup()

    if (input$Area != 'All' & input$Area != "NA") {
      temp_grp <- temp_grp %>% filter(area == input$Area)
    }

    min_val <-  min(temp_grp$sum_amount)
    max_val <-  max(temp_grp$sum_amount) + 1000 # offset by $1000

    if (input$DistributorID != 'All') {
      temp_grp <- temp_grp %>% filter(dist_id == input$DistributorID)
    }


    temp_grp %>% mutate(heat_intensity = (sum_amount - min_val) / (max_val - min_val)) %>% leaflet(width=2000, height=5000) %>% addTiles() %>%
      setView(lng=151.2093, lat=-33.8688, zoom=3.5) %>%
      addHeatmap(lng=~Lo, lat=~La, intensity = ~heat_intensity, blur=5,
                 minOpacity = 0.05, max=0.95, radius=15) %>%
      addCircleMarkers(lng = ~Lo, lat = ~La,
                       fillOpacity = 0, weight = 0,
                       label = ~htmlEscape(paste(temp_grp$BusinessName,
                                     "$$$:", prettyNum(round(temp_grp$sum_amount), big.mark = "," ))),
                       labelOptions = labelOptions(noHide = FALSE))



    }
  )


  output$MapFrequency <- renderLeaflet({

    fetchdata <- myFilterData() %>% filter( as.Date(StatusDate) >= input$daterange1[1] & as.Date(StatusDate) <= input$daterange1[2])

    merged_df <- merge(fetchdata, distributor_df, by.x="dist_id", by.y="DistributorshipID")

    temp_grp <- merged_df %>% group_by(dist_id) %>% mutate(total_freq = n()) %>% ungroup()

    if (input$Area != 'All' & input$Area != "NA") {
      temp_grp <- temp_grp %>% filter(area == input$Area)
    }

    min_val <-  min(temp_grp$total_freq)
    max_val <-  max(temp_grp$total_freq) + 10

    if (input$DistributorID != 'All') {
      temp_grp <- temp_grp %>% filter(dist_id == input$DistributorID)
    }


    temp_grp %>% mutate(heat_intensity = (total_freq - min_val) / (max_val - min_val)) %>% leaflet(width=2000, height=5000) %>% addTiles() %>%
      setView(lng=151.2093, lat=-33.8688, zoom=3.5) %>%
      addHeatmap(lng=~Lo, lat=~La, intensity = ~heat_intensity, blur=5,
                 minOpacity = 0.05, max=0.95, radius=15) %>%
      addCircleMarkers(lng = ~Lo, lat = ~La,
                       fillOpacity = 0, weight = 0,
                       label = ~htmlEscape(paste(temp_grp$BusinessName,
                                                 "Count:", prettyNum(round(temp_grp$total_freq), big.mark = "," ))),
                       labelOptions = labelOptions(noHide = FALSE))



  }
  )

  output$CustomerMap <- renderLeaflet({
    start_date <- input$daterange1[1]
    end_date <- input$daterange1[2]

    tempData <- customerData() %>% filter(OrderSentToSupplier >= start_date & OrderSentToSupplier <= end_date)

   # tempData <- tempData %>% filter(DistributerCode == 'All')
    merged_df <- merge(tempData, au_postcodes_df, by.x=c("Town", "PostCode"), by.y=c("locality", "postcode"))

    if (input$AusStateID != 'All') {
        merged_df <- merged_df %>% filter(state == input$AusStateID)
    }

    if (input$PostCodeID != 'All') {
      merged_df <- merged_df %>% filter(PostCode == input$PostCodeID)
    }

    first_table <- merged_df %>% group_by(Town, PostCode) %>% mutate(TotalSales = sum(TotalPrice)) %>% ungroup()
    min_val <-  min(first_table$TotalSales)
    max_val <-  max(first_table$TotalSales) + 1000
    range_val = max_val - min_val

    first_table %>% mutate(heat_intensity = max(0.1*range_val, (TotalSales - min_val)/range_val) ) %>% leaflet() %>% addTiles() %>%
      setView(lng=151.2093, lat=-33.8688, zoom=3.5) %>%
      addHeatmap(lng=~Long_precise, lat=~Lat_precise, intensity = ~heat_intensity, blur=5,
                 minOpacity = 0.05, max=0.95, radius=15) %>%
      addCircleMarkers(lng = ~Long_precise, lat = ~Lat_precise,
                       fillOpacity = 0, weight = 0,
                       label = ~htmlEscape(paste(first_table$Town,
                                                 first_table$PostCode,"$$$:", prettyNum(ceiling(first_table$TotalSales), big.mark = ","))),
                       labelOptions = labelOptions(noHide = FALSE))

  }
  )

  output$CustomerFrequency <- renderLeaflet({

    start_date <- input$daterange1[1]
    end_date <- input$daterange1[2]

    tempData <- customerData() %>% filter(OrderSentToSupplier >= start_date & OrderSentToSupplier <= end_date)
   # tempData <- tempData %>% filter(DistributerCode == 'All')
    merged_df <- merge(tempData, au_postcodes_df, by.x=c("Town", "PostCode"), by.y=c("locality", "postcode"))
    first_table <- merged_df %>% group_by(Town, PostCode) %>% mutate(total_freq = n()) %>% ungroup()


    if (input$AusStateID != 'All') {
      first_table <- first_table %>% filter(state == input$AusStateID)
    }

    if (input$PostCodeID != 'All') {
      first_table <- first_table %>% filter(PostCode == input$PostCodeID)
    }

    min_val <-  min(first_table$total_freq)
    max_val <-  max(first_table$total_freq) + 10
    range_val = max_val - min_val

    first_table %>% mutate(heat_intensity = (total_freq - min_val)/range_val ) %>% leaflet() %>% addTiles() %>%
      setView(lng=151.2093, lat=-33.8688, zoom=3.5) %>%
      addHeatmap(lng=~Long_precise, lat=~Lat_precise, intensity = ~heat_intensity, blur=5,
                 minOpacity = 0.05, max=0.95, radius=15) %>%
      addCircleMarkers(lng = ~Long_precise, lat = ~Lat_precise,
                       fillOpacity = 0, weight = 0,
                       label = ~htmlEscape(paste(first_table$Town,
                                                 first_table$PostCode,"Count:", prettyNum(ceiling(first_table$total_freq), big.mark = ","))),
                       labelOptions = labelOptions(noHide = FALSE))

  }
  )

  output$LeadMap <- renderLeaflet({
    start_date <- input$daterange1[1]
    end_date <- input$daterange1[2]

    tempData <- leadData() %>% filter(request_time >= start_date & request_time <= end_date)

    merged_df <- merge(tempData, au_postcodes_df, by.x=c("Town", "PostCode"), by.y=c("locality", "postcode"))

    if (input$LeadAusStateID != 'All') {
      merged_df <- merged_df %>% filter(state == input$LeadAusStateID)
    }

    if (input$LeadPostCodeID != 'All') {
      merged_df <- merged_df %>% filter(PostCode == input$LeadPostCodeID)
    }

    if (input$LeadStatus != 'All') {
      merged_df <- merged_df %>% filter(status == input$LeadStatus)
    }

    first_table <- merged_df %>% group_by(Town, PostCode) # %>% mutate(TotalSales = sum(TotalPrice)) %>% ungroup()
    min_val <- min(first_table$cnt)
    max_val <- max(first_table$cnt) + 5
    range_val = max_val - min_val

    first_table %>% mutate(heat_intensity = (cnt - min_val)/range_val) %>% leaflet() %>% addTiles() %>%
      setView(lng=151.2093, lat=-33.8688, zoom=3.5) %>%
      addHeatmap(lng=~Long_precise, lat=~Lat_precise, intensity = ~heat_intensity, blur=5,
                 minOpacity = 0.05, max=0.95, radius=15) %>%
      addCircleMarkers(lng = ~Long_precise, lat = ~Lat_precise,
                       fillOpacity = 0, weight = 0,
                       label = ~htmlEscape(paste(first_table$Town,
                                                 first_table$PostCode,"Count:", prettyNum(ceiling(first_table$cnt), big.mark = ","))),
                       labelOptions = labelOptions(noHide = FALSE))

  }
  )

  output$number_sheds <- renderGirafe({

      todayDate <- input$daterange1[2]
      lastyearDate <- todayDate - 360

    new_fetchdata <- sheddata() %>% rowwise() %>% mutate(orderID = str_split(OrderIdCode, "\\\\")[[1]][1])
    merged_df <- merge(new_fetchdata, sheddata_address(), by.x="orderID", by.y = "AddressID")

    result_df <- merged_df %>% filter(Country == "AU")  %>% group_by(year, month, Country) %>% summarise(actual = n()/2, total_actual = sum(Amount))
    current_fin_year = result_df %>% filter(((year == year(todayDate-360)) & (month > month(todayDate) )) | (year == year(todayDate)))
    prev_fin_year = setdiff(result_df, current_fin_year)
    # hacky way to increment the year of prev fin year so its plot can be overlayed
    prev_fin_year$year = prev_fin_year$year + 1
    # rename column actual value
    names(prev_fin_year)[names(prev_fin_year) == "total_actual"] <- "prev_total_actual"
    names(prev_fin_year)[names(prev_fin_year) == "actual"] <- "prev_actual"


    first_merged <- merge(result_df, target_data(), by=c("year", "month"))
    final_merged <- merge(first_merged, prev_fin_year, by=c("year", "month"), all=TRUE)
    final_table <- final_merged %>% mutate(total_budget = budget * 286,
                                           total_forecast = forecast * 286,
                                           date=as.Date(as.yearmon(paste(as.character(year), as.character(month),sep="-")))) %>% drop_na()

    final_table_projected <- final_table %>% mutate(projected_qty = actual, projected_value = total_actual, lastyear_mtd = actual)

    currentMonth_salesqty <- thisMonthSaleValue() %>% filter(PaymentTo == "FeeAGS") %>% nrow()
    total_working_day <- get_working_day(year(todayDate), month(todayDate))
    remaining_working_day <- get_working_day2( todayDate )

    final_table_projected[ final_table_projected$year == year(todayDate) & final_table_projected$month == month(todayDate), "lastyear_mtd" ] = ceiling(lastyear_mtd())
    final_table_projected[ final_table_projected$year == year(todayDate) & final_table_projected$month == month(todayDate), "projected_qty" ] = ceiling(currentMonth_salesqty * total_working_day / (total_working_day - remaining_working_day))

    gg <- final_table_projected %>% mutate(date=as.Date(as.yearmon(paste(as.character(year), as.character(month),sep="-")))) %>% ggplot() +
      geom_line(aes(x=date, y=projected_qty, color="Projected"), alpha=0.5,linewidth=1) +
      geom_point_interactive(aes(x=date, y=projected_qty, color="Projected", tooltip=paste0("projected: ", projected_qty)), size=2) +
      geom_line(aes(x=date, y=lastyear_mtd, color="Last Year (MTD)"), alpha=0.5,linewidth=1) +
      geom_point_interactive(aes(x=date, y=lastyear_mtd, color="Last Year (MTD)", tooltip=paste0("lastyear_mtd: ", lastyear_mtd)), size=2) +
      geom_line(aes(x=date, y=actual, color="Actual"), linewidth=1) +
      geom_point_interactive(aes(x=date, y=actual, color="Actual", tooltip=paste0("actual: ", actual)), size=2) +
      geom_line(aes(x=date, y=prev_actual, color="Last Year"), linewidth=1) +
      geom_point_interactive(aes(x=date, y=prev_actual, color="Last Year", tooltip=paste0("last year: ", prev_actual)), size=2) +
      geom_line(aes(x=date, y=budget, color="Budget"), linewidth=1) +
      geom_point_interactive(aes(x=date, y=budget, color="Budget", tooltip=paste0("budget: ", budget)), size=2) +
      geom_line(aes(x=date, y=forecast, color="Forecast"), linewidth=1) +
      geom_point_interactive(aes(x=date, y=forecast, color="Forecast", tooltip=paste0("forecast: ", forecast)), size=2) +

      labs(x = "Date", y = "# of Sheds") +
      scale_color_manual(values = c("Actual" = "red", "Budget" = "green",
                                    "Forecast" = "blue", "Last Year" ="orange",
                                    "Projected" = "pink", "Last Year (MTD)" = "yellow")) +
      theme(axis.title.x = element_text(size = 12,
                                        color = "blue",
                                        face = "bold"),
            axis.title.y = element_text(size = 12,
                                        color = "red",
                                        face = "italic")) +
      scale_x_date(date_breaks = "1 month", date_labels = "%Y-%m") +
      scale_y_continuous(labels = label_comma(),
                         limits = c(0, 1500)) +
      theme(panel.grid.minor.x = element_blank())
    girafe(code = print(gg), width=1, width_svg = 8)

  })





  output$total_sales <- renderGirafe({
    todayDate <- input$daterange1[2]
    new_fetchdata <- sheddata() %>% rowwise() %>% mutate(orderID = str_split(OrderIdCode, "\\\\")[[1]][1])
    merged_df <- merge(new_fetchdata, sheddata_address(), by.x="orderID", by.y = "AddressID")

    result_df <- merged_df %>% filter(Country == "AU")  %>% group_by(year, month, Country) %>% summarise(actual = n()/2, total_actual = sum(Amount))
    current_fin_year = result_df %>% filter(((year == year(todayDate-360)) & (month > month(todayDate) )) | (year == year(todayDate)))
    prev_fin_year = setdiff(result_df, current_fin_year)
    # hacky way to increment the year of prev fin year so its plot can be overlayed
    prev_fin_year$year = prev_fin_year$year + 1
    # rename column actual value
    names(prev_fin_year)[names(prev_fin_year) == "total_actual"] <- "prev_total_actual"
    names(prev_fin_year)[names(prev_fin_year) == "actual"] <- "prev_actual"



    first_merged <- merge(result_df, target_data(), by=c("year", "month"))
    final_merged <- merge(first_merged, prev_fin_year, by=c("year", "month"), all=TRUE)
    final_table <- final_merged %>% mutate(total_budget = budget * input$unit_price,
                                           total_forecast = forecast * input$unit_price,
                                           date=as.Date(as.yearmon(paste(as.character(year), as.character(month),sep="-")))) %>% drop_na()
    final_table_projected <- final_table %>% mutate(projected_qty = actual, projected_value = total_actual, lastyear_mtd = actual)

    currentMonth_salesqty <- thisMonthSaleValue() %>% filter(PaymentTo == "FeeAGS") %>% nrow()
    total_working_day <- get_working_day(year(todayDate), month(todayDate))
    remaining_working_day <- get_working_day2( todayDate )

    final_table_projected[ final_table_projected$year == year(todayDate) & final_table_projected$month == month(todayDate), "lastyear_mtd" ] = ceiling(lastyear_mtd())
    final_table_projected[ final_table_projected$year == year(todayDate) & final_table_projected$month == month(todayDate), "projected_qty" ] = ceiling(currentMonth_salesqty * total_working_day / (total_working_day - remaining_working_day ) )


    gg <- final_table_projected %>% mutate(date=as.Date(as.yearmon(paste(as.character(year), as.character(month),sep="-")))) %>% ggplot() +
      geom_line(aes(x=date, y=projected_qty * input$unit_price, color="Projected"), alpha=0.5,linewidth=1) +
      geom_point_interactive(aes(x=date, y=projected_qty * input$unit_price, color="Projected", tooltip=paste0("projected: ", projected_qty)), size=2) +
      geom_line(aes(x=date, y=lastyear_mtd * input$unit_price, color="Last Year (MTD)"), alpha=0.5,linewidth=1) +
      geom_point_interactive(aes(x=date, y=lastyear_mtd * input$unit_price, color="Last Year (MTD)", tooltip=paste0("lastyear_mtd: ", lastyear_mtd)), size=2) +
      geom_line(aes(x=date, y=actual* input$unit_price, color="Actual"), linewidth=1) +
      geom_point_interactive(aes(x=date, y=actual*input$unit_price, color="Actual", tooltip=paste0("actual: ", actual*input$unit_price)), size=2) +
      geom_line(aes(x=date, y=prev_actual*input$unit_price, color="Last Year"), linewidth=1) +
      geom_point_interactive(aes(x=date, y=prev_actual*input$unit_price, color="Last Year", tooltip=paste0("last year: ", prev_actual*input$unit_price)), size=2) +
      geom_line(aes(x=date, y=budget*input$unit_price, color="Budget"), linewidth=1) +
      geom_point_interactive(aes(x=date, y=budget*input$unit_price, color="Budget", tooltip=paste0("budget: ", budget*input$unit_price)), size=2) +
      geom_line(aes(x=date, y=forecast*input$unit_price, color="Forecast"), linewidth=1) +
      geom_point_interactive(aes(x=date, y=forecast*input$unit_price, color="Forecast", tooltip=paste0("budget: ", forecast*input$unit_price)), size=2) +
      labs(x = "Date", y = "$$") +
      scale_color_manual(values = c("Actual" = "red", "Budget" = "green",
                                    "Forecast" = "blue", "Last Year" ="orange",
                                    "Projected" = "pink", "Last Year (MTD)" = "yellow")) +
      theme(axis.title.x = element_text(size = 12,
                                        color = "blue",
                                        face = "bold"),
            axis.title.y = element_text(size = 12,
                                        color = "red",
                                        face = "italic")) +
      scale_x_date(date_breaks = "1 month", date_labels = "%Y-%m") +
      theme(panel.grid.minor.x = element_blank()) +
      scale_y_continuous(labels = label_comma(),
                         limits = c(0, 1500 * input$unit_price))

    girafe(code = print(gg), width=1, width_svg = 8)
  })


  # Downloadable csv of selected dataset ----
  output$downloadData <- downloadHandler(
    filename = function() {
      paste("sales_data", ".csv", sep = "")
    },
    content = function(file) {
      write.csv(myData(), file, row.names = FALSE)
    }
  )

  runjs("$('#mainTab li:contains(\"Data Table\")').hide();")})











