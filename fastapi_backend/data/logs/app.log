2025-07-03 15:43:18 - app.startup - INFO - logging_config.py:120 - setup_logging - ==================================================
2025-07-03 15:43:18 - app.startup - INFO - logging_config.py:121 - setup_logging - Stramit Data Export API Starting
2025-07-03 15:43:18 - app.startup - INFO - logging_config.py:122 - setup_logging - Timestamp: 2025-07-03T15:43:18.645196
2025-07-03 15:43:18 - app.startup - INFO - logging_config.py:123 - setup_logging - Debug Mode: True
2025-07-03 15:43:18 - app.startup - INFO - logging_config.py:124 - setup_logging - Log Directory: data/logs
2025-07-03 15:43:18 - app.startup - INFO - logging_config.py:125 - setup_logging - ==================================================
2025-07-03 15:43:18 - uvicorn.error - INFO - server.py:84 - _serve - Started server process [50669]
2025-07-03 15:43:18 - uvicorn.error - INFO - on.py:48 - startup - Waiting for application startup.
2025-07-03 15:43:18 - app.main - INFO - main.py:38 - lifespan - Starting FastAPI application...
2025-07-03 15:43:18 - app.main - INFO - main.py:42 - lifespan - Testing database connections...
2025-07-03 15:43:18 - app.config.database - INFO - database.py:40 - get_connection - Connecting to cdb database at localhost:3306
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.main - WARNING - main.py:51 - lifespan - Database connection 'cdb' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app database at localhost:3306
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.config.database - INFO - database.py:40 - get_connection - Connecting to reporting database at localhost:3306
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.main - WARNING - main.py:51 - lifespan - Database connection 'reporting' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app_secondary database at localhost:3306
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app_secondary' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.main - INFO - main.py:54 - lifespan - Testing Redis connection...
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 15:43:18 - app.main - WARNING - main.py:59 - lifespan - Redis connection - FAILED
2025-07-03 15:43:18 - app.main - INFO - main.py:61 - lifespan - Application startup completed successfully
2025-07-03 15:43:18 - uvicorn.error - INFO - on.py:62 - startup - Application startup complete.
2025-07-03 15:43:44 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:44 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:44 - app.requests - INFO - security.py:220 - dispatch - GET / - Status: 200 - Time: 0.000s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 15:43:44 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:43:44 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:44 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:44 - app.requests - INFO - security.py:220 - dispatch - GET /favicon.ico - Status: 404 - Time: 0.000s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 15:43:44 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:43:48 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:48 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:48 - app.requests - INFO - security.py:220 - dispatch - GET /docs - Status: 200 - Time: 0.000s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 15:43:48 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:43:53 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:53 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:53 - app.requests - INFO - security.py:220 - dispatch - GET /docs - Status: 200 - Time: 0.001s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 15:43:53 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:44:06 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:06 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:06 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 15:44:06 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 15:44:06 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 15:44:06 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 15:44:06 - app.config.database - INFO - database.py:40 - get_connection - Connecting to cdb database at localhost:3306
2025-07-03 15:44:06 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:44:06 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app database at localhost:3306
2025-07-03 15:44:06 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:44:06 - app.config.database - INFO - database.py:40 - get_connection - Connecting to reporting database at localhost:3306
2025-07-03 15:44:06 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:44:06 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app_secondary database at localhost:3306
2025-07-03 15:44:06 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:44:06 - app.requests - INFO - security.py:220 - dispatch - GET /health - Status: 200 - Time: 0.003s - User: anonymous - IP: 127.0.0.1 - UA: curl/8.7.1
2025-07-03 15:44:06 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:44:21 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:21 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:21 - app.requests - INFO - security.py:220 - dispatch - GET /docs - Status: 200 - Time: 0.000s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 15:44:21 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:44:22 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:22 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:22 - app.requests - INFO - security.py:220 - dispatch - GET /docs - Status: 200 - Time: 0.000s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 15:44:22 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:44:26 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:26 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:26 - app.requests - INFO - security.py:220 - dispatch - GET /.well-known/appspecific/com.chrome.devtools.json - Status: 404 - Time: 0.001s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 15:44:26 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:52:21 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:52:21 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:52:21 - app.requests - INFO - security.py:220 - dispatch - GET /docs - Status: 200 - Time: 0.001s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 15:52:21 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:52:21 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:52:21 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:52:21 - app.requests - INFO - security.py:220 - dispatch - GET /.well-known/appspecific/com.chrome.devtools.json - Status: 404 - Time: 0.001s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 15:52:21 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:53:19 - uvicorn.error - INFO - server.py:264 - shutdown - Shutting down
2025-07-03 15:53:19 - uvicorn.error - INFO - on.py:67 - shutdown - Waiting for application shutdown.
2025-07-03 15:53:19 - app.main - INFO - main.py:70 - lifespan - Shutting down FastAPI application...
2025-07-03 15:53:19 - app.main - ERROR - main.py:81 - lifespan - Error during shutdown: 'DatabaseManager' object has no attribute 'close_all_connections'
2025-07-03 15:53:19 - app.main - INFO - main.py:83 - lifespan - Application shutdown completed
2025-07-03 15:53:19 - uvicorn.error - INFO - on.py:76 - shutdown - Application shutdown complete.
2025-07-03 15:53:19 - uvicorn.error - INFO - server.py:94 - _serve - Finished server process [50669]
2025-07-03 15:53:19 - app.startup - INFO - logging_config.py:120 - setup_logging - ==================================================
2025-07-03 15:53:19 - app.startup - INFO - logging_config.py:121 - setup_logging - Stramit Data Export API Starting
2025-07-03 15:53:19 - app.startup - INFO - logging_config.py:122 - setup_logging - Timestamp: 2025-07-03T15:53:19.865413
2025-07-03 15:53:19 - app.startup - INFO - logging_config.py:123 - setup_logging - Debug Mode: True
2025-07-03 15:53:19 - app.startup - INFO - logging_config.py:124 - setup_logging - Log Directory: data/logs
2025-07-03 15:53:19 - app.startup - INFO - logging_config.py:125 - setup_logging - ==================================================
2025-07-03 15:53:19 - uvicorn.error - INFO - server.py:84 - _serve - Started server process [50826]
2025-07-03 15:53:19 - uvicorn.error - INFO - on.py:48 - startup - Waiting for application startup.
2025-07-03 15:53:19 - app.main - INFO - main.py:38 - lifespan - Starting FastAPI application...
2025-07-03 15:53:19 - app.main - INFO - main.py:42 - lifespan - Testing database connections...
2025-07-03 15:53:19 - app.config.database - INFO - database.py:40 - get_connection - Connecting to cdb database at localhost:3306
2025-07-03 15:53:19 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:53:19 - app.main - WARNING - main.py:51 - lifespan - Database connection 'cdb' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:53:19 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app database at localhost:3306
2025-07-03 15:53:19 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:53:19 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:53:19 - app.config.database - INFO - database.py:40 - get_connection - Connecting to reporting database at localhost:3306
2025-07-03 15:53:19 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:53:19 - app.main - WARNING - main.py:51 - lifespan - Database connection 'reporting' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:53:19 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app_secondary database at localhost:3306
2025-07-03 15:53:19 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:53:19 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app_secondary' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:53:19 - app.main - INFO - main.py:54 - lifespan - Testing Redis connection...
2025-07-03 15:53:19 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 15:53:19 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 15:53:19 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 15:53:19 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 15:53:19 - app.main - WARNING - main.py:59 - lifespan - Redis connection - FAILED
2025-07-03 15:53:19 - app.main - INFO - main.py:61 - lifespan - Application startup completed successfully
2025-07-03 15:53:19 - uvicorn.error - INFO - on.py:62 - startup - Application startup complete.
2025-07-03 15:53:34 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:53:34 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:53:34 - app.requests - INFO - security.py:220 - dispatch - GET /docs - Status: 200 - Time: 0.000s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 15:53:34 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:53:38 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:53:38 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:53:38 - app.requests - INFO - security.py:220 - dispatch - GET /openapi.json - Status: 200 - Time: 0.013s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 15:53:38 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:54:02 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:54:02 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:54:02 - app.requests - INFO - security.py:220 - dispatch - GET / - Status: 200 - Time: 0.000s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 15:54:02 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 16:26:12 - uvicorn.error - INFO - server.py:264 - shutdown - Shutting down
2025-07-03 16:26:12 - uvicorn.error - INFO - on.py:67 - shutdown - Waiting for application shutdown.
2025-07-03 16:26:12 - app.main - INFO - main.py:70 - lifespan - Shutting down FastAPI application...
2025-07-03 16:26:12 - app.main - ERROR - main.py:81 - lifespan - Error during shutdown: 'DatabaseManager' object has no attribute 'close_all_connections'
2025-07-03 16:26:12 - app.main - INFO - main.py:83 - lifespan - Application shutdown completed
2025-07-03 16:26:12 - uvicorn.error - INFO - on.py:76 - shutdown - Application shutdown complete.
2025-07-03 16:26:12 - uvicorn.error - INFO - server.py:94 - _serve - Finished server process [50826]
2025-07-03 16:26:13 - app.startup - INFO - logging_config.py:120 - setup_logging - ==================================================
2025-07-03 16:26:13 - app.startup - INFO - logging_config.py:121 - setup_logging - Stramit Data Export API Starting
2025-07-03 16:26:13 - app.startup - INFO - logging_config.py:122 - setup_logging - Timestamp: 2025-07-03T16:26:13.110870
2025-07-03 16:26:13 - app.startup - INFO - logging_config.py:123 - setup_logging - Debug Mode: True
2025-07-03 16:26:13 - app.startup - INFO - logging_config.py:124 - setup_logging - Log Directory: data/logs
2025-07-03 16:26:13 - app.startup - INFO - logging_config.py:125 - setup_logging - ==================================================
2025-07-03 16:26:13 - uvicorn.error - INFO - server.py:84 - _serve - Started server process [51160]
2025-07-03 16:26:13 - uvicorn.error - INFO - on.py:48 - startup - Waiting for application startup.
2025-07-03 16:26:13 - app.main - INFO - main.py:38 - lifespan - Starting FastAPI application...
2025-07-03 16:26:13 - app.main - INFO - main.py:42 - lifespan - Testing database connections...
2025-07-03 16:26:13 - app.config.database - INFO - database.py:40 - get_connection - Connecting to cdb database at localhost:3306
2025-07-03 16:26:13 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:13 - app.main - WARNING - main.py:51 - lifespan - Database connection 'cdb' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:13 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app database at localhost:3306
2025-07-03 16:26:13 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:13 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:13 - app.config.database - INFO - database.py:40 - get_connection - Connecting to reporting database at localhost:3306
2025-07-03 16:26:13 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:13 - app.main - WARNING - main.py:51 - lifespan - Database connection 'reporting' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:13 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app_secondary database at localhost:3306
2025-07-03 16:26:13 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:13 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app_secondary' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:13 - app.main - INFO - main.py:54 - lifespan - Testing Redis connection...
2025-07-03 16:26:13 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 16:26:13 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 16:26:13 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 16:26:13 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 16:26:13 - app.main - WARNING - main.py:59 - lifespan - Redis connection - FAILED
2025-07-03 16:26:13 - app.main - INFO - main.py:61 - lifespan - Application startup completed successfully
2025-07-03 16:26:13 - uvicorn.error - INFO - on.py:62 - startup - Application startup complete.
2025-07-03 16:26:27 - uvicorn.error - INFO - server.py:264 - shutdown - Shutting down
2025-07-03 16:26:27 - uvicorn.error - INFO - on.py:67 - shutdown - Waiting for application shutdown.
2025-07-03 16:26:27 - app.main - INFO - main.py:70 - lifespan - Shutting down FastAPI application...
2025-07-03 16:26:27 - app.main - ERROR - main.py:81 - lifespan - Error during shutdown: 'DatabaseManager' object has no attribute 'close_all_connections'
2025-07-03 16:26:27 - app.main - INFO - main.py:83 - lifespan - Application shutdown completed
2025-07-03 16:26:27 - uvicorn.error - INFO - on.py:76 - shutdown - Application shutdown complete.
2025-07-03 16:26:27 - uvicorn.error - INFO - server.py:94 - _serve - Finished server process [51160]
2025-07-03 16:26:28 - app.startup - INFO - logging_config.py:120 - setup_logging - ==================================================
2025-07-03 16:26:28 - app.startup - INFO - logging_config.py:121 - setup_logging - Stramit Data Export API Starting
2025-07-03 16:26:28 - app.startup - INFO - logging_config.py:122 - setup_logging - Timestamp: 2025-07-03T16:26:28.236983
2025-07-03 16:26:28 - app.startup - INFO - logging_config.py:123 - setup_logging - Debug Mode: True
2025-07-03 16:26:28 - app.startup - INFO - logging_config.py:124 - setup_logging - Log Directory: data/logs
2025-07-03 16:26:28 - app.startup - INFO - logging_config.py:125 - setup_logging - ==================================================
2025-07-03 16:26:28 - uvicorn.error - INFO - server.py:84 - _serve - Started server process [51177]
2025-07-03 16:26:28 - uvicorn.error - INFO - on.py:48 - startup - Waiting for application startup.
2025-07-03 16:26:28 - app.main - INFO - main.py:38 - lifespan - Starting FastAPI application...
2025-07-03 16:26:28 - app.main - INFO - main.py:42 - lifespan - Testing database connections...
2025-07-03 16:26:28 - app.config.database - INFO - database.py:40 - get_connection - Connecting to cdb database at localhost:3306
2025-07-03 16:26:28 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:28 - app.main - WARNING - main.py:51 - lifespan - Database connection 'cdb' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:28 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app database at localhost:3306
2025-07-03 16:26:28 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:28 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:28 - app.config.database - INFO - database.py:40 - get_connection - Connecting to reporting database at localhost:3306
2025-07-03 16:26:28 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:28 - app.main - WARNING - main.py:51 - lifespan - Database connection 'reporting' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:28 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app_secondary database at localhost:3306
2025-07-03 16:26:28 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:28 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app_secondary' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:28 - app.main - INFO - main.py:54 - lifespan - Testing Redis connection...
2025-07-03 16:26:28 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 16:26:28 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 16:26:28 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 16:26:28 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 16:26:28 - app.main - WARNING - main.py:59 - lifespan - Redis connection - FAILED
2025-07-03 16:26:28 - app.main - INFO - main.py:61 - lifespan - Application startup completed successfully
2025-07-03 16:26:28 - uvicorn.error - INFO - on.py:62 - startup - Application startup complete.
2025-07-03 16:26:35 - uvicorn.error - INFO - server.py:264 - shutdown - Shutting down
2025-07-03 16:26:35 - uvicorn.error - INFO - on.py:67 - shutdown - Waiting for application shutdown.
2025-07-03 16:26:35 - app.main - INFO - main.py:70 - lifespan - Shutting down FastAPI application...
2025-07-03 16:26:35 - app.main - ERROR - main.py:81 - lifespan - Error during shutdown: 'DatabaseManager' object has no attribute 'close_all_connections'
2025-07-03 16:26:35 - app.main - INFO - main.py:83 - lifespan - Application shutdown completed
2025-07-03 16:26:35 - uvicorn.error - INFO - on.py:76 - shutdown - Application shutdown complete.
2025-07-03 16:26:35 - uvicorn.error - INFO - server.py:94 - _serve - Finished server process [51177]
2025-07-03 16:26:35 - app.startup - INFO - logging_config.py:120 - setup_logging - ==================================================
2025-07-03 16:26:35 - app.startup - INFO - logging_config.py:121 - setup_logging - Stramit Data Export API Starting
2025-07-03 16:26:35 - app.startup - INFO - logging_config.py:122 - setup_logging - Timestamp: 2025-07-03T16:26:35.899330
2025-07-03 16:26:35 - app.startup - INFO - logging_config.py:123 - setup_logging - Debug Mode: True
2025-07-03 16:26:35 - app.startup - INFO - logging_config.py:124 - setup_logging - Log Directory: data/logs
2025-07-03 16:26:35 - app.startup - INFO - logging_config.py:125 - setup_logging - ==================================================
2025-07-03 16:26:35 - uvicorn.error - INFO - server.py:84 - _serve - Started server process [51182]
2025-07-03 16:26:35 - uvicorn.error - INFO - on.py:48 - startup - Waiting for application startup.
2025-07-03 16:26:35 - app.main - INFO - main.py:38 - lifespan - Starting FastAPI application...
2025-07-03 16:26:35 - app.main - INFO - main.py:42 - lifespan - Testing database connections...
2025-07-03 16:26:35 - app.config.database - INFO - database.py:40 - get_connection - Connecting to cdb database at localhost:3306
2025-07-03 16:26:35 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:35 - app.main - WARNING - main.py:51 - lifespan - Database connection 'cdb' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:35 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app database at localhost:3306
2025-07-03 16:26:35 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:35 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:35 - app.config.database - INFO - database.py:40 - get_connection - Connecting to reporting database at localhost:3306
2025-07-03 16:26:35 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:35 - app.main - WARNING - main.py:51 - lifespan - Database connection 'reporting' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:35 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app_secondary database at localhost:3306
2025-07-03 16:26:35 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:35 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app_secondary' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:35 - app.main - INFO - main.py:54 - lifespan - Testing Redis connection...
2025-07-03 16:26:35 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 16:26:35 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 16:26:35 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 16:26:35 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 16:26:35 - app.main - WARNING - main.py:59 - lifespan - Redis connection - FAILED
2025-07-03 16:26:35 - app.main - INFO - main.py:61 - lifespan - Application startup completed successfully
2025-07-03 16:26:35 - uvicorn.error - INFO - on.py:62 - startup - Application startup complete.
2025-07-03 16:27:04 - uvicorn.error - INFO - server.py:264 - shutdown - Shutting down
2025-07-03 16:27:04 - uvicorn.error - INFO - on.py:67 - shutdown - Waiting for application shutdown.
2025-07-03 16:27:04 - app.main - INFO - main.py:70 - lifespan - Shutting down FastAPI application...
2025-07-03 16:27:04 - app.main - ERROR - main.py:81 - lifespan - Error during shutdown: 'DatabaseManager' object has no attribute 'close_all_connections'
2025-07-03 16:27:04 - app.main - INFO - main.py:83 - lifespan - Application shutdown completed
2025-07-03 16:27:04 - uvicorn.error - INFO - on.py:76 - shutdown - Application shutdown complete.
2025-07-03 16:27:04 - uvicorn.error - INFO - server.py:94 - _serve - Finished server process [51182]
2025-07-03 16:27:05 - app.startup - INFO - logging_config.py:120 - setup_logging - ==================================================
2025-07-03 16:27:05 - app.startup - INFO - logging_config.py:121 - setup_logging - Stramit Data Export API Starting
2025-07-03 16:27:05 - app.startup - INFO - logging_config.py:122 - setup_logging - Timestamp: 2025-07-03T16:27:05.101547
2025-07-03 16:27:05 - app.startup - INFO - logging_config.py:123 - setup_logging - Debug Mode: True
2025-07-03 16:27:05 - app.startup - INFO - logging_config.py:124 - setup_logging - Log Directory: data/logs
2025-07-03 16:27:05 - app.startup - INFO - logging_config.py:125 - setup_logging - ==================================================
2025-07-03 16:27:05 - uvicorn.error - INFO - server.py:84 - _serve - Started server process [51185]
2025-07-03 16:27:05 - uvicorn.error - INFO - on.py:48 - startup - Waiting for application startup.
2025-07-03 16:27:05 - app.main - INFO - main.py:38 - lifespan - Starting FastAPI application...
2025-07-03 16:27:05 - app.main - INFO - main.py:42 - lifespan - Testing database connections...
2025-07-03 16:27:05 - app.config.database - INFO - database.py:40 - get_connection - Connecting to cdb database at localhost:3306
2025-07-03 16:27:05 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:27:05 - app.main - WARNING - main.py:51 - lifespan - Database connection 'cdb' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:27:05 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app database at localhost:3306
2025-07-03 16:27:05 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:27:05 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:27:05 - app.config.database - INFO - database.py:40 - get_connection - Connecting to reporting database at localhost:3306
2025-07-03 16:27:05 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:27:05 - app.main - WARNING - main.py:51 - lifespan - Database connection 'reporting' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:27:05 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app_secondary database at localhost:3306
2025-07-03 16:27:05 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:27:05 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app_secondary' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:27:05 - app.main - INFO - main.py:54 - lifespan - Testing Redis connection...
2025-07-03 16:27:05 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 16:27:05 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 16:27:05 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 16:27:05 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 16:27:05 - app.main - WARNING - main.py:59 - lifespan - Redis connection - FAILED
2025-07-03 16:27:05 - app.main - INFO - main.py:61 - lifespan - Application startup completed successfully
2025-07-03 16:27:05 - uvicorn.error - INFO - on.py:62 - startup - Application startup complete.
2025-07-03 16:27:52 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:27:52 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:27:52 - passlib.handlers.bcrypt - WARNING - bcrypt.py:622 - _load_backend_mixin - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-03 16:27:52 - app.utils.security - INFO - security.py:115 - create_user - User created successfully: testuser
2025-07-03 16:27:52 - app.utils.security - INFO - security.py:37 - _load_users - Loading users from data/users.json
2025-07-03 16:27:52 - app.utils.security - INFO - security.py:55 - _load_users - Loaded 3 users
2025-07-03 16:27:52 - app.utils.security - INFO - security.py:79 - authenticate_user - User authenticated successfully: testuser
2025-07-03 16:27:52 - app.auth.auth - INFO - auth.py:55 - authenticate_and_create_token - Token created successfully for user: testuser
2025-07-03 16:27:52 - app.auth.auth - INFO - auth.py:149 - register_user - User registered successfully: testuser
2025-07-03 16:27:52 - app.api.routes.auth - INFO - auth.py:71 - register - User registered successfully: testuser
2025-07-03 16:27:52 - app.requests - INFO - security.py:220 - dispatch - POST /auth/register - Status: 200 - Time: 0.357s - User: anonymous - IP: 127.0.0.1 - UA: curl/8.7.1
2025-07-03 16:27:52 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 16:30:16 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:30:16 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:30:16 - app.utils.security - WARNING - security.py:96 - create_user - User already exists: testuser
2025-07-03 16:30:16 - app.auth.auth - WARNING - auth.py:141 - register_user - User registration failed for: testuser
2025-07-03 16:30:16 - app.api.routes.auth - WARNING - auth.py:65 - register - Registration failed for user: testuser
2025-07-03 16:30:16 - app.main - WARNING - main.py:129 - http_exception_handler - HTTP 400: Username already exists or registration failed - http://localhost:8000/auth/register
2025-07-03 16:30:16 - app.requests - INFO - security.py:220 - dispatch - POST /auth/register - Status: 400 - Time: 0.001s - User: anonymous - IP: 127.0.0.1 - UA: curl/8.7.1
2025-07-03 16:30:16 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 16:32:38 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:38 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:38 - app.requests - INFO - security.py:220 - dispatch - GET /docs - Status: 200 - Time: 0.001s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 16:32:38 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 16:32:38 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:38 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:38 - app.requests - INFO - security.py:220 - dispatch - GET /openapi.json - Status: 200 - Time: 0.008s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 16:32:38 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 16:32:41 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:41 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:41 - app.requests - INFO - security.py:220 - dispatch - GET /docs - Status: 200 - Time: 0.000s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 16:32:41 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 16:32:41 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:41 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:41 - app.requests - INFO - security.py:220 - dispatch - GET /openapi.json - Status: 200 - Time: 0.001s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 16:32:41 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 16:32:42 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:42 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:42 - app.requests - INFO - security.py:220 - dispatch - GET /docs - Status: 200 - Time: 0.000s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 16:32:42 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 16:32:42 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:42 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:42 - app.requests - INFO - security.py:220 - dispatch - GET /openapi.json - Status: 200 - Time: 0.001s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 16:32:42 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 16:32:42 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:42 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:42 - app.requests - INFO - security.py:220 - dispatch - GET /docs - Status: 200 - Time: 0.001s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 16:32:42 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 16:32:42 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:42 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 16:32:42 - app.requests - INFO - security.py:220 - dispatch - GET /openapi.json - Status: 200 - Time: 0.001s - User: anonymous - IP: 127.0.0.1 - UA: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.
2025-07-03 16:32:42 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 16:52:28 - uvicorn.error - INFO - server.py:264 - shutdown - Shutting down
2025-07-03 16:52:28 - uvicorn.error - INFO - on.py:67 - shutdown - Waiting for application shutdown.
2025-07-03 16:52:28 - app.main - INFO - main.py:70 - lifespan - Shutting down FastAPI application...
2025-07-03 16:52:28 - app.main - ERROR - main.py:81 - lifespan - Error during shutdown: 'DatabaseManager' object has no attribute 'close_all_connections'
2025-07-03 16:52:28 - app.main - INFO - main.py:83 - lifespan - Application shutdown completed
2025-07-03 16:52:28 - uvicorn.error - INFO - on.py:76 - shutdown - Application shutdown complete.
2025-07-03 16:52:28 - uvicorn.error - INFO - server.py:94 - _serve - Finished server process [51185]
2025-07-03 16:52:28 - app.startup - INFO - logging_config.py:120 - setup_logging - ==================================================
2025-07-03 16:52:28 - app.startup - INFO - logging_config.py:121 - setup_logging - Stramit Data Export API Starting
2025-07-03 16:52:28 - app.startup - INFO - logging_config.py:122 - setup_logging - Timestamp: 2025-07-03T16:52:28.781003
2025-07-03 16:52:28 - app.startup - INFO - logging_config.py:123 - setup_logging - Debug Mode: True
2025-07-03 16:52:28 - app.startup - INFO - logging_config.py:124 - setup_logging - Log Directory: data/logs
2025-07-03 16:52:28 - app.startup - INFO - logging_config.py:125 - setup_logging - ==================================================
2025-07-03 16:52:28 - uvicorn.error - INFO - server.py:84 - _serve - Started server process [51749]
2025-07-03 16:52:28 - uvicorn.error - INFO - on.py:48 - startup - Waiting for application startup.
2025-07-03 16:52:28 - app.main - INFO - main.py:38 - lifespan - Starting FastAPI application...
2025-07-03 16:52:28 - app.main - INFO - main.py:42 - lifespan - Testing database connections...
2025-07-03 16:52:28 - app.config.database - INFO - database.py:40 - get_connection - Connecting to cdb database at localhost:3306
2025-07-03 16:52:28 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:28 - app.main - WARNING - main.py:51 - lifespan - Database connection 'cdb' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:28 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app database at localhost:3306
2025-07-03 16:52:28 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:28 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:28 - app.config.database - INFO - database.py:40 - get_connection - Connecting to reporting database at localhost:3306
2025-07-03 16:52:28 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:28 - app.main - WARNING - main.py:51 - lifespan - Database connection 'reporting' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:28 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app_secondary database at localhost:3306
2025-07-03 16:52:28 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:28 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app_secondary' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:28 - app.main - INFO - main.py:54 - lifespan - Testing Redis connection...
2025-07-03 16:52:28 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 16:52:28 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 16:52:28 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 16:52:28 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 16:52:28 - app.main - WARNING - main.py:59 - lifespan - Redis connection - FAILED
2025-07-03 16:52:28 - app.main - INFO - main.py:61 - lifespan - Application startup completed successfully
2025-07-03 16:52:28 - uvicorn.error - INFO - on.py:62 - startup - Application startup complete.
2025-07-03 16:52:32 - uvicorn.error - INFO - server.py:264 - shutdown - Shutting down
2025-07-03 16:52:32 - uvicorn.error - INFO - on.py:67 - shutdown - Waiting for application shutdown.
2025-07-03 16:52:32 - app.main - INFO - main.py:70 - lifespan - Shutting down FastAPI application...
2025-07-03 16:52:32 - app.main - ERROR - main.py:81 - lifespan - Error during shutdown: 'DatabaseManager' object has no attribute 'close_all_connections'
2025-07-03 16:52:32 - app.main - INFO - main.py:83 - lifespan - Application shutdown completed
2025-07-03 16:52:32 - uvicorn.error - INFO - on.py:76 - shutdown - Application shutdown complete.
2025-07-03 16:52:32 - uvicorn.error - INFO - server.py:94 - _serve - Finished server process [51749]
2025-07-03 16:52:32 - app.startup - INFO - logging_config.py:120 - setup_logging - ==================================================
2025-07-03 16:52:32 - app.startup - INFO - logging_config.py:121 - setup_logging - Stramit Data Export API Starting
2025-07-03 16:52:32 - app.startup - INFO - logging_config.py:122 - setup_logging - Timestamp: 2025-07-03T16:52:32.914863
2025-07-03 16:52:32 - app.startup - INFO - logging_config.py:123 - setup_logging - Debug Mode: True
2025-07-03 16:52:32 - app.startup - INFO - logging_config.py:124 - setup_logging - Log Directory: data/logs
2025-07-03 16:52:32 - app.startup - INFO - logging_config.py:125 - setup_logging - ==================================================
2025-07-03 16:52:32 - uvicorn.error - INFO - server.py:84 - _serve - Started server process [51809]
2025-07-03 16:52:32 - uvicorn.error - INFO - on.py:48 - startup - Waiting for application startup.
2025-07-03 16:52:32 - app.main - INFO - main.py:38 - lifespan - Starting FastAPI application...
2025-07-03 16:52:32 - app.main - INFO - main.py:42 - lifespan - Testing database connections...
2025-07-03 16:52:32 - app.config.database - INFO - database.py:40 - get_connection - Connecting to cdb database at localhost:3306
2025-07-03 16:52:32 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:32 - app.main - WARNING - main.py:51 - lifespan - Database connection 'cdb' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:32 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app database at localhost:3306
2025-07-03 16:52:32 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:32 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:32 - app.config.database - INFO - database.py:40 - get_connection - Connecting to reporting database at localhost:3306
2025-07-03 16:52:32 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:32 - app.main - WARNING - main.py:51 - lifespan - Database connection 'reporting' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:32 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app_secondary database at localhost:3306
2025-07-03 16:52:32 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:32 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app_secondary' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:52:32 - app.main - INFO - main.py:54 - lifespan - Testing Redis connection...
2025-07-03 16:52:32 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 16:52:32 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 16:52:32 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 16:52:32 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 16:52:32 - app.main - WARNING - main.py:59 - lifespan - Redis connection - FAILED
2025-07-03 16:52:32 - app.main - INFO - main.py:61 - lifespan - Application startup completed successfully
2025-07-03 16:52:32 - uvicorn.error - INFO - on.py:62 - startup - Application startup complete.
