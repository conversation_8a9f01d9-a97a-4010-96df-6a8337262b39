2025-07-03 15:43:18 - app.startup - INFO - logging_config.py:120 - setup_logging - ==================================================
2025-07-03 15:43:18 - app.startup - INFO - logging_config.py:121 - setup_logging - Stramit Data Export API Starting
2025-07-03 15:43:18 - app.startup - INFO - logging_config.py:122 - setup_logging - Timestamp: 2025-07-03T15:43:18.645196
2025-07-03 15:43:18 - app.startup - INFO - logging_config.py:123 - setup_logging - Debug Mode: True
2025-07-03 15:43:18 - app.startup - INFO - logging_config.py:124 - setup_logging - Log Directory: data/logs
2025-07-03 15:43:18 - app.startup - INFO - logging_config.py:125 - setup_logging - ==================================================
2025-07-03 15:43:18 - uvicorn.error - INFO - server.py:84 - _serve - Started server process [50669]
2025-07-03 15:43:18 - uvicorn.error - INFO - on.py:48 - startup - Waiting for application startup.
2025-07-03 15:43:18 - app.main - INFO - main.py:38 - lifespan - Starting FastAPI application...
2025-07-03 15:43:18 - app.main - INFO - main.py:42 - lifespan - Testing database connections...
2025-07-03 15:43:18 - app.config.database - INFO - database.py:40 - get_connection - Connecting to cdb database at localhost:3306
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.main - WARNING - main.py:51 - lifespan - Database connection 'cdb' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app database at localhost:3306
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.config.database - INFO - database.py:40 - get_connection - Connecting to reporting database at localhost:3306
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.main - WARNING - main.py:51 - lifespan - Database connection 'reporting' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.config.database - INFO - database.py:40 - get_connection - Connecting to app_secondary database at localhost:3306
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.main - WARNING - main.py:51 - lifespan - Database connection 'app_secondary' - FAILED: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.main - INFO - main.py:54 - lifespan - Testing Redis connection...
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 15:43:18 - app.main - WARNING - main.py:59 - lifespan - Redis connection - FAILED
2025-07-03 15:43:18 - app.main - INFO - main.py:61 - lifespan - Application startup completed successfully
2025-07-03 15:43:18 - uvicorn.error - INFO - on.py:62 - startup - Application startup complete.
