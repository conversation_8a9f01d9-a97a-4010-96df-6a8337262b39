2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 15:43:44 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:44 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:44 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:43:44 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:44 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:44 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:43:48 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:48 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:48 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:43:53 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:53 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:43:53 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:44:06 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:06 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:06 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 15:44:06 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 15:44:06 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 15:44:06 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 15:44:06 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:44:06 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:44:06 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:44:06 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:44:06 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:44:21 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:21 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:21 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:44:22 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:22 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:22 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:44:26 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:26 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:44:26 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:52:21 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:52:21 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:52:21 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:52:21 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:52:21 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:52:21 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:53:19 - app.main - ERROR - main.py:81 - lifespan - Error during shutdown: 'DatabaseManager' object has no attribute 'close_all_connections'
2025-07-03 15:53:19 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:53:19 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:53:19 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:53:19 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:53:19 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 15:53:19 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 15:53:19 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 15:53:19 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 15:53:34 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:53:34 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:53:34 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:53:38 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:53:38 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:53:38 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 15:54:02 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:54:02 - app.middleware.security - ERROR - security.py:129 - _increment_counter - Failed to increment rate limit counter: 'NoneType' object has no attribute 'pipeline'
2025-07-03 15:54:02 - app.middleware.security - ERROR - security.py:156 - _add_rate_limit_headers - Failed to add rate limit headers: 'NoneType' object has no attribute 'get'
2025-07-03 16:26:12 - app.main - ERROR - main.py:81 - lifespan - Error during shutdown: 'DatabaseManager' object has no attribute 'close_all_connections'
2025-07-03 16:26:13 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:13 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:13 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:13 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:13 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 16:26:13 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 16:26:13 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 16:26:13 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 16:26:27 - app.main - ERROR - main.py:81 - lifespan - Error during shutdown: 'DatabaseManager' object has no attribute 'close_all_connections'
2025-07-03 16:26:28 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:28 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:28 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:28 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:28 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 16:26:28 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 16:26:28 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 16:26:28 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 16:26:35 - app.main - ERROR - main.py:81 - lifespan - Error during shutdown: 'DatabaseManager' object has no attribute 'close_all_connections'
2025-07-03 16:26:35 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:35 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:35 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:35 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:26:35 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 16:26:35 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 16:26:35 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 16:26:35 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
2025-07-03 16:27:04 - app.main - ERROR - main.py:81 - lifespan - Error during shutdown: 'DatabaseManager' object has no attribute 'close_all_connections'
2025-07-03 16:27:05 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:27:05 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:27:05 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:27:05 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 16:27:05 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 16:27:05 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 16:27:05 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 16:27:05 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
