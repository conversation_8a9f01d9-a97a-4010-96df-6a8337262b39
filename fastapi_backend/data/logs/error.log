2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for cdb: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for reporting: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.config.database - ERROR - database.py:51 - get_connection - Database connection error for app_secondary: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (61)
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:215 - set_json - Failed to cache JSON: 'NoneType' object has no attribute 'setex'
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:229 - get_json - Failed to retrieve cached JSON: 'NoneType' object has no attribute 'get'
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:135 - delete_cache - Failed to delete cache key health_check_test: 'NoneType' object has no attribute 'delete'
2025-07-03 15:43:18 - app.services.redis_service - ERROR - redis_service.py:319 - health_check - Redis health check failed: 'NoneType' object has no attribute 'info'
