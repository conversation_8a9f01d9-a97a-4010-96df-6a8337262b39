# FastAPI Backend Environment Configuration
# Copy this file to .env and update with your actual values

# Application Settings
DEBUG=false
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
HOST=0.0.0.0
PORT=8000

# Database Configuration - CDB
CDB_HOST=localhost
CDB_PORT=3306
CDB_DATABASE=CDB
CDB_USERNAME=your_username
CDB_PASSWORD=your_password

# Database Configuration - App
APP_HOST=localhost
APP_PORT=3306
APP_DATABASE=app
APP_USERNAME=your_username
APP_PASSWORD=your_password

# Database Configuration - Reporting
REPORTING_HOST=localhost
REPORTING_PORT=3306
REPORTING_DATABASE=ReportingDB
REPORTING_USERNAME=your_username
REPORTING_PASSWORD=your_password

# Database Configuration - App Secondary
APP_SECONDARY_HOST=localhost
APP_SECONDARY_PORT=3306
APP_SECONDARY_DATABASE=app
APP_SECONDARY_USERNAME=your_username
APP_SECONDARY_PASSWORD=your_password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password_123
REDIS_DB=0

# CORS Settings
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000
ALLOWED_HOSTS=localhost,127.0.0.1

# Security Settings
ADMIN_IP_WHITELIST=127.0.0.1,::1
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# File Paths
EXPORT_FILES_PATH=./data/exports
LOG_FILES_PATH=./data/logs
CACHE_FILES_PATH=./data/cache
USER_DATA_FILE=./data/users.json

# Cache TTL Settings (in seconds)
CACHE_TTL_SALES=1800        # 30 minutes
CACHE_TTL_HEATMAP=3600      # 1 hour
CACHE_TTL_POSTCODES=86400   # 24 hours
CACHE_TTL_SESSIONS=86400    # 24 hours

# Export Settings
EXPORT_MAX_FILE_SIZE=104857600  # 100MB
EXPORT_CLEANUP_DAYS=7
EXPORT_MAX_CONCURRENT_JOBS=5

# Logging Settings
LOG_LEVEL=INFO
LOG_MAX_SIZE=10485760       # 10MB
LOG_BACKUP_COUNT=5
