# Data API Routes
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional, List
import logging
from datetime import date

from ...auth.dependencies import get_current_active_user
from ...auth.models import User
from ...services.data_service import data_service
from ...models.data import (
    SalesDataRequest, SalesDataResponse,
    CustomerDataRequest, CustomerDataResponse,
    LeadsDataRequest, LeadsDataResponse,
    HeatmapDataRequest, HeatmapDataResponse,
    DistributorLocationsResponse, PostcodesResponse,
    APIResponse, HeatmapType
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api", tags=["Data"], dependencies=[Depends(get_current_active_user)])


@router.get("/sales-data", response_model=SalesDataResponse, summary="Get Sales Data")
async def get_sales_data(
    start_date: Optional[date] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="End date (YYYY-MM-DD)"),
    category: Optional[str] = Query(None, description="Sales category filter"),
    distributor_id: Optional[str] = Query(None, description="Distributor ID filter"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get sales data with optional filtering.
    
    - **start_date**: Start date for data extraction (defaults to 1 year ago)
    - **end_date**: End date for data extraction (defaults to today)
    - **category**: Optional category filter (FDB Sheds, SB Sheds, etc.)
    - **distributor_id**: Optional distributor ID filter
    
    Returns sales data with summary statistics.
    """
    try:
        # Create request model for validation
        request = SalesDataRequest(
            start_date=start_date,
            end_date=end_date,
            category=category,
            distributor_id=distributor_id
        )
        
        # Get datetime range
        start_dt, end_dt = request.get_datetime_range()
        
        logger.info(f"Sales data requested by {current_user.username} for {start_dt.date()} to {end_dt.date()}")
        
        # Get sales data
        df = data_service.get_sales_data(start_dt, end_dt, category, distributor_id)
        
        # Convert DataFrame to list of dictionaries
        data = df.to_dict('records') if not df.empty else []
        
        # Get summary statistics
        summary = data_service.get_sales_summary(start_dt, end_dt)
        
        response = SalesDataResponse(
            data=data,
            total_records=len(data),
            summary=summary,
            date_range={
                "start": start_dt.strftime('%Y-%m-%d'),
                "end": end_dt.strftime('%Y-%m-%d')
            }
        )
        
        logger.info(f"Returning {len(data)} sales records")
        return response
        
    except Exception as e:
        logger.error(f"Failed to get sales data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sales data"
        )


@router.get("/customer-data", response_model=CustomerDataResponse, summary="Get Customer Data")
async def get_customer_data(
    start_date: Optional[date] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="End date (YYYY-MM-DD)"),
    state: Optional[str] = Query(None, description="State filter"),
    postcode: Optional[str] = Query(None, description="Postcode filter"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get customer data with optional filtering.
    
    - **start_date**: Start date for data extraction
    - **end_date**: End date for data extraction
    - **state**: Optional state filter (NSW, VIC, QLD, etc.)
    - **postcode**: Optional postcode filter
    
    Returns customer order data with addresses.
    """
    try:
        # Create request model for validation
        request = CustomerDataRequest(
            start_date=start_date,
            end_date=end_date,
            state=state,
            postcode=postcode
        )
        
        # Get datetime range
        start_dt, end_dt = request.get_datetime_range()
        
        logger.info(f"Customer data requested by {current_user.username} for {start_dt.date()} to {end_dt.date()}")
        
        # Get customer data
        df = data_service.get_customer_data(start_dt, end_dt, state, postcode)
        
        # Convert DataFrame to list of dictionaries
        data = df.to_dict('records') if not df.empty else []
        
        response = CustomerDataResponse(
            data=data,
            total_records=len(data),
            date_range={
                "start": start_dt.strftime('%Y-%m-%d'),
                "end": end_dt.strftime('%Y-%m-%d')
            }
        )
        
        logger.info(f"Returning {len(data)} customer records")
        return response
        
    except Exception as e:
        logger.error(f"Failed to get customer data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve customer data"
        )


@router.get("/leads-data", response_model=LeadsDataResponse, summary="Get Leads Data")
async def get_leads_data(
    start_date: Optional[date] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="End date (YYYY-MM-DD)"),
    source: Optional[str] = Query(None, description="Lead source filter"),
    status: Optional[str] = Query(None, description="Lead status filter"),
    aggregated: bool = Query(False, description="Return aggregated data"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get leads data with optional filtering.
    
    - **start_date**: Start date for data extraction
    - **end_date**: End date for data extraction
    - **source**: Optional source filter (App, Web, etc.)
    - **status**: Optional status filter
    - **aggregated**: Return aggregated data by distributor and month
    
    Returns leads/quote data.
    """
    try:
        # Create request model for validation
        request = LeadsDataRequest(
            start_date=start_date,
            end_date=end_date,
            source=source,
            status=status,
            aggregated=aggregated
        )
        
        # Get datetime range
        start_dt, end_dt = request.get_datetime_range()
        
        logger.info(f"Leads data requested by {current_user.username} for {start_dt.date()} to {end_dt.date()}")
        
        # Get leads data
        if aggregated:
            df = data_service.get_leads_aggregated_data(start_dt, end_dt)
        else:
            df = data_service.get_leads_raw_data(start_dt, end_dt, source, status)
        
        # Convert DataFrame to list of dictionaries
        data = df.to_dict('records') if not df.empty else []
        
        response = LeadsDataResponse(
            data=data,
            total_records=len(data),
            date_range={
                "start": start_dt.strftime('%Y-%m-%d'),
                "end": end_dt.strftime('%Y-%m-%d')
            },
            aggregated=aggregated
        )
        
        logger.info(f"Returning {len(data)} leads records")
        return response
        
    except Exception as e:
        logger.error(f"Failed to get leads data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve leads data"
        )


@router.get("/heatmap-data", response_model=HeatmapDataResponse, summary="Get Heatmap Data")
async def get_heatmap_data(
    start_date: Optional[date] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="End date (YYYY-MM-DD)"),
    data_type: HeatmapType = Query(HeatmapType.SALES, description="Type of data for heatmap"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get processed heatmap data for Australian map visualization.
    
    - **start_date**: Start date for data extraction
    - **end_date**: End date for data extraction
    - **data_type**: Type of data for heatmap (sales, customers, leads)
    
    Returns heatmap data points with postcode and value.
    """
    try:
        # Create request model for validation
        request = HeatmapDataRequest(
            start_date=start_date,
            end_date=end_date,
            data_type=data_type
        )
        
        # Get datetime range
        start_dt, end_dt = request.get_datetime_range()
        
        logger.info(f"Heatmap data ({data_type}) requested by {current_user.username}")
        
        # Get heatmap data
        heatmap_data = data_service.get_heatmap_data(start_dt, end_dt, data_type.value)
        
        response = HeatmapDataResponse(
            data=heatmap_data,
            total_points=len(heatmap_data),
            data_type=data_type.value,
            date_range={
                "start": start_dt.strftime('%Y-%m-%d'),
                "end": end_dt.strftime('%Y-%m-%d')
            }
        )
        
        logger.info(f"Returning {len(heatmap_data)} heatmap points")
        return response
        
    except Exception as e:
        logger.error(f"Failed to get heatmap data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve heatmap data"
        )


@router.get("/distributor-locations", response_model=DistributorLocationsResponse, summary="Get Distributor Locations")
async def get_distributor_locations(current_user: User = Depends(get_current_active_user)):
    """
    Get distributor locations for map markers.
    
    Returns list of distributor locations with coordinates.
    """
    try:
        logger.info(f"Distributor locations requested by {current_user.username}")
        
        locations = data_service.get_distributor_locations()
        
        response = DistributorLocationsResponse(
            data=locations,
            total_locations=len(locations)
        )
        
        logger.info(f"Returning {len(locations)} distributor locations")
        return response
        
    except Exception as e:
        logger.error(f"Failed to get distributor locations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve distributor locations"
        )


@router.get("/postcodes", response_model=PostcodesResponse, summary="Get Postcodes Data")
async def get_postcodes(current_user: User = Depends(get_current_active_user)):
    """
    Get Australian postcodes data.
    
    Returns list of postcodes with coordinates and state information.
    """
    try:
        logger.info(f"Postcodes data requested by {current_user.username}")
        
        postcodes = data_service.get_postcodes_data()
        
        response = PostcodesResponse(
            data=postcodes,
            total_postcodes=len(postcodes)
        )
        
        logger.info(f"Returning {len(postcodes)} postcodes")
        return response
        
    except Exception as e:
        logger.error(f"Failed to get postcodes data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve postcodes data"
        )


@router.delete("/cache", response_model=APIResponse, summary="Clear Data Cache")
async def clear_cache(
    cache_type: Optional[str] = Query(None, description="Specific cache type to clear"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Clear cached data.
    
    - **cache_type**: Optional specific cache type to clear (sales_data, customer_data, etc.)
    
    Admin users can clear all caches, regular users can only clear their own data caches.
    """
    try:
        # Only admin users can clear all caches
        if not cache_type and current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required to clear all caches"
            )
        
        logger.info(f"Cache clear requested by {current_user.username} for {cache_type or 'all'}")
        
        data_service.clear_cache(cache_type)
        
        return APIResponse(
            success=True,
            message=f"Cache cleared successfully for {cache_type or 'all data types'}",
            data={"cache_type": cache_type or "all", "user": current_user.username}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear cache"
        )
