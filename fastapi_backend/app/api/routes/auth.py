# Authentication Routes
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON>ear<PERSON>
import logging

from ...auth.models import User<PERSON>ogin, UserCreate, Token, User
from ...auth.auth import auth_service
from ...auth.dependencies import get_current_active_user
from ...models.data import APIResponse

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/auth", tags=["Authentication"])
security = HTTPBearer()


@router.post("/login", response_model=Token, summary="User Login")
async def login(login_data: UserLogin):
    """
    Authenticate user and return JWT token.
    
    - **username**: User's username
    - **password**: User's password
    
    Returns JWT token with user information and expiration time.
    """
    try:
        token = await auth_service.authenticate_and_create_token(login_data)
        
        if not token:
            logger.warning(f"Login failed for user: {login_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        logger.info(f"User logged in successfully: {login_data.username}")
        return token
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during login"
        )


@router.post("/register", response_model=Token, summary="User Registration")
async def register(user_data: UserCreate):
    """
    Register a new user and return JWT token.

    - **username**: Desired username (must be unique)
    - **password**: User's password
    - **role**: User role (default: "user", can be "admin")

    Returns JWT token with user information and expiration time.
    """
    try:
        token = await auth_service.register_user(user_data)

        if not token:
            logger.warning(f"Registration failed for user: {user_data.username}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already exists or registration failed"
            )

        logger.info(f"User registered successfully: {user_data.username}")
        return token

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/refresh", response_model=Token, summary="Refresh Token")
async def refresh_token(current_user: User = Depends(get_current_active_user)):
    """
    Refresh JWT token for authenticated user.
    
    Requires valid JWT token in Authorization header.
    Returns new JWT token with extended expiration time.
    """
    try:
        token = await auth_service.refresh_token(current_user)
        logger.info(f"Token refreshed for user: {current_user.username}")
        return token
        
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refresh token"
        )


@router.get("/me", response_model=User, summary="Get Current User")
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """
    Get current authenticated user information.
    
    Requires valid JWT token in Authorization header.
    Returns user profile information.
    """
    return current_user


@router.post("/logout", response_model=APIResponse, summary="User Logout")
async def logout(current_user: User = Depends(get_current_active_user)):
    """
    Logout current user (placeholder for future session management).
    
    Currently returns success response. In the future, this could:
    - Invalidate the JWT token
    - Clear user sessions from Redis
    - Log the logout event
    """
    try:
        # Future: Add token blacklisting or session cleanup
        logger.info(f"User logged out: {current_user.username}")
        
        return APIResponse(
            success=True,
            message="Successfully logged out",
            data={"username": current_user.username}
        )
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to logout"
        )
