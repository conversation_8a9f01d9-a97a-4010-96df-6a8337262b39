# Export API Routes
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import FileResponse
from typing import Optional, List
import logging
from pathlib import Path

from ...auth.dependencies import get_current_active_user, require_admin
from ...auth.models import User
from ...services.export_service import export_service
from ...models.export import (
    ExportJobRequest, ExportJobResponse, ExportJobStatus,
    ExportJobStatusResponse, ExportJobListResponse,
    ExportStatus
)
from ...models.data import APIResponse

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/export", tags=["Export"], dependencies=[Depends(get_current_active_user)])


@router.post("/start", response_model=ExportJobResponse, summary="Start Export Job")
async def start_export_job(
    request: ExportJobRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Start a background export job.
    
    - **export_type**: Type of data to export (sales, customers, leads, all)
    - **export_format**: File format (csv, excel, json)
    - **start_date**: Start date for data extraction
    - **end_date**: End date for data extraction
    - **filters**: Various filters based on export type
    - **options**: Export options (include_summary, compress)
    
    Returns job ID and status for tracking progress.
    """
    try:
        logger.info(f"Export job requested by {current_user.username}: {request.export_type.value}")
        
        response = await export_service.start_export_job(request, current_user.username)
        
        logger.info(f"Export job {response.job_id} started successfully")
        return response
        
    except Exception as e:
        logger.error(f"Failed to start export job: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start export job"
        )


@router.get("/status/{job_id}", response_model=ExportJobStatusResponse, summary="Get Export Job Status")
async def get_export_job_status(
    job_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get export job status and progress.
    
    - **job_id**: Export job identifier
    
    Returns current job status, progress, and download information.
    """
    try:
        job_status = export_service.get_job_status(job_id)
        
        if not job_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Export job not found"
            )
        
        # Check if user can access this job (own job or admin)
        if job_status.user != current_user.username and current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this export job"
            )
        
        # Determine permissions
        can_download = (
            job_status.status == ExportStatus.COMPLETED and 
            job_status.file_path is not None
        )
        can_cancel = job_status.status in [ExportStatus.PENDING, ExportStatus.PROCESSING]
        
        response = ExportJobStatusResponse(
            job=job_status,
            can_download=can_download,
            can_cancel=can_cancel
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve job status"
        )


@router.get("/jobs", response_model=ExportJobListResponse, summary="List Export Jobs")
async def list_export_jobs(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_active_user)
):
    """
    List export jobs for the current user.
    
    - **page**: Page number (starts from 1)
    - **page_size**: Number of jobs per page (max 100)
    
    Returns paginated list of user's export jobs.
    """
    try:
        # Get user jobs
        all_jobs = export_service.get_user_jobs(current_user.username)
        
        # Calculate pagination
        total_jobs = len(all_jobs)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        jobs = all_jobs[start_idx:end_idx]
        
        response = ExportJobListResponse(
            jobs=jobs,
            total_jobs=total_jobs,
            page=page,
            page_size=page_size
        )
        
        logger.info(f"Listed {len(jobs)} jobs for user {current_user.username}")
        return response
        
    except Exception as e:
        logger.error(f"Failed to list export jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve export jobs"
        )


@router.get("/download/{job_id}", summary="Download Export File")
async def download_export_file(
    job_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Download completed export file.
    
    - **job_id**: Export job identifier
    
    Returns the export file for download.
    """
    try:
        # Get job status
        job_status = export_service.get_job_status(job_id)
        
        if not job_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Export job not found"
            )
        
        # Check permissions
        if job_status.user != current_user.username and current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this export job"
            )
        
        # Check if job is completed
        if job_status.status != ExportStatus.COMPLETED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Export job is not completed (status: {job_status.status.value})"
            )
        
        # Get file path
        file_path = export_service.get_export_file_path(job_id)
        
        if not file_path or not file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Export file not found or has been deleted"
            )
        
        # Determine content type based on file extension
        content_type_map = {
            '.csv': 'text/csv',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.json': 'application/json',
            '.zip': 'application/zip'
        }
        
        content_type = content_type_map.get(file_path.suffix.lower(), 'application/octet-stream')
        
        logger.info(f"File download: {file_path.name} by {current_user.username}")
        
        return FileResponse(
            path=str(file_path),
            filename=file_path.name,
            media_type=content_type
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to download export file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to download export file"
        )


@router.delete("/cancel/{job_id}", response_model=APIResponse, summary="Cancel Export Job")
async def cancel_export_job(
    job_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Cancel a pending or processing export job.
    
    - **job_id**: Export job identifier
    
    Cancels the job if it's still pending or processing.
    """
    try:
        # Get job status
        job_status = export_service.get_job_status(job_id)
        
        if not job_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Export job not found"
            )
        
        # Check permissions
        if job_status.user != current_user.username and current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this export job"
            )
        
        # Check if job can be cancelled
        if job_status.status not in [ExportStatus.PENDING, ExportStatus.PROCESSING]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot cancel job with status: {job_status.status.value}"
            )
        
        # Update job status to cancelled
        from ...services.redis_service import export_job_tracker
        success = export_job_tracker.update_job_status(job_id, "cancelled")
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to cancel export job"
            )
        
        logger.info(f"Export job {job_id} cancelled by {current_user.username}")
        
        return APIResponse(
            success=True,
            message="Export job cancelled successfully",
            data={"job_id": job_id, "status": "cancelled"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel export job: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel export job"
        )


@router.delete("/cleanup", response_model=APIResponse, summary="Cleanup Old Export Files")
async def cleanup_old_exports(
    days: int = Query(7, ge=1, le=30, description="Delete files older than N days"),
    current_user: User = Depends(require_admin)
):
    """
    Clean up old export files and job records.
    
    - **days**: Delete files and jobs older than this many days (1-30)
    
    Admin only endpoint for cleaning up old export data.
    """
    try:
        deleted_count = export_service.cleanup_old_files()
        
        logger.info(f"Export cleanup completed by {current_user.username}: {deleted_count} items deleted")
        
        return APIResponse(
            success=True,
            message=f"Cleanup completed successfully",
            data={
                "deleted_items": deleted_count,
                "days_threshold": days,
                "admin": current_user.username
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to cleanup old exports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cleanup old export files"
        )
