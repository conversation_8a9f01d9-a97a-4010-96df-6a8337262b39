# Security Utilities
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
import json
import logging
from pathlib import Path

from ..config.settings import settings
from ..auth.models import User, UserInDB, TokenData

logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class UserManager:
    """User management class for handling user authentication."""
    
    def __init__(self, users_file_path: str = None):
        self.users_file_path = users_file_path or settings.users_file_path
        self._users_cache = None
        self._cache_timestamp = None
    
    def _load_users(self) -> Dict[str, UserInDB]:
        """Load users from JSON file with caching."""
        try:
            users_path = Path(self.users_file_path)
            
            # Check if we need to reload the cache
            if (self._users_cache is None or 
                self._cache_timestamp is None or 
                users_path.stat().st_mtime > self._cache_timestamp):
                
                logger.info(f"Loading users from {users_path}")
                
                if not users_path.exists():
                    logger.error(f"Users file not found: {users_path}")
                    return {}
                
                with open(users_path, 'r') as f:
                    data = json.load(f)
                
                # Convert to dictionary with username as key
                users_dict = {}
                for user_data in data.get('users', []):
                    user = UserInDB(**user_data)
                    users_dict[user.username] = user
                
                self._users_cache = users_dict
                self._cache_timestamp = users_path.stat().st_mtime
                
                logger.info(f"Loaded {len(users_dict)} users")
            
            return self._users_cache
            
        except Exception as e:
            logger.error(f"Failed to load users: {e}")
            return {}
    
    def get_user(self, username: str) -> Optional[UserInDB]:
        """Get user by username."""
        users = self._load_users()
        return users.get(username)
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate user with username and password."""
        user = self.get_user(username)
        if not user:
            logger.warning(f"User not found: {username}")
            return None
        
        if not verify_password(password, user.password):
            logger.warning(f"Invalid password for user: {username}")
            return None
        
        logger.info(f"User authenticated successfully: {username}")
        return User(username=user.username, role=user.role)
    
    def create_user(self, username: str, password: str, role: str = "user") -> bool:
        """Create a new user (for admin functionality)."""
        try:
            users_data = {"users": []}
            users_path = Path(self.users_file_path)
            
            # Load existing users
            if users_path.exists():
                with open(users_path, 'r') as f:
                    users_data = json.load(f)
            
            # Check if user already exists
            for user in users_data['users']:
                if user['username'] == username:
                    logger.warning(f"User already exists: {username}")
                    return False
            
            # Add new user
            hashed_password = get_password_hash(password)
            new_user = {
                "username": username,
                "password": hashed_password,
                "role": role
            }
            users_data['users'].append(new_user)
            
            # Save to file
            with open(users_path, 'w') as f:
                json.dump(users_data, f, indent=2)
            
            # Clear cache to force reload
            self._users_cache = None
            
            logger.info(f"User created successfully: {username}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create user {username}: {e}")
            return False


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a plain password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Generate password hash."""
    return pwd_context.hash(password)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


def verify_token(token: str) -> Optional[TokenData]:
    """Verify JWT token and return token data."""
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        username: str = payload.get("sub")
        role: str = payload.get("role")
        exp: datetime = datetime.fromtimestamp(payload.get("exp", 0))
        
        if username is None:
            return None
        
        token_data = TokenData(username=username, role=role, exp=exp)
        return token_data
        
    except JWTError as e:
        logger.warning(f"JWT verification failed: {e}")
        return None


# Global user manager instance
user_manager = UserManager()


# Utility function to generate default users file
def generate_default_users_file(file_path: str):
    """Generate default users.json file with admin and user accounts."""
    default_users = {
        "users": [
            {
                "username": "admin",
                "password": get_password_hash("secret"),  # Default password: secret
                "role": "admin"
            },
            {
                "username": "user",
                "password": get_password_hash("secret"),  # Default password: secret
                "role": "user"
            }
        ]
    }
    
    users_path = Path(file_path)
    users_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(users_path, 'w') as f:
        json.dump(default_users, f, indent=2)
    
    logger.info(f"Generated default users file: {file_path}")
    logger.info("Default credentials - admin:secret, user:secret")


# Generate default users file if it doesn't exist
if not Path(settings.users_file_path).exists():
    generate_default_users_file(settings.users_file_path)
