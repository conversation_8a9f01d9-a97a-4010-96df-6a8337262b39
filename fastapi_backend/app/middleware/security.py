# Security Middleware
import time
import logging
from typing import Dict, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta

from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from ..services.redis_service import redis_service

logger = logging.getLogger(__name__)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware using Redis for distributed rate limiting."""
    
    def __init__(self, app, calls_per_minute: int = 60, calls_per_hour: int = 1000):
        super().__init__(app)
        self.calls_per_minute = calls_per_minute
        self.calls_per_hour = calls_per_hour
        
        # Endpoint-specific rate limits
        self.endpoint_limits = {
            "/auth/login": {"per_minute": 5, "per_hour": 50},
            "/api/export/start": {"per_minute": 2, "per_hour": 20},
            "/api/sales-data": {"per_minute": 10, "per_hour": 100},
            "/api/customer-data": {"per_minute": 10, "per_hour": 100},
            "/api/leads-data": {"per_minute": 10, "per_hour": 100},
        }
    
    async def dispatch(self, request: Request, call_next):
        """Process request with rate limiting."""
        try:
            # Get client identifier
            client_id = self._get_client_id(request)
            
            # Check rate limits
            if not await self._check_rate_limit(client_id, request.url.path):
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "error": "Rate limit exceeded",
                        "detail": "Too many requests. Please try again later.",
                        "status_code": 429
                    }
                )
            
            # Process request
            response = await call_next(request)
            
            # Add rate limit headers
            await self._add_rate_limit_headers(response, client_id, request.url.path)
            
            return response
            
        except Exception as e:
            logger.error(f"Rate limit middleware error: {e}")
            # Continue processing if rate limiting fails
            return await call_next(request)
    
    def _get_client_id(self, request: Request) -> str:
        """Get client identifier for rate limiting."""
        # Try to get user from JWT token first
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            try:
                from ..utils.security import decode_jwt_token
                token = auth_header.split(" ")[1]
                payload = decode_jwt_token(token)
                if payload and "sub" in payload:
                    return f"user:{payload['sub']}"
            except:
                pass
        
        # Fall back to IP address
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return f"ip:{forwarded_for.split(',')[0].strip()}"
        
        return f"ip:{request.client.host}"
    
    async def _check_rate_limit(self, client_id: str, path: str) -> bool:
        """Check if request is within rate limits."""
        try:
            # Get limits for this endpoint
            limits = self.endpoint_limits.get(path, {
                "per_minute": self.calls_per_minute,
                "per_hour": self.calls_per_hour
            })
            
            current_time = int(time.time())
            
            # Check minute limit
            minute_key = f"rate_limit:{client_id}:minute:{current_time // 60}"
            minute_count = await self._increment_counter(minute_key, 60)
            
            if minute_count > limits["per_minute"]:
                logger.warning(f"Rate limit exceeded (minute): {client_id} on {path}")
                return False
            
            # Check hour limit
            hour_key = f"rate_limit:{client_id}:hour:{current_time // 3600}"
            hour_count = await self._increment_counter(hour_key, 3600)
            
            if hour_count > limits["per_hour"]:
                logger.warning(f"Rate limit exceeded (hour): {client_id} on {path}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            # Allow request if rate limiting fails
            return True
    
    async def _increment_counter(self, key: str, ttl: int) -> int:
        """Increment rate limit counter in Redis."""
        try:
            # Use Redis pipeline for atomic operations
            pipe = redis_service.redis_client.pipeline()
            pipe.incr(key)
            pipe.expire(key, ttl)
            results = pipe.execute()
            return results[0]
        except Exception as e:
            logger.error(f"Failed to increment rate limit counter: {e}")
            return 0
    
    async def _add_rate_limit_headers(self, response: Response, client_id: str, path: str):
        """Add rate limit headers to response."""
        try:
            limits = self.endpoint_limits.get(path, {
                "per_minute": self.calls_per_minute,
                "per_hour": self.calls_per_hour
            })
            
            current_time = int(time.time())
            
            # Get current counts
            minute_key = f"rate_limit:{client_id}:minute:{current_time // 60}"
            hour_key = f"rate_limit:{client_id}:hour:{current_time // 3600}"
            
            minute_count = redis_service.redis_client.get(minute_key) or 0
            hour_count = redis_service.redis_client.get(hour_key) or 0
            
            # Add headers
            response.headers["X-RateLimit-Limit-Minute"] = str(limits["per_minute"])
            response.headers["X-RateLimit-Remaining-Minute"] = str(max(0, limits["per_minute"] - int(minute_count)))
            response.headers["X-RateLimit-Limit-Hour"] = str(limits["per_hour"])
            response.headers["X-RateLimit-Remaining-Hour"] = str(max(0, limits["per_hour"] - int(hour_count)))
            
        except Exception as e:
            logger.error(f"Failed to add rate limit headers: {e}")


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Add security headers to responses."""
    
    def __init__(self, app):
        super().__init__(app)
        
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
        }
    
    async def dispatch(self, request: Request, call_next):
        """Add security headers to response."""
        response = await call_next(request)
        
        # Add security headers
        for header, value in self.security_headers.items():
            response.headers[header] = value
        
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Log requests for monitoring and debugging."""
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = logging.getLogger("app.requests")
    
    async def dispatch(self, request: Request, call_next):
        """Log request details."""
        start_time = time.time()
        
        # Get client info
        client_ip = request.headers.get("x-forwarded-for", request.client.host)
        user_agent = request.headers.get("user-agent", "")
        
        # Get user from token if available
        user = "anonymous"
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            try:
                from ..utils.security import decode_jwt_token
                token = auth_header.split(" ")[1]
                payload = decode_jwt_token(token)
                if payload and "sub" in payload:
                    user = payload["sub"]
            except:
                pass
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log request
            self.logger.info(
                f"{request.method} {request.url.path} - "
                f"Status: {response.status_code} - "
                f"Time: {process_time:.3f}s - "
                f"User: {user} - "
                f"IP: {client_ip} - "
                f"UA: {user_agent[:100]}"
            )
            
            # Add processing time header
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # Log error
            process_time = time.time() - start_time
            self.logger.error(
                f"{request.method} {request.url.path} - "
                f"ERROR: {str(e)} - "
                f"Time: {process_time:.3f}s - "
                f"User: {user} - "
                f"IP: {client_ip}"
            )
            raise


class IPWhitelistMiddleware(BaseHTTPMiddleware):
    """IP whitelist middleware for admin endpoints."""
    
    def __init__(self, app, whitelist: Optional[list] = None):
        super().__init__(app)
        self.whitelist = whitelist or []
        
        # Admin endpoints that require IP whitelisting
        self.admin_endpoints = [
            "/api/export/cleanup",
            "/admin/",
        ]
    
    async def dispatch(self, request: Request, call_next):
        """Check IP whitelist for admin endpoints."""
        # Skip if no whitelist configured
        if not self.whitelist:
            return await call_next(request)
        
        # Check if this is an admin endpoint
        is_admin_endpoint = any(
            request.url.path.startswith(endpoint) 
            for endpoint in self.admin_endpoints
        )
        
        if is_admin_endpoint:
            client_ip = request.headers.get("x-forwarded-for", request.client.host)
            if client_ip.split(',')[0].strip() not in self.whitelist:
                logger.warning(f"IP {client_ip} blocked from admin endpoint {request.url.path}")
                return JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content={
                        "error": "Access denied",
                        "detail": "Your IP address is not authorized to access this endpoint",
                        "status_code": 403
                    }
                )
        
        return await call_next(request)
