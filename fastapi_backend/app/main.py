# FastAPI Main Application
import logging
import sys
from pathlib import Path
from contextlib import asynccontextmanager

from fastapi import <PERSON>AP<PERSON>, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.exceptions import RequestValidationError
import uvicorn

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.config.settings import settings
from app.services.redis_service import redis_service
from app.config.database import db_manager
from app.api.routes import auth, data, export
from app.models.data import ErrorResponse, ValidationErrorResponse, HealthCheckResponse
from app.utils.logging_config import setup_logging
from app.middleware.security import (
    RateLimitMiddleware, SecurityHeadersMiddleware,
    RequestLoggingMiddleware, IPWhitelistMiddleware
)

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events."""
    # Startup
    logger.info("Starting FastAPI application...")
    
    try:
        # Test database connections
        logger.info("Testing database connections...")
        for db_name in ["cdb", "app", "reporting", "app_secondary"]:
            try:
                with db_manager.get_connection(db_name) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                logger.info(f"Database connection '{db_name}' - OK")
            except Exception as e:
                logger.warning(f"Database connection '{db_name}' - FAILED: {e}")
        
        # Test Redis connection
        logger.info("Testing Redis connection...")
        health = redis_service.health_check()
        if health.get("status") == "healthy":
            logger.info("Redis connection - OK")
        else:
            logger.warning("Redis connection - FAILED")
        
        logger.info("Application startup completed successfully")
        
    except Exception as e:
        logger.error(f"Application startup failed: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down FastAPI application...")
    try:
        # Close database connections
        db_manager.close_all_connections()
        logger.info("Database connections closed")
        
        # Close Redis connection
        redis_service.close()
        logger.info("Redis connection closed")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")
    
    logger.info("Application shutdown completed")


# Create FastAPI application
app = FastAPI(
    title="Stramit Data Export API",
    description="FastAPI backend for Stramit data export and visualization system",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add middleware (order matters - first added is outermost)
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.allowed_hosts
)

# Security middleware
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(RateLimitMiddleware, calls_per_minute=60, calls_per_hour=1000)

# IP whitelist middleware (optional - configure in settings)
if hasattr(settings, 'admin_ip_whitelist') and settings.admin_ip_whitelist:
    app.add_middleware(IPWhitelistMiddleware, whitelist=settings.admin_ip_whitelist)

# Include routers
app.include_router(auth.router)
app.include_router(data.router)
app.include_router(export.router)


# Global exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions."""
    logger.warning(f"HTTP {exc.status_code}: {exc.detail} - {request.url}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.detail,
            status_code=exc.status_code
        ).dict()
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle request validation errors."""
    logger.warning(f"Validation error: {exc.errors()} - {request.url}")
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=ValidationErrorResponse(
            detail=exc.errors()
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc} - {request.url}", exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            error="Internal server error",
            detail="An unexpected error occurred",
            status_code=500
        ).dict()
    )


# Root endpoint
@app.get("/", summary="Root Endpoint")
async def root():
    """Root endpoint with basic API information."""
    return {
        "message": "Stramit Data Export API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc",
        "health": "/health"
    }


# Health check endpoint
@app.get("/health", response_model=HealthCheckResponse, summary="Health Check")
async def health_check():
    """
    Health check endpoint.
    
    Returns the health status of the API and its dependencies.
    """
    try:
        from datetime import datetime
        
        # Check Redis
        redis_health = redis_service.health_check()
        
        # Check databases
        db_status = {}
        for db_name in ["cdb", "app", "reporting", "app_secondary"]:
            try:
                with db_manager.get_connection(db_name) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                db_status[db_name] = "healthy"
            except Exception as e:
                db_status[db_name] = f"error: {str(e)}"
        
        # Overall status
        overall_status = "healthy"
        if redis_health.get("status") != "healthy":
            overall_status = "degraded"
        if any("error" in status for status in db_status.values()):
            overall_status = "degraded"
        
        return HealthCheckResponse(
            status=overall_status,
            timestamp=datetime.now(),
            version="1.0.0",
            services={
                "redis": redis_health,
                "databases": db_status
            }
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthCheckResponse(
            status="unhealthy",
            timestamp=datetime.now(),
            version="1.0.0",
            services={
                "error": str(e)
            }
        )


# API Info endpoint
@app.get("/api/info", summary="API Information")
async def api_info():
    """Get API information and available endpoints."""
    return {
        "api_name": "Stramit Data Export API",
        "version": "1.0.0",
        "description": "FastAPI backend for Stramit data export and visualization system",
        "endpoints": {
            "authentication": "/auth/*",
            "data": "/api/*",
            "export": "/api/export/*",
            "health": "/health",
            "docs": "/docs"
        },
        "features": [
            "JWT Authentication",
            "Data Export (Sales, Customers, Leads)",
            "Australian Map Visualization Data",
            "Background Export Jobs",
            "Redis Caching",
            "Multiple Database Support"
        ]
    }


if __name__ == "__main__":
    # Run the application
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug"
    )
