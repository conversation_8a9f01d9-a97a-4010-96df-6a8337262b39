# Export Job Models
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, date
from enum import Enum


class ExportFormat(str, Enum):
    """Supported export formats."""
    CSV = "csv"
    EXCEL = "excel"
    JSON = "json"


class ExportType(str, Enum):
    """Supported export types."""
    SALES = "sales"
    CUSTOMERS = "customers"
    LEADS = "leads"
    ALL = "all"


class ExportStatus(str, Enum):
    """Export job status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ExportJobRequest(BaseModel):
    """Request model for starting an export job."""
    export_type: ExportType = Field(..., description="Type of data to export")
    export_format: ExportFormat = Field(ExportFormat.CSV, description="Export file format")
    start_date: Optional[date] = Field(None, description="Start date for data extraction")
    end_date: Optional[date] = Field(None, description="End date for data extraction")
    
    # Optional filters
    category: Optional[str] = Field(None, description="Category filter for sales data")
    distributor_id: Optional[str] = Field(None, description="Distributor ID filter")
    state: Optional[str] = Field(None, description="State filter for customer data")
    postcode: Optional[str] = Field(None, description="Postcode filter")
    source: Optional[str] = Field(None, description="Source filter for leads data")
    status: Optional[str] = Field(None, description="Status filter for leads data")
    
    # Export options
    include_summary: bool = Field(True, description="Include summary statistics")
    compress: bool = Field(False, description="Compress export file")
    
    @validator('end_date')
    def end_date_must_be_after_start_date(cls, v, values):
        if v and values.get('start_date') and v < values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v


class ExportJobResponse(BaseModel):
    """Response model for export job creation."""
    job_id: str
    status: ExportStatus
    message: str
    estimated_completion: Optional[datetime] = None


class ExportJobStatus(BaseModel):
    """Export job status model."""
    job_id: str
    job_type: str
    status: ExportStatus
    progress: int = Field(0, ge=0, le=100, description="Progress percentage")
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    user: str
    
    # Job parameters
    params: Dict[str, Any]
    
    # Results
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    download_url: Optional[str] = None
    
    # Error information
    error: Optional[str] = None
    
    # Statistics
    total_records: Optional[int] = None
    processing_time: Optional[float] = None


class ExportJobStatusResponse(BaseModel):
    """Response model for export job status."""
    job: ExportJobStatus
    can_download: bool
    can_cancel: bool


class ExportJobListResponse(BaseModel):
    """Response model for listing export jobs."""
    jobs: List[ExportJobStatus]
    total_jobs: int
    page: int
    page_size: int


class FileDownloadInfo(BaseModel):
    """File download information."""
    file_id: str
    filename: str
    file_size: int
    content_type: str
    created_at: datetime
    expires_at: Optional[datetime] = None


class ExportSummary(BaseModel):
    """Export summary statistics."""
    total_records: int
    file_size: int
    processing_time: float
    data_range: Dict[str, str]
    filters_applied: Dict[str, Any]
    export_format: str
    created_at: datetime


# Background job task models
class ExportTask(BaseModel):
    """Background export task model."""
    job_id: str
    export_type: ExportType
    export_format: ExportFormat
    user: str
    params: Dict[str, Any]
    created_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for task queue."""
        return {
            "job_id": self.job_id,
            "export_type": self.export_type.value,
            "export_format": self.export_format.value,
            "user": self.user,
            "params": self.params,
            "created_at": self.created_at.isoformat()
        }
