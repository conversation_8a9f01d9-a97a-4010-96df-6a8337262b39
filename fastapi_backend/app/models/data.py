# Data Models for API Requests and Responses
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, date
from enum import Enum


class DataType(str, Enum):
    """Supported data types for export."""
    SALES = "sales"
    CUSTOMERS = "customers"
    LEADS = "leads"
    HEATMAP = "heatmap"
    DISTRIBUTORS = "distributors"
    POSTCODES = "postcodes"


class HeatmapType(str, Enum):
    """Supported heatmap data types."""
    SALES = "sales"
    CUSTOMERS = "customers"
    LEADS = "leads"


class DateRangeFilter(BaseModel):
    """Base model for date range filtering."""
    start_date: Optional[date] = Field(None, description="Start date for data extraction")
    end_date: Optional[date] = Field(None, description="End date for data extraction")
    
    @validator('end_date')
    def end_date_must_be_after_start_date(cls, v, values):
        if v and values.get('start_date') and v < values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v
    
    def get_datetime_range(self) -> tuple[datetime, datetime]:
        """Convert date range to datetime objects with defaults."""
        if not self.start_date:
            # Default to last 360 days
            end = datetime.now()
            start = end.replace(year=end.year - 1)
        else:
            start = datetime.combine(self.start_date, datetime.min.time())
            end = datetime.combine(
                self.end_date or date.today(), 
                datetime.max.time()
            )
        return start, end


# Sales Data Models
class SalesDataRequest(DateRangeFilter):
    """Request model for sales data."""
    category: Optional[str] = Field(None, description="Sales category filter")
    distributor_id: Optional[str] = Field(None, description="Distributor ID filter")


class SalesDataPoint(BaseModel):
    """Individual sales data point."""
    payment_id: Optional[int] = None
    amount: Optional[float] = None
    category: Optional[str] = None
    distributor_id: Optional[str] = None
    network_id: Optional[str] = None
    payment_date: Optional[datetime] = None
    postcode: Optional[str] = None
    year: Optional[int] = None
    month: Optional[int] = None


class SalesDataResponse(BaseModel):
    """Response model for sales data."""
    data: List[Dict[str, Any]]
    total_records: int
    summary: Dict[str, Any]
    date_range: Dict[str, str]


# Customer Data Models
class CustomerDataRequest(DateRangeFilter):
    """Request model for customer data."""
    state: Optional[str] = Field(None, description="State filter")
    postcode: Optional[str] = Field(None, description="Postcode filter")


class CustomerDataPoint(BaseModel):
    """Individual customer data point."""
    order_id: Optional[int] = None
    customer_name: Optional[str] = None
    address: Optional[str] = None
    town: Optional[str] = None
    state: Optional[str] = None
    postcode: Optional[str] = None
    total_price: Optional[float] = None
    order_date: Optional[datetime] = None
    distributor_code: Optional[str] = None
    category: Optional[str] = None


class CustomerDataResponse(BaseModel):
    """Response model for customer data."""
    data: List[Dict[str, Any]]
    total_records: int
    date_range: Dict[str, str]


# Leads Data Models
class LeadsDataRequest(DateRangeFilter):
    """Request model for leads data."""
    source: Optional[str] = Field(None, description="Lead source filter")
    status: Optional[str] = Field(None, description="Lead status filter")
    aggregated: bool = Field(False, description="Return aggregated data")


class LeadsDataPoint(BaseModel):
    """Individual leads data point."""
    quote_id: Optional[int] = None
    source: Optional[str] = None
    status: Optional[str] = None
    distributor_id: Optional[str] = None
    postcode: Optional[str] = None
    created_date: Optional[datetime] = None
    lead_category: Optional[str] = None


class LeadsDataResponse(BaseModel):
    """Response model for leads data."""
    data: List[Dict[str, Any]]
    total_records: int
    date_range: Dict[str, str]
    aggregated: bool


# Heatmap Data Models
class HeatmapDataRequest(DateRangeFilter):
    """Request model for heatmap data."""
    data_type: HeatmapType = Field(HeatmapType.SALES, description="Type of data for heatmap")


class HeatmapDataPoint(BaseModel):
    """Individual heatmap data point."""
    postcode: str
    value: float
    lat: Optional[float] = None
    lng: Optional[float] = None


class HeatmapDataResponse(BaseModel):
    """Response model for heatmap data."""
    data: List[HeatmapDataPoint]
    total_points: int
    data_type: str
    date_range: Dict[str, str]


# Distributor Location Models
class DistributorLocation(BaseModel):
    """Distributor location data."""
    id: str
    name: str
    lat: float
    lng: float
    state: str
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None


class DistributorLocationsResponse(BaseModel):
    """Response model for distributor locations."""
    data: List[DistributorLocation]
    total_locations: int


# Postcode Data Models
class PostcodeData(BaseModel):
    """Postcode data point."""
    postcode: str
    state: str
    lat: float
    lng: float
    suburb: Optional[str] = None


class PostcodesResponse(BaseModel):
    """Response model for postcodes data."""
    data: List[PostcodeData]
    total_postcodes: int


# Generic API Response Models
class APIResponse(BaseModel):
    """Generic API response wrapper."""
    success: bool
    message: str
    data: Optional[Any] = None
    error: Optional[str] = None


class HealthCheckResponse(BaseModel):
    """Health check response model."""
    status: str
    timestamp: datetime
    version: str
    services: Dict[str, Any]


class ValidationErrorResponse(BaseModel):
    """Validation error response model."""
    detail: List[Dict[str, Any]]

    class Config:
        schema_extra = {
            "example": {
                "detail": [
                    {
                        "loc": ["body", "field_name"],
                        "msg": "field required",
                        "type": "value_error.missing"
                    }
                ]
            }
        }


# Error Response Models
class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    detail: Optional[str] = None
    status_code: int


class ValidationErrorResponse(BaseModel):
    """Validation error response model."""
    error: str = "Validation Error"
    detail: List[Dict[str, Any]]
    status_code: int = 422
