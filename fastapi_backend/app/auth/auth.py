# Authentication Service
from datetime import timedelta
from typing import Optional
import logging

from ..config.settings import settings
from ..utils.security import user_manager, create_access_token
from ..auth.models import User, UserLogin, UserCreate, Token

logger = logging.getLogger(__name__)


class AuthService:
    """Authentication service for handling login and token generation."""
    
    def __init__(self):
        self.user_manager = user_manager
    
    async def authenticate_and_create_token(self, login_data: UserLogin) -> Optional[Token]:
        """
        Authenticate user and create JWT token.
        
        Args:
            login_data: User login credentials
            
        Returns:
            JWT token with user information or None if authentication fails
        """
        try:
            # Authenticate user
            user = self.user_manager.authenticate_user(
                username=login_data.username,
                password=login_data.password
            )
            
            if not user:
                logger.warning(f"Authentication failed for user: {login_data.username}")
                return None
            
            # Create access token
            access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
            access_token = create_access_token(
                data={"sub": user.username, "role": user.role},
                expires_delta=access_token_expires
            )
            
            # Create token response
            token = Token(
                access_token=access_token,
                token_type="bearer",
                expires_in=settings.access_token_expire_minutes * 60,  # Convert to seconds
                user=user
            )
            
            logger.info(f"Token created successfully for user: {user.username}")
            return token
            
        except Exception as e:
            logger.error(f"Token creation failed: {e}")
            return None
    
    async def validate_token(self, token: str) -> Optional[User]:
        """
        Validate JWT token and return user.
        
        Args:
            token: JWT token string
            
        Returns:
            User if token is valid, None otherwise
        """
        try:
            from ..utils.security import verify_token
            
            token_data = verify_token(token)
            if not token_data or not token_data.username:
                return None
            
            user = self.user_manager.get_user(token_data.username)
            if not user:
                return None
            
            return User(username=user.username, role=user.role)
            
        except Exception as e:
            logger.error(f"Token validation failed: {e}")
            return None
    
    async def refresh_token(self, current_user: User) -> Token:
        """
        Refresh JWT token for current user.

        Args:
            current_user: Current authenticated user

        Returns:
            New JWT token
        """
        try:
            # Create new access token
            access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
            access_token = create_access_token(
                data={"sub": current_user.username, "role": current_user.role},
                expires_delta=access_token_expires
            )

            # Create token response
            token = Token(
                access_token=access_token,
                token_type="bearer",
                expires_in=settings.access_token_expire_minutes * 60,
                user=current_user
            )

            logger.info(f"Token refreshed for user: {current_user.username}")
            return token

        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            raise

    async def register_user(self, user_data: UserCreate) -> Optional[Token]:
        """
        Register a new user and create JWT token.

        Args:
            user_data: User registration data

        Returns:
            JWT token with user information or None if registration fails
        """
        try:
            # Create user
            success = self.user_manager.create_user(
                username=user_data.username,
                password=user_data.password,
                role=user_data.role
            )

            if not success:
                logger.warning(f"User registration failed for: {user_data.username}")
                return None

            # Authenticate and create token for the new user
            login_data = UserLogin(username=user_data.username, password=user_data.password)
            token = await self.authenticate_and_create_token(login_data)

            if token:
                logger.info(f"User registered successfully: {user_data.username}")

            return token

        except Exception as e:
            logger.error(f"User registration failed: {e}")
            return None


# Global auth service instance
auth_service = AuthService()
