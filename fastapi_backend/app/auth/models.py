# Authentication Models
from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class User(BaseModel):
    """User model."""
    username: str
    role: str
    
    class Config:
        from_attributes = True


class UserInDB(User):
    """User model with hashed password for database storage."""
    password: str


class UserCreate(BaseModel):
    """User creation model."""
    username: str
    password: str
    role: str = "user"


class UserLogin(BaseModel):
    """User login model."""
    username: str
    password: str


class Token(BaseModel):
    """JWT token model."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: User


class TokenData(BaseModel):
    """Token data model for JWT payload."""
    username: Optional[str] = None
    role: Optional[str] = None
    exp: Optional[datetime] = None
