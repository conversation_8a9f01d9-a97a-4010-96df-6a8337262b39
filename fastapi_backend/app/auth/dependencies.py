# Authentication Dependencies
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
import logging

from ..utils.security import verify_token, user_manager
from ..auth.models import User, TokenData

logger = logging.getLogger(__name__)

# HTTP Bearer token scheme
security = HTTPBearer()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """
    Get current authenticated user from JWT token.
    
    Args:
        credentials: HTTP Bearer credentials
        
    Returns:
        Current user
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Extract token from credentials
        token = credentials.credentials
        
        # Verify token
        token_data: Optional[TokenData] = verify_token(token)
        if token_data is None or token_data.username is None:
            logger.warning("Invalid token provided")
            raise credentials_exception
        
        # Get user from database
        user = user_manager.get_user(username=token_data.username)
        if user is None:
            logger.warning(f"User not found: {token_data.username}")
            raise credentials_exception
        
        # Return user without password
        return User(username=user.username, role=user.role)
        
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise credentials_exception


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Get current active user (placeholder for future user status checks).
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Current active user
    """
    # In the future, you could add checks for user status (active, disabled, etc.)
    return current_user


async def require_admin(current_user: User = Depends(get_current_active_user)) -> User:
    """
    Require admin role for accessing endpoint.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Current user if admin
        
    Raises:
        HTTPException: If user is not admin
    """
    if current_user.role != "admin":
        logger.warning(f"Admin access denied for user: {current_user.username}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    return current_user


async def get_optional_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[User]:
    """
    Get current user if token is provided, otherwise return None.
    Useful for endpoints that work with or without authentication.
    
    Args:
        credentials: Optional HTTP Bearer credentials
        
    Returns:
        Current user or None
    """
    if not credentials:
        return None
    
    try:
        token = credentials.credentials
        token_data = verify_token(token)
        
        if token_data is None or token_data.username is None:
            return None
        
        user = user_manager.get_user(username=token_data.username)
        if user is None:
            return None
        
        return User(username=user.username, role=user.role)
        
    except Exception as e:
        logger.warning(f"Optional authentication failed: {e}")
        return None
