# Data Service for Stramit Data Export Integration
import sys
import os
from pathlib import Path
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
import logging

# Add the python_data_exporter to the path
current_dir = Path(__file__).parent.parent.parent.parent
exporter_path = current_dir / "python_data_exporter"
if exporter_path.exists():
    sys.path.insert(0, str(exporter_path))

try:
    from exporters.sales_exporter import SalesExporter
    from exporters.customer_exporter import CustomerExporter
    from exporters.leads_exporter import LeadsExporter
    from exporters.address_exporter import AddressExporter
    from config.database import db_manager
    from utils.helpers import categorize_sales_data, categorize_customer_data
except ImportError as e:
    logging.error(f"Failed to import data exporters: {e}")
    # Create mock classes for development
    class SalesExporter:
        def extract_sales_data(self, start_date, end_date): return pd.DataFrame()
        def get_sales_summary(self, start_date, end_date): return {}
    class CustomerExporter:
        def extract_customer_data(self, start_date, end_date): return pd.DataFrame()
    class LeadsExporter:
        def extract_leads_raw_data(self, start_date, end_date): return pd.DataFrame()
        def extract_leads_aggregated_data(self, start_date, end_date): return pd.DataFrame()
    class AddressExporter:
        def extract_address_data(self): return pd.DataFrame()

from .redis_service import redis_service, redis_cache_dataframe, redis_cache

logger = logging.getLogger(__name__)


class DataService:
    """Service for handling data extraction and processing with caching."""
    
    def __init__(self):
        self.sales_exporter = SalesExporter()
        self.customer_exporter = CustomerExporter()
        self.leads_exporter = LeadsExporter()
        self.address_exporter = AddressExporter()
    
    # Sales Data Methods
    @redis_cache_dataframe(ttl=1800, key_prefix="sales_data")  # 30 minutes cache
    def get_sales_data(self, start_date: datetime, end_date: datetime, 
                      category: Optional[str] = None, 
                      distributor_id: Optional[str] = None) -> pd.DataFrame:
        """
        Get sales data with optional filtering.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            category: Optional category filter
            distributor_id: Optional distributor filter
            
        Returns:
            DataFrame with sales data
        """
        try:
            logger.info(f"Extracting sales data from {start_date.date()} to {end_date.date()}")
            
            # Extract base sales data
            df = self.sales_exporter.extract_sales_data(start_date, end_date)
            
            if df is None or df.empty:
                logger.warning("No sales data found")
                return pd.DataFrame()
            
            # Apply filters
            if category:
                df = df[df['Category'] == category] if 'Category' in df.columns else df
            
            if distributor_id:
                df = df[df['dist_id'] == distributor_id] if 'dist_id' in df.columns else df
            
            logger.info(f"Returning {len(df)} sales records")
            return df
            
        except Exception as e:
            logger.error(f"Failed to get sales data: {e}")
            return pd.DataFrame()
    
    @redis_cache(ttl=1800, key_prefix="sales_summary")  # 30 minutes cache
    def get_sales_summary(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get sales summary statistics."""
        try:
            return self.sales_exporter.get_sales_summary(start_date, end_date)
        except Exception as e:
            logger.error(f"Failed to get sales summary: {e}")
            return {}
    
    # Customer Data Methods
    @redis_cache_dataframe(ttl=3600, key_prefix="customer_data")  # 1 hour cache
    def get_customer_data(self, start_date: datetime, end_date: datetime,
                         state: Optional[str] = None,
                         postcode: Optional[str] = None) -> pd.DataFrame:
        """
        Get customer data with optional filtering.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            state: Optional state filter
            postcode: Optional postcode filter
            
        Returns:
            DataFrame with customer data
        """
        try:
            logger.info(f"Extracting customer data from {start_date.date()} to {end_date.date()}")
            
            # Extract base customer data
            df = self.customer_exporter.extract_customer_data(start_date, end_date)
            
            if df is None or df.empty:
                logger.warning("No customer data found")
                return pd.DataFrame()
            
            # Apply filters
            if state:
                df = df[df['State'] == state] if 'State' in df.columns else df
            
            if postcode:
                df = df[df['Postcode'] == postcode] if 'Postcode' in df.columns else df
            
            logger.info(f"Returning {len(df)} customer records")
            return df
            
        except Exception as e:
            logger.error(f"Failed to get customer data: {e}")
            return pd.DataFrame()
    
    # Leads Data Methods
    @redis_cache_dataframe(ttl=3600, key_prefix="leads_raw")  # 1 hour cache
    def get_leads_raw_data(self, start_date: datetime, end_date: datetime,
                          source: Optional[str] = None,
                          status: Optional[str] = None) -> pd.DataFrame:
        """
        Get raw leads data with optional filtering.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            source: Optional source filter
            status: Optional status filter
            
        Returns:
            DataFrame with raw leads data
        """
        try:
            logger.info(f"Extracting raw leads data from {start_date.date()} to {end_date.date()}")
            
            # Extract base leads data
            df = self.leads_exporter.extract_leads_raw_data(start_date, end_date)
            
            if df is None or df.empty:
                logger.warning("No leads data found")
                return pd.DataFrame()
            
            # Apply filters
            if source:
                df = df[df['Source'] == source] if 'Source' in df.columns else df
            
            if status:
                df = df[df['Status'] == status] if 'Status' in df.columns else df
            
            logger.info(f"Returning {len(df)} leads records")
            return df
            
        except Exception as e:
            logger.error(f"Failed to get leads data: {e}")
            return pd.DataFrame()
    
    @redis_cache_dataframe(ttl=3600, key_prefix="leads_aggregated")  # 1 hour cache
    def get_leads_aggregated_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Get aggregated leads data."""
        try:
            logger.info(f"Extracting aggregated leads data from {start_date.date()} to {end_date.date()}")
            return self.leads_exporter.extract_leads_aggregated_data(start_date, end_date)
        except Exception as e:
            logger.error(f"Failed to get aggregated leads data: {e}")
            return pd.DataFrame()
    
    # Heatmap Data Methods
    @redis_cache_dataframe(ttl=3600, key_prefix="heatmap_data")  # 1 hour cache
    def get_heatmap_data(self, start_date: datetime, end_date: datetime,
                        data_type: str = "sales") -> List[Dict[str, Any]]:
        """
        Get processed heatmap data for Australian map visualization.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            data_type: Type of data for heatmap ('sales', 'customers', 'leads')
            
        Returns:
            List of dictionaries with heatmap data points
        """
        try:
            logger.info(f"Generating {data_type} heatmap data")
            
            if data_type == "sales":
                df = self.get_sales_data(start_date, end_date)
                # Process sales data for heatmap
                if not df.empty and 'Postcode' in df.columns and 'Ammount' in df.columns:
                    heatmap_df = df.groupby('Postcode')['Ammount'].sum().reset_index()
                    heatmap_df.columns = ['postcode', 'value']
                else:
                    heatmap_df = pd.DataFrame(columns=['postcode', 'value'])
                    
            elif data_type == "customers":
                df = self.get_customer_data(start_date, end_date)
                # Process customer data for heatmap
                if not df.empty and 'Postcode' in df.columns:
                    heatmap_df = df['Postcode'].value_counts().reset_index()
                    heatmap_df.columns = ['postcode', 'value']
                else:
                    heatmap_df = pd.DataFrame(columns=['postcode', 'value'])
                    
            elif data_type == "leads":
                df = self.get_leads_raw_data(start_date, end_date)
                # Process leads data for heatmap
                if not df.empty and 'Postcode' in df.columns:
                    heatmap_df = df['Postcode'].value_counts().reset_index()
                    heatmap_df.columns = ['postcode', 'value']
                else:
                    heatmap_df = pd.DataFrame(columns=['postcode', 'value'])
            else:
                logger.warning(f"Unknown heatmap data type: {data_type}")
                return []
            
            # Convert to list of dictionaries for API response
            heatmap_data = []
            for _, row in heatmap_df.iterrows():
                heatmap_data.append({
                    'postcode': str(row['postcode']),
                    'value': float(row['value']) if pd.notna(row['value']) else 0.0
                })
            
            logger.info(f"Generated {len(heatmap_data)} heatmap data points")
            return heatmap_data
            
        except Exception as e:
            logger.error(f"Failed to generate heatmap data: {e}")
            return []
    
    # Distributor and Location Data
    @redis_cache(ttl=86400, key_prefix="distributor_locations")  # 24 hours cache
    def get_distributor_locations(self) -> List[Dict[str, Any]]:
        """Get distributor locations for map markers."""
        try:
            logger.info("Extracting distributor locations")
            
            # This would typically come from a distributors table
            # For now, return mock data based on the existing structure
            locations = [
                {"id": "1", "name": "Sydney North", "lat": -33.7969, "lng": 151.2846, "state": "NSW"},
                {"id": "2", "name": "Melbourne East", "lat": -37.8136, "lng": 144.9631, "state": "VIC"},
                {"id": "3", "name": "Brisbane South", "lat": -27.4698, "lng": 153.0251, "state": "QLD"},
                {"id": "4", "name": "Perth Central", "lat": -31.9505, "lng": 115.8605, "state": "WA"},
                {"id": "5", "name": "Adelaide Hills", "lat": -34.9285, "lng": 138.6007, "state": "SA"}
            ]
            
            logger.info(f"Returning {len(locations)} distributor locations")
            return locations
            
        except Exception as e:
            logger.error(f"Failed to get distributor locations: {e}")
            return []
    
    @redis_cache(ttl=86400, key_prefix="postcodes")  # 24 hours cache
    def get_postcodes_data(self) -> List[Dict[str, Any]]:
        """Get Australian postcodes data."""
        try:
            logger.info("Extracting postcodes data")
            
            # Extract address data which contains postcode information
            df = self.address_exporter.extract_address_data()
            
            if df is None or df.empty:
                logger.warning("No address/postcode data found")
                return []
            
            # Process postcodes data
            postcodes = []
            if 'Postcode' in df.columns:
                unique_postcodes = df['Postcode'].dropna().unique()
                for postcode in unique_postcodes:
                    postcodes.append({
                        'postcode': str(postcode),
                        'state': 'Unknown',  # Would need additional lookup
                        'lat': 0.0,  # Would need geocoding
                        'lng': 0.0   # Would need geocoding
                    })
            
            logger.info(f"Returning {len(postcodes)} postcodes")
            return postcodes
            
        except Exception as e:
            logger.error(f"Failed to get postcodes data: {e}")
            return []
    
    def clear_cache(self, cache_type: Optional[str] = None):
        """Clear cached data."""
        try:
            if cache_type:
                pattern = f"{cache_type}:*"
                cleared = redis_service.clear_cache_by_pattern(pattern)
                logger.info(f"Cleared {cleared} cache entries for {cache_type}")
            else:
                # Clear all data-related caches
                patterns = ["sales_data:*", "customer_data:*", "leads_raw:*", 
                           "leads_aggregated:*", "heatmap_data:*", "sales_summary:*"]
                total_cleared = 0
                for pattern in patterns:
                    cleared = redis_service.clear_cache_by_pattern(pattern)
                    total_cleared += cleared
                logger.info(f"Cleared {total_cleared} total cache entries")
                
        except Exception as e:
            logger.error(f"Failed to clear cache: {e}")


# Global data service instance
data_service = DataService()
