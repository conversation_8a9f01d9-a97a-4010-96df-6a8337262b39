# Export Service for Background Job Processing
import os
import uuid
import asyncio
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
import logging
import json
import zipfile

from ..config.settings import settings
from ..services.data_service import data_service
from ..services.redis_service import redis_service, export_job_tracker
from ..models.export import (
    ExportJobRequest, ExportJobResponse, ExportJobStatus, ExportTask,
    ExportStatus, ExportType, ExportFormat, ExportSummary
)

logger = logging.getLogger(__name__)


class ExportService:
    """Service for handling background export jobs."""
    
    def __init__(self):
        self.export_dir = Path(settings.export_files_path)
        self.export_dir.mkdir(parents=True, exist_ok=True)
        
        # Maximum file age in days
        self.max_file_age_days = 7
        
        # File size limits (in MB)
        self.max_file_size_mb = 100
    
    async def start_export_job(self, request: ExportJobRequest, user: str) -> ExportJobResponse:
        """
        Start a new export job.
        
        Args:
            request: Export job request
            user: Username of the requesting user
            
        Returns:
            Export job response with job ID and status
        """
        try:
            # Generate unique job ID
            job_id = str(uuid.uuid4())
            
            # Create job parameters
            params = {
                "export_type": request.export_type.value,
                "export_format": request.export_format.value,
                "start_date": request.start_date.isoformat() if request.start_date else None,
                "end_date": request.end_date.isoformat() if request.end_date else None,
                "category": request.category,
                "distributor_id": request.distributor_id,
                "state": request.state,
                "postcode": request.postcode,
                "source": request.source,
                "status": request.status,
                "include_summary": request.include_summary,
                "compress": request.compress
            }
            
            # Create job in Redis
            success = export_job_tracker.create_job(
                job_id=job_id,
                job_type=request.export_type.value,
                user=user,
                params=params
            )
            
            if not success:
                raise Exception("Failed to create job in tracking system")
            
            # Start background processing
            asyncio.create_task(self._process_export_job(job_id, request, user))
            
            logger.info(f"Export job {job_id} started for user {user}")
            
            return ExportJobResponse(
                job_id=job_id,
                status=ExportStatus.PENDING,
                message="Export job started successfully",
                estimated_completion=datetime.now() + timedelta(minutes=5)
            )
            
        except Exception as e:
            logger.error(f"Failed to start export job: {e}")
            raise
    
    async def _process_export_job(self, job_id: str, request: ExportJobRequest, user: str):
        """
        Process export job in background.
        
        Args:
            job_id: Job identifier
            request: Export job request
            user: Username
        """
        try:
            # Update job status to processing
            export_job_tracker.update_job_status(job_id, "processing", progress=0)
            
            logger.info(f"Processing export job {job_id}")
            
            # Get date range
            if request.start_date and request.end_date:
                start_date = datetime.combine(request.start_date, datetime.min.time())
                end_date = datetime.combine(request.end_date, datetime.max.time())
            else:
                # Default to last year
                end_date = datetime.now()
                start_date = end_date.replace(year=end_date.year - 1)
            
            # Extract data based on export type
            export_job_tracker.update_job_status(job_id, "processing", progress=20)
            
            if request.export_type == ExportType.SALES:
                df = data_service.get_sales_data(
                    start_date, end_date, 
                    request.category, request.distributor_id
                )
                summary_data = data_service.get_sales_summary(start_date, end_date)
                
            elif request.export_type == ExportType.CUSTOMERS:
                df = data_service.get_customer_data(
                    start_date, end_date,
                    request.state, request.postcode
                )
                summary_data = {"total_records": len(df)}
                
            elif request.export_type == ExportType.LEADS:
                df = data_service.get_leads_raw_data(
                    start_date, end_date,
                    request.source, request.status
                )
                summary_data = {"total_records": len(df)}
                
            elif request.export_type == ExportType.ALL:
                # Export all data types
                sales_df = data_service.get_sales_data(start_date, end_date)
                customer_df = data_service.get_customer_data(start_date, end_date)
                leads_df = data_service.get_leads_raw_data(start_date, end_date)
                
                df = {
                    "sales": sales_df,
                    "customers": customer_df,
                    "leads": leads_df
                }
                summary_data = {
                    "sales_records": len(sales_df),
                    "customer_records": len(customer_df),
                    "leads_records": len(leads_df)
                }
            else:
                raise ValueError(f"Unknown export type: {request.export_type}")
            
            export_job_tracker.update_job_status(job_id, "processing", progress=60)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"{request.export_type.value}_export_{timestamp}"
            
            # Save data to file
            file_path = await self._save_export_data(
                df, base_filename, request.export_format,
                summary_data if request.include_summary else None,
                request.compress
            )
            
            export_job_tracker.update_job_status(job_id, "processing", progress=90)
            
            # Get file size
            file_size = file_path.stat().st_size if file_path.exists() else 0
            
            # Update job as completed
            export_job_tracker.update_job_status(
                job_id, "completed", progress=100,
                file_path=str(file_path.relative_to(self.export_dir))
            )
            
            logger.info(f"Export job {job_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Export job {job_id} failed: {e}")
            export_job_tracker.update_job_status(
                job_id, "failed", error=str(e)
            )
    
    async def _save_export_data(self, data: Any, base_filename: str, 
                               export_format: ExportFormat, summary: Optional[Dict] = None,
                               compress: bool = False) -> Path:
        """
        Save export data to file.
        
        Args:
            data: Data to export (DataFrame or dict of DataFrames)
            base_filename: Base filename without extension
            export_format: Export format
            summary: Optional summary data
            compress: Whether to compress the file
            
        Returns:
            Path to saved file
        """
        try:
            if export_format == ExportFormat.CSV:
                if isinstance(data, dict):
                    # Multiple DataFrames - create zip file
                    zip_path = self.export_dir / f"{base_filename}.zip"
                    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                        for name, df in data.items():
                            csv_content = df.to_csv(index=False)
                            zf.writestr(f"{name}.csv", csv_content)
                        
                        if summary:
                            summary_content = json.dumps(summary, indent=2, default=str)
                            zf.writestr("summary.json", summary_content)
                    
                    return zip_path
                else:
                    # Single DataFrame
                    file_path = self.export_dir / f"{base_filename}.csv"
                    data.to_csv(file_path, index=False)
                    
                    if compress:
                        zip_path = self.export_dir / f"{base_filename}.zip"
                        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                            zf.write(file_path, file_path.name)
                            if summary:
                                summary_content = json.dumps(summary, indent=2, default=str)
                                zf.writestr("summary.json", summary_content)
                        
                        # Remove original CSV file
                        file_path.unlink()
                        return zip_path
                    
                    return file_path
                    
            elif export_format == ExportFormat.EXCEL:
                file_path = self.export_dir / f"{base_filename}.xlsx"
                
                if isinstance(data, dict):
                    # Multiple sheets
                    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                        for name, df in data.items():
                            df.to_excel(writer, sheet_name=name, index=False)
                        
                        if summary:
                            summary_df = pd.DataFrame([summary])
                            summary_df.to_excel(writer, sheet_name='Summary', index=False)
                else:
                    # Single sheet
                    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                        data.to_excel(writer, sheet_name='Data', index=False)
                        
                        if summary:
                            summary_df = pd.DataFrame([summary])
                            summary_df.to_excel(writer, sheet_name='Summary', index=False)
                
                return file_path
                
            elif export_format == ExportFormat.JSON:
                file_path = self.export_dir / f"{base_filename}.json"
                
                if isinstance(data, dict):
                    # Multiple data types
                    export_data = {}
                    for name, df in data.items():
                        export_data[name] = df.to_dict('records')
                    
                    if summary:
                        export_data['summary'] = summary
                else:
                    # Single DataFrame
                    export_data = {
                        'data': data.to_dict('records'),
                        'summary': summary
                    }
                
                with open(file_path, 'w') as f:
                    json.dump(export_data, f, indent=2, default=str)
                
                return file_path
            
            else:
                raise ValueError(f"Unsupported export format: {export_format}")
                
        except Exception as e:
            logger.error(f"Failed to save export data: {e}")
            raise
    
    def get_job_status(self, job_id: str) -> Optional[ExportJobStatus]:
        """Get export job status."""
        try:
            job_data = export_job_tracker.get_job_status(job_id)
            if not job_data:
                return None
            
            # Convert to ExportJobStatus model
            status = ExportJobStatus(
                job_id=job_data['job_id'],
                job_type=job_data['job_type'],
                status=ExportStatus(job_data['status']),
                progress=job_data['progress'],
                created_at=datetime.fromisoformat(job_data['created_at']),
                updated_at=datetime.fromisoformat(job_data['updated_at']),
                user=job_data['user'],
                params=job_data['params'],
                file_path=job_data.get('file_path'),
                error=job_data.get('error')
            )
            
            # Add download URL if file is ready
            if status.file_path and status.status == ExportStatus.COMPLETED:
                status.download_url = f"/api/export/download/{job_id}"
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to get job status: {e}")
            return None
    
    def get_user_jobs(self, user: str, limit: int = 50) -> List[ExportJobStatus]:
        """Get export jobs for a specific user."""
        try:
            jobs_data = export_job_tracker.get_user_jobs(user)
            
            jobs = []
            for job_data in jobs_data[:limit]:
                try:
                    status = ExportJobStatus(
                        job_id=job_data['job_id'],
                        job_type=job_data['job_type'],
                        status=ExportStatus(job_data['status']),
                        progress=job_data['progress'],
                        created_at=datetime.fromisoformat(job_data['created_at']),
                        updated_at=datetime.fromisoformat(job_data['updated_at']),
                        user=job_data['user'],
                        params=job_data['params'],
                        file_path=job_data.get('file_path'),
                        error=job_data.get('error')
                    )
                    
                    # Add download URL if file is ready
                    if status.file_path and status.status == ExportStatus.COMPLETED:
                        status.download_url = f"/api/export/download/{status.job_id}"
                    
                    jobs.append(status)
                    
                except Exception as e:
                    logger.warning(f"Failed to parse job data: {e}")
                    continue
            
            return jobs
            
        except Exception as e:
            logger.error(f"Failed to get user jobs: {e}")
            return []
    
    def get_export_file_path(self, job_id: str) -> Optional[Path]:
        """Get the file path for a completed export job."""
        try:
            job_data = export_job_tracker.get_job_status(job_id)
            if not job_data or job_data['status'] != 'completed':
                return None
            
            file_path = job_data.get('file_path')
            if not file_path:
                return None
            
            full_path = self.export_dir / file_path
            if full_path.exists():
                return full_path
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get export file path: {e}")
            return None
    
    def cleanup_old_files(self) -> int:
        """Clean up old export files and jobs."""
        try:
            deleted_files = 0
            cutoff_date = datetime.now() - timedelta(days=self.max_file_age_days)
            
            # Clean up files
            for file_path in self.export_dir.glob("*"):
                if file_path.is_file():
                    file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_mtime < cutoff_date:
                        try:
                            file_path.unlink()
                            deleted_files += 1
                            logger.info(f"Deleted old export file: {file_path.name}")
                        except Exception as e:
                            logger.warning(f"Failed to delete file {file_path}: {e}")
            
            # Clean up old job records
            deleted_jobs = export_job_tracker.cleanup_old_jobs(self.max_file_age_days)
            
            logger.info(f"Cleanup completed: {deleted_files} files, {deleted_jobs} jobs")
            return deleted_files + deleted_jobs
            
        except Exception as e:
            logger.error(f"Failed to cleanup old files: {e}")
            return 0


# Global export service instance
export_service = ExportService()
