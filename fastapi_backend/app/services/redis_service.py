# Redis Service for Caching and Session Management
import redis
import json
import pickle
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Any, Dict, List, Union
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    pd = None
import logging
from functools import wraps
import asyncio
from contextlib import asynccontextmanager

from ..config.settings import settings

logger = logging.getLogger(__name__)


class RedisService:
    """Redis service for caching, session management, and job tracking."""
    
    def __init__(self):
        self.redis_client = None
        self.connection_pool = None
        self.connected = False
        self._initialize_connection()
    
    def _initialize_connection(self):
        """Initialize Redis connection with connection pooling."""
        try:
            # Create connection pool
            self.connection_pool = redis.ConnectionPool.from_url(
                settings.redis_url,
                max_connections=20,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={}
            )

            # Create Redis client
            self.redis_client = redis.Redis(
                connection_pool=self.connection_pool,
                decode_responses=False  # We'll handle encoding manually
            )

            # Test connection
            self.redis_client.ping()
            self.connected = True
            logger.info("Redis connection established successfully")

        except redis.ConnectionError as e:
            logger.warning(f"Failed to connect to Redis: {e}. Running without Redis.")
            self.connected = False
            self.redis_client = None
        except Exception as e:
            logger.warning(f"Unexpected Redis connection error: {e}. Running without Redis.")
            self.connected = False
            self.redis_client = None
    
    def get_cache_key(self, prefix: str, **kwargs) -> str:
        """Generate consistent cache keys."""
        # Create a hash of the parameters for consistent keys
        params_str = json.dumps(kwargs, sort_keys=True, default=str)
        params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
        return f"{prefix}:{params_hash}"
    
    def set_cache(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """
        Cache any serializable value with TTL.
        
        Args:
            key: Cache key
            value: Value to cache (will be pickled)
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        if not self.connected:
            return False

        try:
            # Serialize value using pickle for complex objects
            pickled_value = pickle.dumps(value)
            result = self.redis_client.setex(key, ttl, pickled_value)
            
            if result:
                logger.debug(f"Cached value with key: {key} (TTL: {ttl}s)")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Failed to cache value for key {key}: {e}")
            return False
    
    def get_cache(self, key: str) -> Optional[Any]:
        """
        Retrieve cached value.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found/expired
        """
        if not self.connected:
            return None

        try:
            pickled_value = self.redis_client.get(key)
            if pickled_value:
                value = pickle.loads(pickled_value)
                logger.debug(f"Retrieved cached value for key: {key}")
                return value
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve cached value for key {key}: {e}")
            return None
    
    def delete_cache(self, key: str) -> bool:
        """Delete cached value."""
        try:
            result = self.redis_client.delete(key)
            if result:
                logger.debug(f"Deleted cache key: {key}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete cache key {key}: {e}")
            return False
    
    def cache_dataframe(self, key: str, df: pd.DataFrame, ttl: int = 3600) -> bool:
        """
        Cache pandas DataFrame efficiently.
        
        Args:
            key: Cache key
            df: DataFrame to cache
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert DataFrame to dict for more efficient storage
            df_dict = {
                'data': df.to_dict('records'),
                'columns': df.columns.tolist(),
                'index': df.index.tolist(),
                'dtypes': df.dtypes.to_dict()
            }
            
            return self.set_cache(key, df_dict, ttl)
            
        except Exception as e:
            logger.error(f"Failed to cache DataFrame: {e}")
            return False
    
    def get_cached_dataframe(self, key: str) -> Optional[pd.DataFrame]:
        """
        Retrieve cached DataFrame.
        
        Args:
            key: Cache key
            
        Returns:
            DataFrame or None if not found
        """
        try:
            df_dict = self.get_cache(key)
            if df_dict and isinstance(df_dict, dict):
                # Reconstruct DataFrame
                df = pd.DataFrame(df_dict['data'])
                
                # Restore column order if available
                if 'columns' in df_dict:
                    df = df.reindex(columns=df_dict['columns'])
                
                # Restore data types if available
                if 'dtypes' in df_dict:
                    for col, dtype in df_dict['dtypes'].items():
                        if col in df.columns:
                            try:
                                df[col] = df[col].astype(dtype)
                            except Exception:
                                pass  # Skip if conversion fails
                
                logger.debug(f"Retrieved cached DataFrame: {key}")
                return df
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve cached DataFrame: {e}")
            return None
    
    def set_json(self, key: str, data: Dict, ttl: int = 3600) -> bool:
        """Cache JSON-serializable data."""
        try:
            json_data = json.dumps(data, default=str)
            result = self.redis_client.setex(key, ttl, json_data.encode('utf-8'))
            
            if result:
                logger.debug(f"Cached JSON data: {key}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Failed to cache JSON: {e}")
            return False
    
    def get_json(self, key: str) -> Optional[Dict]:
        """Retrieve cached JSON data."""
        try:
            json_data = self.redis_client.get(key)
            if json_data:
                data = json.loads(json_data.decode('utf-8'))
                logger.debug(f"Retrieved cached JSON: {key}")
                return data
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve cached JSON: {e}")
            return None
    
    def store_session(self, session_id: str, user_data: Dict, ttl: int = None) -> bool:
        """
        Store user session data.
        
        Args:
            session_id: Session identifier
            user_data: User session data
            ttl: Time to live (defaults to token expiration time)
            
        Returns:
            True if successful, False otherwise
        """
        if ttl is None:
            ttl = settings.access_token_expire_minutes * 60
        
        session_key = f"session:{session_id}"
        return self.set_json(session_key, user_data, ttl)
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """Retrieve user session data."""
        session_key = f"session:{session_id}"
        return self.get_json(session_key)
    
    def delete_session(self, session_id: str) -> bool:
        """Delete user session."""
        session_key = f"session:{session_id}"
        return self.delete_cache(session_key)
    
    def publish_message(self, channel: str, message: Dict) -> bool:
        """Publish message to Redis channel for real-time notifications."""
        try:
            message_json = json.dumps(message, default=str)
            result = self.redis_client.publish(channel, message_json)
            logger.debug(f"Published message to channel {channel}: {result} subscribers")
            return True
        except Exception as e:
            logger.error(f"Failed to publish message: {e}")
            return False
    
    def get_keys_by_pattern(self, pattern: str) -> List[str]:
        """Get all keys matching a pattern."""
        try:
            keys = self.redis_client.keys(pattern)
            return [key.decode('utf-8') if isinstance(key, bytes) else key for key in keys]
        except Exception as e:
            logger.error(f"Failed to get keys by pattern {pattern}: {e}")
            return []
    
    def clear_cache_by_pattern(self, pattern: str) -> int:
        """Clear all cache entries matching a pattern."""
        try:
            keys = self.get_keys_by_pattern(pattern)
            if keys:
                deleted = self.redis_client.delete(*keys)
                logger.info(f"Cleared {deleted} cache entries matching pattern: {pattern}")
                return deleted
            return 0
        except Exception as e:
            logger.error(f"Failed to clear cache by pattern {pattern}: {e}")
            return 0
    
    def health_check(self) -> Dict[str, Any]:
        """Check Redis health and return status information."""
        try:
            # Test basic operations
            test_key = "health_check_test"
            test_value = {"timestamp": datetime.now().isoformat()}
            
            # Test set/get
            self.set_json(test_key, test_value, 10)
            retrieved = self.get_json(test_key)
            self.delete_cache(test_key)
            
            # Get Redis info
            info = self.redis_client.info()
            
            return {
                "status": "healthy",
                "connected": True,
                "test_passed": retrieved is not None,
                "redis_version": info.get("redis_version"),
                "used_memory": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "total_commands_processed": info.get("total_commands_processed")
            }
            
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return {
                "status": "unhealthy",
                "connected": False,
                "error": str(e)
            }


# Global Redis service instance
redis_service = RedisService()


# Cache decorator for functions
def redis_cache(ttl: int = 3600, key_prefix: str = "cache"):
    """
    Decorator to cache function results in Redis.
    
    Args:
        ttl: Time to live in seconds
        key_prefix: Cache key prefix
    """
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Generate cache key from function name and parameters
            cache_key = redis_service.get_cache_key(
                f"{key_prefix}:{func.__name__}",
                args=args,
                kwargs=kwargs
            )
            
            # Try to get from cache first
            cached_result = redis_service.get_cache(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {func.__name__}")
                return cached_result
            
            # Execute function and cache result
            logger.debug(f"Cache miss for {func.__name__}, executing function")
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            if result is not None:
                redis_service.set_cache(cache_key, result, ttl)
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # Generate cache key from function name and parameters
            cache_key = redis_service.get_cache_key(
                f"{key_prefix}:{func.__name__}",
                args=args,
                kwargs=kwargs
            )
            
            # Try to get from cache first
            cached_result = redis_service.get_cache(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {func.__name__}")
                return cached_result
            
            # Execute function and cache result
            logger.debug(f"Cache miss for {func.__name__}, executing function")
            result = func(*args, **kwargs)
            
            if result is not None:
                redis_service.set_cache(cache_key, result, ttl)
            
            return result
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# DataFrame cache decorator
def redis_cache_dataframe(ttl: int = 3600, key_prefix: str = "df_cache"):
    """
    Decorator to cache DataFrame results in Redis.

    Args:
        ttl: Time to live in seconds
        key_prefix: Cache key prefix
    """
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            cache_key = redis_service.get_cache_key(
                f"{key_prefix}:{func.__name__}",
                args=args,
                kwargs=kwargs
            )

            # Try to get from cache first
            cached_df = redis_service.get_cached_dataframe(cache_key)
            if cached_df is not None:
                logger.debug(f"DataFrame cache hit for {func.__name__}")
                return cached_df

            # Execute function and cache result
            logger.debug(f"DataFrame cache miss for {func.__name__}, executing function")
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)

            if result is not None and isinstance(result, pd.DataFrame):
                redis_service.cache_dataframe(cache_key, result, ttl)

            return result

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            cache_key = redis_service.get_cache_key(
                f"{key_prefix}:{func.__name__}",
                args=args,
                kwargs=kwargs
            )

            # Try to get from cache first
            cached_df = redis_service.get_cached_dataframe(cache_key)
            if cached_df is not None:
                logger.debug(f"DataFrame cache hit for {func.__name__}")
                return cached_df

            # Execute function and cache result
            logger.debug(f"DataFrame cache miss for {func.__name__}, executing function")
            result = func(*args, **kwargs)

            if result is not None and isinstance(result, pd.DataFrame):
                redis_service.cache_dataframe(cache_key, result, ttl)

            return result

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


# Export Job Tracker using Redis
class ExportJobTracker:
    """Track export job progress and status using Redis."""

    def __init__(self, redis_service: RedisService):
        self.redis = redis_service
        self.job_prefix = "export_job"

    def create_job(self, job_id: str, job_type: str, user: str, params: Dict) -> bool:
        """Create a new export job."""
        job_data = {
            "job_id": job_id,
            "job_type": job_type,
            "user": user,
            "params": params,
            "status": "pending",
            "progress": 0,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "file_path": None,
            "error": None
        }

        job_key = f"{self.job_prefix}:{job_id}"
        return self.redis.set_json(job_key, job_data, ttl=86400)  # 24 hours

    def update_job_status(self, job_id: str, status: str, progress: int = None,
                         file_path: str = None, error: str = None) -> bool:
        """Update job status and progress."""
        job_key = f"{self.job_prefix}:{job_id}"
        job_data = self.redis.get_json(job_key)

        if not job_data:
            return False

        job_data["status"] = status
        job_data["updated_at"] = datetime.now().isoformat()

        if progress is not None:
            job_data["progress"] = progress
        if file_path is not None:
            job_data["file_path"] = file_path
        if error is not None:
            job_data["error"] = error

        return self.redis.set_json(job_key, job_data, ttl=86400)

    def get_job_status(self, job_id: str) -> Optional[Dict]:
        """Get job status and details."""
        job_key = f"{self.job_prefix}:{job_id}"
        return self.redis.get_json(job_key)

    def get_user_jobs(self, user: str) -> List[Dict]:
        """Get all jobs for a specific user."""
        pattern = f"{self.job_prefix}:*"
        job_keys = self.redis.get_keys_by_pattern(pattern)

        user_jobs = []
        for key in job_keys:
            job_data = self.redis.get_json(key)
            if job_data and job_data.get("user") == user:
                user_jobs.append(job_data)

        # Sort by created_at descending
        user_jobs.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        return user_jobs

    def cleanup_old_jobs(self, days: int = 7) -> int:
        """Clean up jobs older than specified days."""
        cutoff_date = datetime.now() - timedelta(days=days)
        pattern = f"{self.job_prefix}:*"
        job_keys = self.redis.get_keys_by_pattern(pattern)

        deleted_count = 0
        for key in job_keys:
            job_data = self.redis.get_json(key)
            if job_data:
                created_at = datetime.fromisoformat(job_data.get("created_at", ""))
                if created_at < cutoff_date:
                    if self.redis.delete_cache(key):
                        deleted_count += 1

        return deleted_count


# Global export job tracker
export_job_tracker = ExportJobTracker(redis_service)
