# Application Settings Configuration
from pydantic_settings import BaseSettings
from pydantic import Field
from typing import List, Optional
import os
from pathlib import Path


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Application Settings
    app_name: str = "Stramit Data Export API"
    app_version: str = "1.0.0"
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 8000
    
    # Security Settings
    secret_key: str = "your-super-secret-key-change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Redis Configuration
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    
    # Database Configuration - CDB
    db_cdb_host: str = "localhost"
    db_cdb_port: int = 3306
    db_cdb_user: str = "AccessTwo"
    db_cdb_password: str = "gxm7e25cnw"
    db_cdb_name: str = "cdb"
    
    # Database Configuration - App
    db_app_host: str = "localhost"
    db_app_port: int = 3306
    db_app_user: str = "jmtuser"
    db_app_password: str = "xd7=fcHV@*V2q7j!"
    db_app_name: str = "app"
    
    # Database Configuration - Reporting
    db_reporting_host: str = "localhost"
    db_reporting_port: int = 3306
    db_reporting_user: str = "jmtuser"
    db_reporting_password: str = "xd7=fcHV@*V2q7j!"
    db_reporting_name: str = "ReportingDB"
    
    # Database Configuration - App Secondary
    db_app_secondary_host: str = "localhost"
    db_app_secondary_port: int = 3306
    db_app_secondary_user: str = "jmtuser"
    db_app_secondary_password: str = "xd7=fcHV@*V2q7j!"
    db_app_secondary_name: str = "app"
    
    # File Storage
    export_files_path: str = "./exports"
    users_file_path: str = "./data/users.json"
    
    # Rate Limiting
    rate_limit_requests: int = 100
    rate_limit_period: int = 60
    rate_limit_per_minute: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
    rate_limit_per_hour: int = Field(default=1000, env="RATE_LIMIT_PER_HOUR")

    # CORS Settings
    cors_origins: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    cors_allow_credentials: bool = True
    cors_allow_methods: List[str] = ["*"]
    cors_allow_headers: List[str] = ["*"]
    allowed_hosts: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        env="ALLOWED_HOSTS"
    )

    # Security settings
    admin_ip_whitelist: Optional[List[str]] = Field(
        default=None,
        env="ADMIN_IP_WHITELIST"
    )
    
    @property
    def redis_url(self) -> str:
        """Get Redis connection URL."""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"
    
    @property
    def cdb_connection_params(self) -> dict:
        """Get CDB database connection parameters."""
        return {
            "host": self.db_cdb_host,
            "port": self.db_cdb_port,
            "user": self.db_cdb_user,
            "password": self.db_cdb_password,
            "database": self.db_cdb_name,
            "charset": "utf8mb4",
            "autocommit": True
        }
    
    @property
    def app_connection_params(self) -> dict:
        """Get App database connection parameters."""
        return {
            "host": self.db_app_host,
            "port": self.db_app_port,
            "user": self.db_app_user,
            "password": self.db_app_password,
            "database": self.db_app_name,
            "charset": "utf8mb4",
            "autocommit": True
        }
    
    @property
    def reporting_connection_params(self) -> dict:
        """Get Reporting database connection parameters."""
        return {
            "host": self.db_reporting_host,
            "port": self.db_reporting_port,
            "user": self.db_reporting_user,
            "password": self.db_reporting_password,
            "database": self.db_reporting_name,
            "charset": "utf8mb4",
            "autocommit": True
        }
    
    @property
    def app_secondary_connection_params(self) -> dict:
        """Get App Secondary database connection parameters."""
        return {
            "host": self.db_app_secondary_host,
            "port": self.db_app_secondary_port,
            "user": self.db_app_secondary_user,
            "password": self.db_app_secondary_password,
            "database": self.db_app_secondary_name,
            "charset": "utf8mb4",
            "autocommit": True
        }
    
    def ensure_directories(self):
        """Ensure required directories exist."""
        Path(self.export_files_path).mkdir(parents=True, exist_ok=True)
        Path(self.users_file_path).parent.mkdir(parents=True, exist_ok=True)
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()

# Ensure directories exist on import
settings.ensure_directories()
