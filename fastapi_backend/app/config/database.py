# Database Configuration and Connection Management
import mysql.connector
from mysql.connector import <PERSON>rror
from contextlib import contextmanager
from typing import Generator, Optional, Dict, Any
import logging
from .settings import settings

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Database connection manager for multiple MySQL databases."""
    
    def __init__(self):
        self.connection_configs = {
            'cdb': settings.cdb_connection_params,
            'app': settings.app_connection_params,
            'reporting': settings.reporting_connection_params,
            'app_secondary': settings.app_secondary_connection_params
        }
    
    @contextmanager
    def get_connection(self, db_name: str) -> Generator[mysql.connector.MySQLConnection, None, None]:
        """
        Get database connection with context manager.
        
        Args:
            db_name: Database name ('cdb', 'app', 'reporting', 'app_secondary')
            
        Yields:
            MySQL connection object
        """
        if db_name not in self.connection_configs:
            raise ValueError(f"Unknown database: {db_name}")
        
        connection = None
        try:
            config = self.connection_configs[db_name]
            logger.info(f"Connecting to {db_name} database at {config['host']}:{config['port']}")
            
            connection = mysql.connector.connect(**config)
            
            if connection.is_connected():
                logger.info(f"Successfully connected to {db_name} database")
                yield connection
            else:
                raise Error(f"Failed to connect to {db_name} database")
                
        except Error as e:
            logger.error(f"Database connection error for {db_name}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error connecting to {db_name}: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
                logger.info(f"Closed connection to {db_name} database")
    
    def test_connection(self, db_name: str) -> bool:
        """
        Test database connection.
        
        Args:
            db_name: Database name to test
            
        Returns:
            True if connection successful, False otherwise
        """
        try:
            with self.get_connection(db_name) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                return result is not None
        except Exception as e:
            logger.error(f"Connection test failed for {db_name}: {e}")
            return False
    
    def test_all_connections(self) -> Dict[str, bool]:
        """
        Test all database connections.
        
        Returns:
            Dictionary with connection test results for each database
        """
        results = {}
        for db_name in self.connection_configs.keys():
            results[db_name] = self.test_connection(db_name)
        return results
    
    def execute_query(self, db_name: str, query: str, params: Optional[tuple] = None) -> list:
        """
        Execute a SELECT query and return results.
        
        Args:
            db_name: Database name
            query: SQL query string
            params: Query parameters
            
        Returns:
            List of query results
        """
        try:
            with self.get_connection(db_name) as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(query, params or ())
                results = cursor.fetchall()
                cursor.close()
                return results
        except Exception as e:
            logger.error(f"Query execution failed for {db_name}: {e}")
            raise
    
    def execute_query_with_cursor(self, db_name: str, query: str, params: Optional[tuple] = None):
        """
        Execute query and return cursor for streaming results.
        
        Args:
            db_name: Database name
            query: SQL query string
            params: Query parameters
            
        Yields:
            Database cursor for streaming results
        """
        try:
            with self.get_connection(db_name) as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(query, params or ())
                yield cursor
                cursor.close()
        except Exception as e:
            logger.error(f"Streaming query execution failed for {db_name}: {e}")
            raise


# Global database manager instance
db_manager = DatabaseManager()


# Compatibility functions for existing exporters
@contextmanager
def get_cdb_connection():
    """Get CDB database connection (compatibility with existing code)."""
    with db_manager.get_connection('cdb') as conn:
        yield conn


@contextmanager
def get_app_connection():
    """Get App database connection (compatibility with existing code)."""
    with db_manager.get_connection('app') as conn:
        yield conn


@contextmanager
def get_reporting_connection():
    """Get Reporting database connection (compatibility with existing code)."""
    with db_manager.get_connection('reporting') as conn:
        yield conn


@contextmanager
def get_app_secondary_connection():
    """Get App Secondary database connection (compatibility with existing code)."""
    with db_manager.get_connection('app_secondary') as conn:
        yield conn
