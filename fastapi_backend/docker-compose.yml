# Docker Compose for FastAPI Backend System
version: '3.8'

services:
  # Fast<PERSON>I Backend
  fastapi-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: stramit-fastapi-backend
    ports:
      - "8000:8000"
    environment:
      # Database connections (use your actual values)
      - CDB_HOST=host.docker.internal
      - CDB_PORT=3306
      - CDB_DATABASE=CDB
      - CDB_USERNAME=your_username
      - CDB_PASSWORD=your_password
      
      - APP_HOST=host.docker.internal
      - APP_PORT=3306
      - APP_DATABASE=app
      - APP_USERNAME=your_username
      - APP_PASSWORD=your_password
      
      - REPORTING_HOST=host.docker.internal
      - REPORTING_PORT=3306
      - REPORTING_DATABASE=ReportingDB
      - REPORTING_USERNAME=your_username
      - REPORTING_PASSWORD=your_password
      
      - APP_SECONDARY_HOST=host.docker.internal
      - APP_SECONDARY_PORT=3306
      - APP_SECONDARY_DATABASE=app
      - APP_SECONDARY_USERNAME=your_username
      - APP_SECONDARY_PASSWORD=your_password
      
      # Redis connection
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password_123
      
      # Application settings
      - DEBUG=false
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
      - HOST=0.0.0.0
      - PORT=8000
      
      # CORS settings
      - CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000
      - ALLOWED_HOSTS=localhost,127.0.0.1,fastapi-backend
      
      # File paths (inside container)
      - EXPORT_FILES_PATH=/app/data/exports
      - LOG_FILES_PATH=/app/data/logs
      - CACHE_FILES_PATH=/app/data/cache
      - USER_DATA_FILE=/app/data/users.json
    
    volumes:
      - ./data/exports:/app/data/exports
      - ./data/logs:/app/data/logs
      - ./data/cache:/app/data/cache
      - ./data/users.json:/app/data/users.json
    
    depends_on:
      - redis
    
    networks:
      - stramit-network
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: stramit-redis
    ports:
      - "6379:6379"
    command: redis-server --requirepass redis_password_123 --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - stramit-network
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: stramit-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - fastapi-backend
    networks:
      - stramit-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  redis_data:
    driver: local

networks:
  stramit-network:
    driver: bridge
