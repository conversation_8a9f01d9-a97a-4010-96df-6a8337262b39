# Stramit Data Export FastAPI Backend

A comprehensive FastAPI backend system for the Stramit data export and visualization platform, featuring JWT authentication, Redis caching, background export jobs, and Australian map visualization data.

## Features

- **JWT Authentication**: Secure user authentication with role-based access control
- **Data Export**: Sales, customer, leads, and location data export in multiple formats
- **Background Jobs**: Asynchronous export processing with progress tracking
- **Redis Caching**: Intelligent caching with configurable TTL for different data types
- **Australian Maps**: Postcode and distributor location data for map visualization
- **Docker Support**: Complete containerization with docker-compose
- **Security**: Rate limiting, CORS, security headers, and IP whitelisting
- **Monitoring**: Comprehensive logging, health checks, and request tracking

## Quick Start

### Prerequisites

- Python 3.11+
- Redis server
- MySQL databases (4 connections: CDB, App, Reporting, App Secondary)
- Docker and Docker Compose (optional)

### Installation

1. **Clone and setup**:
```bash
git clone <repository>
cd fastapi_backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Configure environment**:
```bash
cp .env.example .env
# Edit .env with your database and Redis credentials
```

3. **Create data directories**:
```bash
mkdir -p data/exports data/logs data/cache
```

4. **Run the application**:
```bash
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Docker Deployment

1. **Configure environment**:
```bash
# Edit docker-compose.yml with your database credentials
```

2. **Start services**:
```bash
docker-compose up -d
```

3. **Access the API**:
- API: http://localhost:8000
- Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health

## API Documentation

### Authentication

All API endpoints (except `/health` and `/`) require JWT authentication.

**Login**:
```bash
POST /auth/login
{
  "username": "admin",
  "password": "admin123"
}
```

**Use token**:
```bash
Authorization: Bearer <your-jwt-token>
```

### Data Endpoints

- `GET /api/sales-data` - Export sales data with filters
- `GET /api/customer-data` - Export customer information
- `GET /api/leads-data` - Export leads and quotes data
- `GET /api/heatmap-data` - Get heatmap visualization data
- `GET /api/distributor-locations` - Get distributor locations for maps
- `GET /api/postcodes` - Get Australian postcode data

### Export Jobs

- `POST /api/export/start` - Start background export job
- `GET /api/export/status/{job_id}` - Check export job status
- `GET /api/export/download/{job_id}` - Download completed export
- `GET /api/export/jobs` - List user's export jobs
- `DELETE /api/export/cancel/{job_id}` - Cancel running job

### Example Requests

**Start Export Job**:
```bash
POST /api/export/start
{
  "export_type": "sales",
  "export_format": "csv",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "filters": {
    "category": "FDB Sheds"
  },
  "options": {
    "include_summary": true,
    "compress": false
  }
}
```

**Get Sales Data**:
```bash
GET /api/sales-data?start_date=2024-01-01&end_date=2024-12-31&category=FDB%20Sheds
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DEBUG` | Enable debug mode | `false` |
| `SECRET_KEY` | Application secret key | Required |
| `JWT_SECRET_KEY` | JWT signing key | Required |
| `HOST` | Server host | `0.0.0.0` |
| `PORT` | Server port | `8000` |
| `REDIS_HOST` | Redis server host | `localhost` |
| `REDIS_PORT` | Redis server port | `6379` |
| `REDIS_PASSWORD` | Redis password | Optional |
| `CDB_HOST` | CDB database host | Required |
| `CDB_USERNAME` | CDB database user | Required |
| `CDB_PASSWORD` | CDB database password | Required |
| `APP_HOST` | App database host | Required |
| `REPORTING_HOST` | Reporting database host | Required |
| `CORS_ORIGINS` | Allowed CORS origins | `http://localhost:3000` |
| `RATE_LIMIT_PER_MINUTE` | Rate limit per minute | `60` |
| `ADMIN_IP_WHITELIST` | Admin IP whitelist | Optional |

### Database Connections

The system connects to 4 MySQL databases:

1. **CDB**: Customer and address data
2. **App**: Application data, quotes, leads
3. **Reporting**: Sales and reporting data
4. **App Secondary**: Secondary application data

### Redis Caching Strategy

- **Sales Data**: 30 minutes TTL
- **Heatmap Data**: 1 hour TTL
- **Postcode Data**: 24 hours TTL
- **Export Jobs**: 7 days retention
- **User Sessions**: 24 hours TTL

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   FastAPI       │    │   Databases     │
│   (React)       │◄──►│   Backend       │◄──►│   (MySQL x4)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Redis Cache   │
                       │   & Job Queue   │
                       └─────────────────┘
```

### Key Components

- **FastAPI Application**: Main web framework with automatic OpenAPI docs
- **Authentication System**: JWT-based with bcrypt password hashing
- **Data Services**: Integration with existing Python exporters
- **Export Service**: Background job processing with file generation
- **Redis Service**: Caching and session management
- **Security Middleware**: Rate limiting, CORS, security headers
- **Database Manager**: Connection pooling and management

## Development

### Project Structure

```
fastapi_backend/
├── app/
│   ├── api/routes/          # API route handlers
│   ├── auth/               # Authentication system
│   ├── config/             # Configuration and settings
│   ├── middleware/         # Custom middleware
│   ├── models/             # Pydantic models
│   ├── services/           # Business logic services
│   └── utils/              # Utility functions
├── data/                   # Data storage
│   ├── exports/            # Export files
│   ├── logs/               # Application logs
│   └── cache/              # File cache
├── tests/                  # Test files
├── requirements.txt        # Python dependencies
├── Dockerfile             # Docker configuration
└── docker-compose.yml     # Docker Compose setup
```

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=app --cov-report=html
```

### Adding New Endpoints

1. Create Pydantic models in `app/models/`
2. Add business logic to `app/services/`
3. Create route handlers in `app/api/routes/`
4. Include router in `app/main.py`

## Deployment

### Production Checklist

- [ ] Set strong `SECRET_KEY` and `JWT_SECRET_KEY`
- [ ] Configure database credentials
- [ ] Set up Redis with password
- [ ] Configure CORS origins for your domain
- [ ] Set up SSL certificates (nginx configuration included)
- [ ] Configure IP whitelist for admin endpoints
- [ ] Set up log rotation and monitoring
- [ ] Configure backup strategy for export files
- [ ] Test all database connections
- [ ] Verify Redis connectivity and caching

### Docker Production

```bash
# Production deployment
docker-compose -f docker-compose.yml --profile production up -d

# View logs
docker-compose logs -f fastapi-backend

# Scale services
docker-compose up -d --scale fastapi-backend=3
```

## Monitoring

### Health Checks

- **Application**: `GET /health`
- **Database**: Connection tests in health endpoint
- **Redis**: Connection and performance tests
- **Docker**: Built-in health checks

### Logging

- **Application logs**: `data/logs/app.log`
- **Error logs**: `data/logs/error.log`
- **Access logs**: `data/logs/access.log`

### Metrics

- Request/response times in headers
- Rate limiting headers
- Export job progress tracking
- Cache hit/miss ratios

## Support

For issues and questions:

1. Check the API documentation at `/docs`
2. Review application logs in `data/logs/`
3. Verify database and Redis connectivity
4. Check export job status via API

## License

[Your License Here]
