# Test Configuration and Fixtures
import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch
import tempfile
import os
from pathlib import Path

# Import the FastAPI app
from app.main import app
from app.config.settings import settings


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
def mock_redis():
    """Mock Redis client for testing."""
    with patch('app.services.redis_service.redis_service') as mock:
        mock.redis_client = Mock()
        mock.health_check.return_value = {"status": "healthy"}
        mock.get.return_value = None
        mock.set.return_value = True
        mock.delete.return_value = True
        yield mock


@pytest.fixture
def mock_database():
    """Mock database connections for testing."""
    with patch('app.config.database.db_manager') as mock:
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_cursor.fetchall.return_value = []
        mock_cursor.fetchone.return_value = None
        mock_conn.cursor.return_value = mock_cursor
        mock.get_connection.return_value.__enter__.return_value = mock_conn
        yield mock


@pytest.fixture
def temp_directory():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def auth_headers(client):
    """Get authentication headers for testing."""
    # Mock successful login
    with patch('app.auth.services.user_manager.authenticate_user') as mock_auth:
        mock_auth.return_value = {
            "username": "testuser",
            "role": "user",
            "email": "<EMAIL>"
        }
        
        response = client.post("/auth/login", json={
            "username": "testuser",
            "password": "testpass"
        })
        
        if response.status_code == 200:
            token = response.json()["access_token"]
            return {"Authorization": f"Bearer {token}"}
        
        # Fallback - create a mock token
        from app.utils.security import create_jwt_token
        token = create_jwt_token({"sub": "testuser", "role": "user"})
        return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def admin_headers(client):
    """Get admin authentication headers for testing."""
    with patch('app.auth.services.user_manager.authenticate_user') as mock_auth:
        mock_auth.return_value = {
            "username": "admin",
            "role": "admin",
            "email": "<EMAIL>"
        }
        
        response = client.post("/auth/login", json={
            "username": "admin",
            "password": "admin123"
        })
        
        if response.status_code == 200:
            token = response.json()["access_token"]
            return {"Authorization": f"Bearer {token}"}
        
        # Fallback - create a mock admin token
        from app.utils.security import create_jwt_token
        token = create_jwt_token({"sub": "admin", "role": "admin"})
        return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def sample_export_request():
    """Sample export request data for testing."""
    return {
        "export_type": "sales",
        "export_format": "csv",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "filters": {
            "category": "FDB Sheds"
        },
        "options": {
            "include_summary": True,
            "compress": False
        }
    }


@pytest.fixture
def sample_sales_data():
    """Sample sales data for testing."""
    return [
        {
            "id": 1,
            "date": "2024-01-15",
            "amount": 15000.00,
            "category": "FDB Sheds",
            "customer": "Test Customer 1",
            "postcode": "2000"
        },
        {
            "id": 2,
            "date": "2024-02-20",
            "amount": 25000.00,
            "category": "SB Sheds",
            "customer": "Test Customer 2",
            "postcode": "3000"
        }
    ]


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment variables."""
    test_env = {
        "DEBUG": "true",
        "SECRET_KEY": "test-secret-key",
        "JWT_SECRET_KEY": "test-jwt-secret",
        "REDIS_HOST": "localhost",
        "REDIS_PORT": "6379",
        "CDB_HOST": "localhost",
        "APP_HOST": "localhost",
        "REPORTING_HOST": "localhost",
        "APP_SECONDARY_HOST": "localhost",
    }
    
    # Set test environment variables
    original_env = {}
    for key, value in test_env.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    yield
    
    # Restore original environment
    for key, value in original_env.items():
        if value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = value


# Test data fixtures
@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "role": "user",
        "hashed_password": "$2b$12$test.hash.here"
    }


@pytest.fixture
def sample_heatmap_data():
    """Sample heatmap data for testing."""
    return [
        {"postcode": "2000", "value": 150000, "count": 5},
        {"postcode": "3000", "value": 200000, "count": 8},
        {"postcode": "4000", "value": 100000, "count": 3}
    ]


@pytest.fixture
def sample_distributor_data():
    """Sample distributor location data for testing."""
    return [
        {
            "id": 1,
            "name": "Sydney Distributor",
            "address": "123 Test St, Sydney NSW 2000",
            "latitude": -33.8688,
            "longitude": 151.2093,
            "postcode": "2000"
        },
        {
            "id": 2,
            "name": "Melbourne Distributor", 
            "address": "456 Test Ave, Melbourne VIC 3000",
            "latitude": -37.8136,
            "longitude": 144.9631,
            "postcode": "3000"
        }
    ]
