# Test Main Application
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, Mo<PERSON>


def test_root_endpoint(client):
    """Test the root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert data["message"] == "Stramit Data Export API"


def test_health_check_healthy(client, mock_redis, mock_database):
    """Test health check when all services are healthy."""
    mock_redis.health_check.return_value = {"status": "healthy"}
    
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] in ["healthy", "degraded"]
    assert "timestamp" in data
    assert "version" in data
    assert "services" in data


def test_health_check_unhealthy(client):
    """Test health check when services are unhealthy."""
    with patch('app.services.redis_service.redis_service') as mock_redis:
        mock_redis.health_check.side_effect = Exception("Redis connection failed")
        
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "unhealthy"


def test_api_info(client):
    """Test API info endpoint."""
    response = client.get("/api/info")
    assert response.status_code == 200
    data = response.json()
    assert "api_name" in data
    assert "version" in data
    assert "endpoints" in data
    assert "features" in data


def test_cors_headers(client):
    """Test CORS headers are present."""
    response = client.options("/", headers={"Origin": "http://localhost:3000"})
    assert "access-control-allow-origin" in response.headers


def test_security_headers(client):
    """Test security headers are present."""
    response = client.get("/")
    assert "x-content-type-options" in response.headers
    assert "x-frame-options" in response.headers
    assert response.headers["x-content-type-options"] == "nosniff"
    assert response.headers["x-frame-options"] == "DENY"


def test_404_error_handling(client):
    """Test 404 error handling."""
    response = client.get("/nonexistent-endpoint")
    assert response.status_code == 404


def test_method_not_allowed(client):
    """Test method not allowed error handling."""
    response = client.post("/health")
    assert response.status_code == 405


@pytest.mark.asyncio
async def test_application_startup():
    """Test application startup process."""
    from app.main import lifespan
    from fastapi import FastAPI
    
    app = FastAPI()
    
    with patch('app.config.database.db_manager') as mock_db:
        with patch('app.services.redis_service.redis_service') as mock_redis:
            mock_redis.health_check.return_value = {"status": "healthy"}
            mock_conn = Mock()
            mock_cursor = Mock()
            mock_cursor.execute.return_value = None
            mock_cursor.fetchone.return_value = (1,)
            mock_conn.cursor.return_value = mock_cursor
            mock_db.get_connection.return_value.__enter__.return_value = mock_conn
            
            # Test startup
            async with lifespan(app):
                pass  # Startup and shutdown should complete without errors


def test_request_logging_middleware(client):
    """Test request logging middleware."""
    with patch('app.middleware.security.logging.getLogger') as mock_logger:
        mock_log = Mock()
        mock_logger.return_value = mock_log
        
        response = client.get("/")
        assert response.status_code == 200
        
        # Check that processing time header is added
        assert "x-process-time" in response.headers


def test_rate_limiting_headers(client):
    """Test rate limiting headers are added."""
    with patch('app.services.redis_service.redis_service') as mock_redis:
        mock_redis.redis_client.get.return_value = 1
        mock_redis.redis_client.pipeline.return_value.execute.return_value = [1, True]
        
        response = client.get("/")
        
        # Rate limiting headers should be present
        # Note: This test might need adjustment based on actual middleware implementation
