# Test Authentication System
import pytest
from unittest.mock import patch, <PERSON><PERSON>
from fastapi import HTTPEx<PERSON>


def test_login_success(client):
    """Test successful login."""
    with patch('app.auth.services.user_manager.authenticate_user') as mock_auth:
        mock_auth.return_value = {
            "username": "testuser",
            "role": "user",
            "email": "<EMAIL>"
        }
        
        response = client.post("/auth/login", json={
            "username": "testuser",
            "password": "testpass"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"


def test_login_invalid_credentials(client):
    """Test login with invalid credentials."""
    with patch('app.auth.services.user_manager.authenticate_user') as mock_auth:
        mock_auth.return_value = None
        
        response = client.post("/auth/login", json={
            "username": "invalid",
            "password": "invalid"
        })
        
        assert response.status_code == 401
        data = response.json()
        assert "error" in data


def test_login_missing_fields(client):
    """Test login with missing fields."""
    response = client.post("/auth/login", json={
        "username": "testuser"
        # Missing password
    })
    
    assert response.status_code == 422


def test_protected_endpoint_without_token(client):
    """Test accessing protected endpoint without token."""
    response = client.get("/api/sales-data")
    assert response.status_code == 401


def test_protected_endpoint_with_invalid_token(client):
    """Test accessing protected endpoint with invalid token."""
    headers = {"Authorization": "Bearer invalid-token"}
    response = client.get("/api/sales-data", headers=headers)
    assert response.status_code == 401


def test_protected_endpoint_with_valid_token(client, auth_headers, mock_database):
    """Test accessing protected endpoint with valid token."""
    response = client.get("/api/sales-data", headers=auth_headers)
    # Should not be 401 (unauthorized)
    assert response.status_code != 401


def test_admin_endpoint_with_user_token(client, auth_headers):
    """Test accessing admin endpoint with user token."""
    response = client.delete("/api/export/cleanup", headers=auth_headers)
    assert response.status_code == 403


def test_admin_endpoint_with_admin_token(client, admin_headers):
    """Test accessing admin endpoint with admin token."""
    with patch('app.services.export_service.export_service.cleanup_old_files') as mock_cleanup:
        mock_cleanup.return_value = 5
        
        response = client.delete("/api/export/cleanup", headers=admin_headers)
        assert response.status_code == 200


def test_token_validation():
    """Test JWT token validation."""
    from app.utils.security import create_jwt_token, decode_jwt_token
    
    # Create a token
    payload = {"sub": "testuser", "role": "user"}
    token = create_jwt_token(payload)
    
    # Decode the token
    decoded = decode_jwt_token(token)
    assert decoded["sub"] == "testuser"
    assert decoded["role"] == "user"


def test_password_hashing():
    """Test password hashing and verification."""
    from app.utils.security import hash_password, verify_password
    
    password = "testpassword123"
    hashed = hash_password(password)
    
    # Verify correct password
    assert verify_password(password, hashed) is True
    
    # Verify incorrect password
    assert verify_password("wrongpassword", hashed) is False


def test_user_manager_load_users():
    """Test user manager loading users."""
    from app.auth.services import user_manager
    
    with patch('builtins.open') as mock_open:
        mock_open.return_value.__enter__.return_value.read.return_value = '''
        {
            "users": [
                {
                    "username": "testuser",
                    "email": "<EMAIL>",
                    "role": "user",
                    "hashed_password": "$2b$12$test.hash"
                }
            ]
        }
        '''
        
        users = user_manager._load_users()
        assert len(users) == 1
        assert users[0]["username"] == "testuser"


def test_user_manager_authenticate():
    """Test user authentication."""
    from app.auth.services import user_manager
    
    with patch.object(user_manager, '_load_users') as mock_load:
        mock_load.return_value = [
            {
                "username": "testuser",
                "email": "<EMAIL>", 
                "role": "user",
                "hashed_password": "$2b$12$LQv3c1yqBWVHxkd0LQ1lqu.hrm.9s.b2RVaWvXg2wtrpxg/O8WJjO"  # "testpass"
            }
        ]
        
        # Test successful authentication
        user = user_manager.authenticate_user("testuser", "testpass")
        assert user is not None
        assert user["username"] == "testuser"
        
        # Test failed authentication
        user = user_manager.authenticate_user("testuser", "wrongpass")
        assert user is None


def test_get_current_user_dependency():
    """Test get_current_user dependency."""
    from app.auth.dependencies import get_current_active_user
    from app.utils.security import create_jwt_token
    from fastapi import Request
    
    # Create a mock request with valid token
    token = create_jwt_token({"sub": "testuser", "role": "user"})
    
    with patch('app.auth.services.user_manager.get_user_by_username') as mock_get_user:
        mock_get_user.return_value = {
            "username": "testuser",
            "email": "<EMAIL>",
            "role": "user"
        }
        
        # This would normally be called by FastAPI's dependency injection
        # In a real test, you'd use the TestClient with proper headers
