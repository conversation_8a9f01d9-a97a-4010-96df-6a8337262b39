// Australian Map Component - Direct React equivalent of your R Shiny maps
import React, { useState, useEffect, useMemo } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, useMap } from 'react-leaflet'
import { HeatmapLayer } from 'react-leaflet-heatmap-layer'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

interface SalesData {
  dist_id: string
  BusinessName: string
  Lo: number  // Longitude
  La: number  // Latitude
  sum_amount: number
  total_freq: number
}

interface MapProps {
  salesData: SalesData[]
  mapType: 'sales' | 'frequency'
  selectedArea?: string
  selectedDistributor?: string
}

const AustralianSalesMap: React.FC<MapProps> = ({ 
  salesData, 
  mapType, 
  selectedArea, 
  selectedDistributor 
}) => {
  // Filter data based on selections (replicating R filter logic)
  const filteredData = useMemo(() => {
    let filtered = [...salesData]
    
    if (selectedArea && selectedArea !== 'All') {
      filtered = filtered.filter(item => item.area === selectedArea)
    }
    
    if (selectedDistributor && selectedDistributor !== 'All') {
      filtered = filtered.filter(item => item.dist_id === selectedDistributor)
    }
    
    return filtered
  }, [salesData, selectedArea, selectedDistributor])

  // Calculate heat intensity (exact R logic replication)
  const heatmapData = useMemo(() => {
    if (filteredData.length === 0) return []
    
    const values = filteredData.map(item => 
      mapType === 'sales' ? item.sum_amount : item.total_freq
    )
    
    const minVal = Math.min(...values)
    const maxVal = Math.max(...values) + (mapType === 'sales' ? 1000 : 10) // R offset logic
    
    return filteredData.map(item => {
      const value = mapType === 'sales' ? item.sum_amount : item.total_freq
      const intensity = (value - minVal) / (maxVal - minVal)
      
      return [
        item.La,  // Latitude
        item.Lo,  // Longitude
        intensity // Heat intensity (0-1)
      ]
    })
  }, [filteredData, mapType])

  // Custom markers with tooltips (replicating R addCircleMarkers)
  const MarkerLayer = () => {
    const map = useMap()
    
    useEffect(() => {
      // Clear existing markers
      map.eachLayer(layer => {
        if (layer instanceof L.CircleMarker) {
          map.removeLayer(layer)
        }
      })
      
      // Add new markers
      filteredData.forEach(item => {
        const value = mapType === 'sales' ? item.sum_amount : item.total_freq
        const label = mapType === 'sales' 
          ? `${item.BusinessName} $$$: ${value.toLocaleString()}`
          : `${item.BusinessName} Count: ${value.toLocaleString()}`
        
        L.circleMarker([item.La, item.Lo], {
          fillOpacity: 0,
          weight: 0,
          radius: 0
        })
        .bindTooltip(label, {
          permanent: false,
          direction: 'top'
        })
        .addTo(map)
      })
    }, [map, filteredData, mapType])
    
    return null
  }

  return (
    <MapContainer
      center={[-33.8688, 151.2093]} // Sydney center (same as R)
      zoom={3.5}
      style={{ height: '600px', width: '100%' }}
    >
      <TileLayer
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>'
      />
      
      {/* Heatmap Layer - Direct equivalent of R addHeatmap */}
      <HeatmapLayer
        points={heatmapData}
        longitudeExtractor={point => point[1]}
        latitudeExtractor={point => point[0]}
        intensityExtractor={point => point[2]}
        options={{
          blur: 5,        // Same as R blur=5
          minOpacity: 0.05, // Same as R minOpacity
          max: 0.95,      // Same as R max
          radius: 15      // Same as R radius
        }}
      />
      
      {/* Invisible markers for tooltips */}
      <MarkerLayer />
    </MapContainer>
  )
}

// Customer Map Component (replicating CustomerMap from R)
const CustomerMap: React.FC<{
  customerData: any[]
  selectedState?: string
  selectedPostcode?: string
}> = ({ customerData, selectedState, selectedPostcode }) => {
  
  const processedData = useMemo(() => {
    let filtered = customerData
    
    if (selectedState && selectedState !== 'All') {
      filtered = filtered.filter(item => item.state === selectedState)
    }
    
    if (selectedPostcode && selectedPostcode !== 'All') {
      filtered = filtered.filter(item => item.PostCode === selectedPostcode)
    }
    
    // Group by Town and PostCode (replicating R group_by logic)
    const grouped = filtered.reduce((acc, item) => {
      const key = `${item.Town}-${item.PostCode}`
      if (!acc[key]) {
        acc[key] = {
          Town: item.Town,
          PostCode: item.PostCode,
          Long_precise: item.Long_precise,
          Lat_precise: item.Lat_precise,
          TotalSales: 0,
          count: 0
        }
      }
      acc[key].TotalSales += item.TotalPrice || 0
      acc[key].count += 1
      return acc
    }, {})
    
    return Object.values(grouped)
  }, [customerData, selectedState, selectedPostcode])

  const heatmapData = useMemo(() => {
    if (processedData.length === 0) return []
    
    const values = processedData.map(item => item.TotalSales)
    const minVal = Math.min(...values)
    const maxVal = Math.max(...values)
    const rangeVal = maxVal - minVal
    
    return processedData.map(item => [
      item.Lat_precise,
      item.Long_precise,
      Math.max(0.1 * rangeVal, (item.TotalSales - minVal) / rangeVal)
    ])
  }, [processedData])

  return (
    <MapContainer
      center={[-33.8688, 151.2093]}
      zoom={3.5}
      style={{ height: '600px', width: '100%' }}
    >
      <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
      <HeatmapLayer
        points={heatmapData}
        longitudeExtractor={point => point[1]}
        latitudeExtractor={point => point[0]}
        intensityExtractor={point => point[2]}
        options={{ blur: 5, minOpacity: 0.05, max: 0.95, radius: 15 }}
      />
    </MapContainer>
  )
}

// Main Dashboard Component
const MapDashboard: React.FC = () => {
  const [mapType, setMapType] = useState<'sales' | 'frequency'>('sales')
  const [selectedArea, setSelectedArea] = useState('All')
  const [selectedDistributor, setSelectedDistributor] = useState('All')
  const [salesData, setSalesData] = useState<SalesData[]>([])

  // Fetch data from your backend API
  useEffect(() => {
    fetch('/api/sales-data')
      .then(res => res.json())
      .then(data => setSalesData(data))
  }, [])

  return (
    <div className="map-dashboard">
      {/* Controls - replicating R selectInput */}
      <div className="controls">
        <select 
          value={selectedArea} 
          onChange={(e) => setSelectedArea(e.target.value)}
        >
          <option value="All">All</option>
          <option value="New South Wales">New South Wales</option>
          <option value="Victoria">Victoria</option>
          <option value="Queensland">Queensland</option>
          {/* Add other states */}
        </select>
        
        <select 
          value={selectedDistributor} 
          onChange={(e) => setSelectedDistributor(e.target.value)}
        >
          <option value="All">All</option>
          {/* Populate from API */}
        </select>
      </div>

      {/* Tab Panel - replicating R tabsetPanel */}
      <div className="tab-panel">
        <button 
          className={mapType === 'sales' ? 'active' : ''}
          onClick={() => setMapType('sales')}
        >
          Sales
        </button>
        <button 
          className={mapType === 'frequency' ? 'active' : ''}
          onClick={() => setMapType('frequency')}
        >
          Frequency
        </button>
      </div>

      {/* Map Component */}
      <AustralianSalesMap
        salesData={salesData}
        mapType={mapType}
        selectedArea={selectedArea}
        selectedDistributor={selectedDistributor}
      />
    </div>
  )
}

export default MapDashboard
