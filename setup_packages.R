# Package Installation Script for Windows
# This script installs all required packages without changing working directory

cat("Starting package installation...\n")
cat("Current working directory:", getwd(), "\n")

# List of required packages
required_packages <- c(
  "shiny", "DT", "RMySQL", "lubridate", "RColorBrewer", "zoo",
  "leaflet", "leaflet.extras", "stringr", "tidyr", "dplyr",
  "bs4Dash", "bslib", "openxlsx", "shinydashboard", "flexdashboard",
  "plotly", "ggplot2", "ggiraph", "scales", "shinyjs", "glue", "htmltools"
)

# Function to install missing packages
install_missing_packages <- function(packages) {
  installed_packages <- rownames(installed.packages())
  missing_packages <- setdiff(packages, installed_packages)
  
  if (length(missing_packages) > 0) {
    cat("Installing", length(missing_packages), "missing packages:\n")
    cat(paste(missing_packages, collapse = ", "), "\n\n")
    
    # Install packages one by one to handle errors better
    for (pkg in missing_packages) {
      cat("Installing", pkg, "...\n")
      tryCatch({
        install.packages(pkg, dependencies = TRUE, repos = "https://cran.rstudio.com/")
        cat("✓", pkg, "installed successfully\n")
      }, error = function(e) {
        cat("✗ Failed to install", pkg, ":", e$message, "\n")
      })
    }
    
    cat("\nInstallation complete!\n")
  } else {
    cat("✓ All required packages are already installed!\n")
  }
}

# Install packages
install_missing_packages(required_packages)

# Test loading key packages
cat("\nTesting package loading...\n")
test_packages <- c("shiny", "dplyr", "ggplot2")
for (pkg in test_packages) {
  tryCatch({
    library(pkg, character.only = TRUE)
    cat("✓", pkg, "loaded successfully\n")
  }, error = function(e) {
    cat("✗ Failed to load", pkg, ":", e$message, "\n")
  })
}

cat("\nSetup complete! You can now run your Shiny app.\n")
