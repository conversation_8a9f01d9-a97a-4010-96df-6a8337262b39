library(shiny)
library(DT)
library(RMySQL)
library(lubridate)
library(RColorBrewer)
library(zoo)
library(leaflet)
library(leaflet.extras)
library(stringr)
library(tidyr)
library(dplyr)
library(bs4Dash)
library(bslib)
library(openxlsx)
library(shinydashboard)
library(flexdashboard)
library(plotly)
library(ggplot2)
library(ggiraph)
library(scales)
#library(profvis)
library(shinyjs)
library(glue)
library(htmltools)

source('global.R')
source("Frontend.R")
source("Backend.R")
source("helpers.R")
source("./login/tab.R")

app <- shinyApp(ui=ui,server=backend)

runApp(app)
# profvis(runApp(app), prof_output = ".")
