# Python gRPC Server Implementation for Stramit Data Export
import grpc
from concurrent import futures
import asyncio
from datetime import datetime
import pandas as pd
import logging
from typing import Iterator

# Generated from proto file
import stramit_pb2
import stramit_pb2_grpc

# Your existing Python modules
from python_data_exporter.exporters.sales_exporter import SalesExporter
from python_data_exporter.exporters.customer_exporter import CustomerExporter
from python_data_exporter.exporters.leads_exporter import LeadsExporter
from python_data_exporter.config.database import db_manager

logger = logging.getLogger(__name__)

class DataExportServicer(stramit_pb2_grpc.DataExportServiceServicer):
    """gRPC service implementation for data export operations."""
    
    def __init__(self):
        self.sales_exporter = SalesExporter()
        self.customer_exporter = CustomerExporter()
        self.leads_exporter = LeadsExporter()
        self.active_jobs = {}  # Track background jobs
    
    def GetSalesData(self, request, context) -> Iterator[stramit_pb2.SalesDataResponse]:
        """Stream sales data with filtering."""
        try:
            # Convert protobuf timestamp to datetime
            start_date = datetime.fromtimestamp(request.start_date.seconds)
            end_date = datetime.fromtimestamp(request.end_date.seconds)
            
            logger.info(f"Streaming sales data from {start_date} to {end_date}")
            
            # Use your existing sales exporter
            df = self.sales_exporter.extract_sales_data(start_date, end_date)
            
            if df is None or df.empty:
                logger.warning("No sales data found")
                return
            
            # Apply filters from request
            if request.categories:
                df = df[df['Category'].isin(request.categories)]
            
            if request.distributor_id and request.distributor_id != 'All':
                df = df[df['dist_id'] == request.distributor_id]
            
            if request.area and request.area != 'All':
                df = df[df['area'] == request.area]
            
            # Stream data in chunks to avoid memory issues
            chunk_size = 1000
            for i in range(0, len(df), chunk_size):
                chunk = df.iloc[i:i+chunk_size]
                
                for _, row in chunk.iterrows():
                    # Convert pandas row to protobuf message
                    response = stramit_pb2.SalesDataResponse(
                        order_id=str(row.get('OrderIdCode', '')),
                        dist_id=str(row.get('dist_id', '')),
                        amount=float(row.get('Ammount', 0)),
                        category=str(row.get('Category', '')),
                        network_id=str(row.get('NetworkID', '')),
                        business_name=str(row.get('BusinessName', ''))
                    )
                    
                    # Set timestamp
                    if pd.notna(row.get('StatusDate')):
                        timestamp = int(pd.to_datetime(row['StatusDate']).timestamp())
                        response.status_date.seconds = timestamp
                    
                    yield response
                    
        except Exception as e:
            logger.error(f"Error streaming sales data: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to stream sales data: {str(e)}")
    
    def GetCustomerData(self, request, context) -> Iterator[stramit_pb2.CustomerDataResponse]:
        """Stream customer data with filtering."""
        try:
            start_date = datetime.fromtimestamp(request.start_date.seconds)
            end_date = datetime.fromtimestamp(request.end_date.seconds)
            
            df = self.customer_exporter.extract_customer_data(start_date, end_date)
            
            if df is None or df.empty:
                return
            
            # Apply filters
            if request.state and request.state != 'All':
                df = df[df['state'] == request.state]
            
            if request.postcode and request.postcode != 'All':
                df = df[df['PostCode'] == request.postcode]
            
            # Stream customer data
            for _, row in df.iterrows():
                response = stramit_pb2.CustomerDataResponse(
                    customer_id=str(row.get('CustomerID', '')),
                    town=str(row.get('Town', '')),
                    postcode=str(row.get('PostCode', '')),
                    state=str(row.get('state', '')),
                    total_price=float(row.get('TotalPrice', 0)),
                    lat_precise=float(row.get('Lat_precise', 0)),
                    long_precise=float(row.get('Long_precise', 0))
                )
                
                if pd.notna(row.get('OrderSentToSupplier')):
                    timestamp = int(pd.to_datetime(row['OrderSentToSupplier']).timestamp())
                    response.order_date.seconds = timestamp
                
                yield response
                
        except Exception as e:
            logger.error(f"Error streaming customer data: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to stream customer data: {str(e)}")
    
    def StartExport(self, request, context) -> stramit_pb2.ExportJobResponse:
        """Start a background export job."""
        import uuid
        import threading
        
        job_id = str(uuid.uuid4())
        
        # Create job record
        job_info = {
            'id': job_id,
            'status': 'pending',
            'progress': 0,
            'created_at': datetime.now(),
            'export_types': list(request.export_types),
            'start_date': datetime.fromtimestamp(request.start_date.seconds),
            'end_date': datetime.fromtimestamp(request.end_date.seconds),
            'output_format': request.output_format
        }
        
        self.active_jobs[job_id] = job_info
        
        # Start background thread
        thread = threading.Thread(target=self._run_export_job, args=(job_id,))
        thread.daemon = True
        thread.start()
        
        return stramit_pb2.ExportJobResponse(
            job_id=job_id,
            status='pending',
            created_at=stramit_pb2.google_dot_protobuf_dot_timestamp__pb2.Timestamp(
                seconds=int(job_info['created_at'].timestamp())
            )
        )
    
    def GetExportStatus(self, request, context) -> stramit_pb2.ExportStatusResponse:
        """Get the status of an export job."""
        job_id = request.job_id
        
        if job_id not in self.active_jobs:
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(f"Job {job_id} not found")
            return stramit_pb2.ExportStatusResponse()
        
        job = self.active_jobs[job_id]
        
        response = stramit_pb2.ExportStatusResponse(
            job_id=job_id,
            status=job['status'],
            progress_percent=job['progress'],
            message=job.get('message', ''),
            file_urls=job.get('file_urls', [])
        )
        
        if job.get('completed_at'):
            response.completed_at.seconds = int(job['completed_at'].timestamp())
        
        return response
    
    def _run_export_job(self, job_id: str):
        """Run export job in background thread."""
        try:
            job = self.active_jobs[job_id]
            job['status'] = 'running'
            job['progress'] = 10
            
            export_types = job['export_types']
            start_date = job['start_date']
            end_date = job['end_date']
            
            file_urls = []
            total_types = len(export_types)
            
            for i, export_type in enumerate(export_types):
                job['message'] = f'Exporting {export_type} data...'
                
                if export_type == 'sales':
                    file_path = self.sales_exporter.export_sales_data(start_date, end_date)
                elif export_type == 'customer':
                    file_path = self.customer_exporter.export_customer_data(start_date, end_date)
                elif export_type == 'leads':
                    file_path = self.leads_exporter.export_leads_data(start_date, end_date)
                
                if file_path:
                    file_urls.append(str(file_path))
                
                # Update progress
                job['progress'] = int(((i + 1) / total_types) * 90) + 10
            
            job['status'] = 'completed'
            job['progress'] = 100
            job['message'] = 'Export completed successfully'
            job['file_urls'] = file_urls
            job['completed_at'] = datetime.now()
            
        except Exception as e:
            logger.error(f"Export job {job_id} failed: {e}")
            job['status'] = 'failed'
            job['message'] = f'Export failed: {str(e)}'

class MapDataServicer(stramit_pb2_grpc.MapDataServiceServicer):
    """gRPC service for map visualization data."""
    
    def GetHeatmapData(self, request, context) -> stramit_pb2.HeatmapResponse:
        """Get heatmap data for Australian map visualization."""
        try:
            start_date = datetime.fromtimestamp(request.start_date.seconds)
            end_date = datetime.fromtimestamp(request.end_date.seconds)
            
            # Use your existing exporters to get data
            if request.data_type == 'sales':
                exporter = SalesExporter()
                df = exporter.extract_sales_data(start_date, end_date)
                
                # Process for heatmap (replicating R logic)
                if df is not None and not df.empty:
                    # Group by distributor and calculate intensity
                    grouped = df.groupby('dist_id').agg({
                        'Ammount': 'sum',
                        'BusinessName': 'first'
                    }).reset_index()
                    
                    # Calculate heat intensity
                    min_val = grouped['Ammount'].min()
                    max_val = grouped['Ammount'].max() + 1000
                    grouped['heat_intensity'] = (grouped['Ammount'] - min_val) / (max_val - min_val)
                    
                    # Create heatmap points
                    points = []
                    for _, row in grouped.iterrows():
                        point = stramit_pb2.HeatmapPoint(
                            latitude=float(row.get('La', 0)),
                            longitude=float(row.get('Lo', 0)),
                            intensity=float(row['heat_intensity']),
                            label=f"{row['BusinessName']} $$$: {row['Ammount']:,.0f}",
                            value=float(row['Ammount'])
                        )
                        points.append(point)
                    
                    return stramit_pb2.HeatmapResponse(points=points)
            
            return stramit_pb2.HeatmapResponse(points=[])
            
        except Exception as e:
            logger.error(f"Error getting heatmap data: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to get heatmap data: {str(e)}")
            return stramit_pb2.HeatmapResponse(points=[])

def serve():
    """Start the gRPC server."""
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    
    # Add servicers
    stramit_pb2_grpc.add_DataExportServiceServicer_to_server(DataExportServicer(), server)
    stramit_pb2_grpc.add_MapDataServiceServicer_to_server(MapDataServicer(), server)
    
    # Configure server
    listen_addr = '[::]:50051'
    server.add_insecure_port(listen_addr)
    
    logger.info(f"Starting gRPC server on {listen_addr}")
    server.start()
    
    try:
        server.wait_for_termination()
    except KeyboardInterrupt:
        logger.info("Shutting down gRPC server")
        server.stop(0)

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    serve()
