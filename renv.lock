{"R": {"Version": "4.5.1", "Repositories": [{"Name": "CRAN", "URL": "https://cloud.r-project.org"}]}, "Packages": {"DBI": {"Package": "DBI", "Version": "1.2.3", "Source": "Repository", "Title": "R Database Interface", "Date": "2024-06-02", "Authors@R": "c( person(\"R Special Interest Group on Databases (R-SIG-DB)\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-1416-3412\")), person(\"R Consortium\", role = \"fnd\") )", "Description": "A database interface definition for communication between R and relational database management systems.  All classes in this package are virtual and need to be extended by the various R/DBMS implementations.", "License": "LGPL (>= 2.1)", "URL": "https://dbi.r-dbi.org, https://github.com/r-dbi/DBI", "BugReports": "https://github.com/r-dbi/DBI/issues", "Depends": ["methods", "R (>= 3.0.0)"], "Suggests": ["arrow", "blob", "covr", "DBItest", "dbplyr", "downlit", "dplyr", "glue", "hms", "knitr", "magrit<PERSON>", "nanoarrow (>= *******)", "RMariaDB", "rmarkdown", "rprojroot", "RSQLite (>= 1.1-2)", "testthat (>= 3.0.0)", "vctrs", "xml2"], "VignetteBuilder": "knitr", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "false", "Config/Needs/check": "r-dbi/DBItest", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Config/Needs/website": "r-dbi/DBItest, r-dbi/dbitemplate, adbi, AzureKusto, bigrquery, DatabaseConnector, dittodb, duckdb, implyr, lazysf, odbc, pool, RAthena, IMSMWU/RClickhouse, RH2, RJDBC, RMariaDB, RMySQL, RPostgres, RPostgreSQL, RPresto, RSQLite, sergeant, sparklyr, withr", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "R Special Interest Group on Databases (R-SIG-DB) [aut], <PERSON> [aut], <PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-1416-3412>), R Consortium [fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "DT": {"Package": "DT", "Version": "0.33", "Source": "Repository", "Type": "Package", "Title": "A Wrapper of the JavaScript Library 'DataTables'", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"SpryMedia Limited\", role = c(\"ctb\", \"cph\"), comment = \"DataTables in htmlwidgets/lib\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"selectize.js in htmlwidgets/lib\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"noUiSlider in htmlwidgets/lib\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"jquery.highlight.js in htmlwidgets/lib\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"ctb\")), person(\"<PERSON>\", \"<PERSON>\", role = c(\"ctb\")), person(\"<PERSON>k<PERSON>\", \"<PERSON>tila\", role = c(\"ctb\")), person(\"<PERSON>s\", \"Quintero\", role = c(\"ctb\")), person(\"<PERSON><PERSON>phane\", \"<PERSON>\", role = c(\"ctb\")), person(given = \"Posit <PERSON>, P<PERSON>\", role = c(\"cph\", \"fnd\")) )", "Description": "Data objects in R can be rendered as HTML tables using the JavaScript library 'DataTables' (typically via R Markdown or Shiny). The 'DataTables' library has been included in this R package. The package name 'DT' is an abbreviation of 'DataTables'.", "URL": "https://github.com/rstudio/DT", "BugReports": "https://github.com/rstudio/DT/issues", "License": "GPL-3 | file LICENSE", "Imports": ["htmltools (>= 0.3.6)", "htmlwidgets (>= 1.3)", "httpuv", "jsonlite (>= 0.9.16)", "magrit<PERSON>", "crosstalk", "j<PERSON><PERSON><PERSON>", "promises"], "Suggests": ["knitr (>= 1.8)", "rmarkdown", "shiny (>= 1.6)", "bslib", "future", "testit", "tibble"], "VignetteBuilder": "knitr", "RoxygenNote": "7.3.1", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON><PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], SpryMedia Limited [ctb, cph] (DataTables in htmlwidgets/lib), <PERSON> [ctb, cph] (selectize.js in htmlwidgets/lib), <PERSON> [ctb, cph] (noUiSlider in htmlwidgets/lib), <PERSON><PERSON> [ctb, cph] (jquery.highlight.js in htmlwidgets/lib), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON><PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "MASS": {"Package": "MASS", "Version": "7.3-65", "Source": "Repository", "Priority": "recommended", "Date": "2025-02-19", "Revision": "$Rev: 3681 $", "Depends": ["R (>= 4.4.0)", "grDevices", "graphics", "stats", "utils"], "Imports": ["methods"], "Suggests": ["lattice", "nlme", "nnet", "survival"], "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<PERSON>.<PERSON>@R-project.org\"), person(\"<PERSON>\", \"Venables\", role = c(\"aut\", \"cph\")), person(c(\"<PERSON>\", \"<PERSON><PERSON>\"), \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"trl\", comment = \"partial port ca 1998\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"trl\", comment = \"partial port ca 1998\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"support functions for polr\"))", "Description": "Functions and datasets to support <PERSON><PERSON><PERSON> and <PERSON>ipley, \"Modern Applied Statistics with S\" (4th edition, 2002).", "Title": "Support Functions and Datasets for Venables and <PERSON><PERSON><PERSON>'s MASS", "LazyData": "yes", "ByteCompile": "yes", "License": "GPL-2 | GPL-3", "URL": "http://www.stats.ox.ac.uk/pub/MASS4/", "Contact": "<<EMAIL>>", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre, cph], <PERSON> [aut, cph], <PERSON> [ctb], <PERSON> [trl] (partial port ca 1998), <PERSON><PERSON> [trl] (partial port ca 1998), <PERSON> [ctb] (support functions for polr)", "Maintainer": "<PERSON> <<PERSON><PERSON>@R-project.org>", "Repository": "CRAN"}, "Matrix": {"Package": "Matrix", "Version": "1.7-3", "Source": "Repository", "VersionNote": "do also bump src/version.h, inst/include/Matrix/version.h", "Date": "2025-03-05", "Priority": "recommended", "Title": "Sparse and Dense Matrix Classes and Methods", "Description": "A rich hierarchy of sparse and dense matrix classes, including general, symmetric, triangular, and diagonal matrices with numeric, logical, or pattern entries.  Efficient methods for operating on such matrices, often wrapping the 'BLAS', 'LAPACK', and 'SuiteSparse' libraries.", "License": "GPL (>= 2) | file LICENCE", "URL": "https://Matrix.R-forge.R-project.org", "BugReports": "https://R-forge.R-project.org/tracker/?atid=294&group_id=61", "Contact": "<EMAIL>", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-8316-9503\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"mm<PERSON><PERSON><PERSON>+<PERSON>@gmail.com\", comment = c(ORCID = \"0000-0002-8685-9910\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-3542-2938\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0001-7614-6899\", \"SuiteSparse libraries\", \"collaborators listed in dir(system.file(\\\"doc\\\", \\\"SuiteSparse\\\", package=\\\"Matrix\\\"), pattern=\\\"License\\\", full.names=TRUE, recursive=TRUE)\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-2753-1437\", \"METIS library\", \"Copyright: Regents of the University of Minnesota\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-4345-4200\", \"GNU Octave's condest() and onenormest()\", \"Copyright: Regents of the University of California\")), person(\"Jens\", \"Oehlschlägel\", role = \"ctb\", comment = \"initial nearPD()\"), person(\"R Core Team\", role = \"ctb\", comment = c(ROR = \"02zz1nj61\", \"base R's matrix implementation\")))", "Depends": ["R (>= 4.4)", "methods"], "Imports": ["grDevices", "graphics", "grid", "lattice", "stats", "utils"], "Suggests": ["MASS", "datasets", "sfsmisc", "tools"], "Enhances": ["SparseM", "graph"], "LazyData": "no", "LazyDataNote": "not possible, since we use data/*.R and our S4 classes", "BuildResaveData": "no", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0001-8316-9503>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-8685-9910>), <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-3542-2938>), <PERSON> [ctb] (<https://orcid.org/0000-0001-7614-6899>, SuiteSparse libraries, collaborators listed in dir(system.file(\"doc\", \"SuiteSparse\", package=\"Matrix\"), pattern=\"License\", full.names=TRUE, recursive=TRUE)), <PERSON> [ctb] (<https://orcid.org/0000-0003-2753-1437>, METIS library, Copyright: Regents of the University of Minnesota), <PERSON> [ctb] (<https://orcid.org/0000-0002-4345-4200>, GNU Octave's condest() and onenormest(), Copyright: Regents of the University of California), <PERSON><PERSON> [ctb] (initial nearPD()), R Core Team [ctb] (02zz1nj61, base R's matrix implementation)", "Maintainer": "<PERSON> <mmae<PERSON>ler+<PERSON>@gmail.com>", "Repository": "CRAN"}, "PKI": {"Package": "PKI", "Version": "0.1-14", "Source": "Repository", "Title": "Public Key Infrastucture for R Based on the X.509 Standard", "Author": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Maintainer": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Depends": ["R (>= 2.9.0)", "base64enc"], "Enhances": ["gmp"], "Description": "Public Key Infrastucture functions such as verifying certificates, RSA encription and signing which can be used to build PKI infrastructure and perform cryptographic tasks.", "License": "GPL-2 | GPL-3 | file LICENSE", "URL": "http://www.rforge.net/PKI", "SystemRequirements": "OpenSSL library and headers (openssl-dev or similar)", "NeedsCompilation": "yes", "Repository": "CRAN"}, "R6": {"Package": "R6", "Version": "2.6.1", "Source": "Repository", "Title": "Encapsulated Classes with Reference Semantics", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Creates classes with reference semantics, similar to R's built-in reference classes. Compared to reference classes, R6 classes are simpler and lighter-weight, and they are not built on S4 classes so they do not require the methods package. These classes allow public and private members, and they support inheritance, even when the classes are defined in different packages.", "License": "MIT + file LICENSE", "URL": "https://r6.r-lib.org, https://github.com/r-lib/R6", "BugReports": "https://github.com/r-lib/R6/issues", "Depends": ["R (>= 3.6)"], "Suggests": ["lobstr", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate, ggplot2, microbenchmark, scales", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "RColorBrewer": {"Package": "RColorBrewer", "Version": "1.1-3", "Source": "Repository", "Date": "2022-04-03", "Title": "ColorBrewer Palettes", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"))", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Depends": ["R (>= 2.0.0)"], "Description": "Provides color schemes for maps (and other graphics) designed by <PERSON> as described at http://colorbrewer2.org.", "License": "Apache License 2.0", "NeedsCompilation": "no", "Repository": "CRAN"}, "RMySQL": {"Package": "RMySQL", "Version": "0.11.1", "Source": "Repository", "Title": "Database Interface and 'MySQL' Driver for R", "Description": "Legacy 'DBI' interface to 'MySQL' / 'MariaDB' based on old code ported from S-PLUS. A modern 'MySQL' client written in 'C++' is available from the 'RMariaDB' package.", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", email = \"jero<PERSON><PERSON>@gmail.com\",  role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"RStudio\", role = \"cph\"))", "Depends": ["R (>= 2.8.0)", "DBI (>= 0.4)"], "Imports": ["methods"], "License": "GPL-2", "URL": "https://r-dbi.r-universe.dev/RMySQL https://downloads.mariadb.org/connector-c/", "BugReports": "https://github.com/r-dbi/rmysql/issues", "SystemRequirements": "libmariadb-client-dev | libmariadb-client-lgpl-dev | libmysqlclient-dev (deb), mariadb-devel (rpm), mariadb | mysql-connector-c (brew), mysql56_dev (csw)", "NeedsCompilation": "yes", "Collate": "'mysql.R' 'driver.R' 'connection.R' 'data-type.R' 'default.R' 'escaping.R' 'result.R' 'extension.R' 'is-valid.R' 'table.R' 'transaction.R'", "Suggests": ["testthat", "curl"], "RoxygenNote": "7.3.2.9000", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>), <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [cph]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "Rcpp": {"Package": "Rcpp", "Version": "1.0.14", "Source": "Repository", "Title": "Seamless R and C++ Integration", "Date": "2025-01-11", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"<PERSON><PERSON>\", \"<PERSON>aire\", role = \"aut\", comment = c(ORCID = \"0000-0003-0174-9868\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0003-2880-7407\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-6786-5453\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-6403-5550\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-8316-9503\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"))", "Description": "The 'Rcpp' package provides R functions as well as C++ classes which offer a seamless integration of R and C++. Many R data types and objects can be mapped back and forth to C++ equivalents which facilitates both writing of new code as well as easier integration of third-party libraries. Documentation about 'Rcpp' is provided by several vignettes included in this package, via the 'Rcpp Gallery' site at <https://gallery.rcpp.org>, the paper by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (2011, <doi:10.18637/jss.v040.i08>), the book by <PERSON><PERSON><PERSON><PERSON><PERSON> (2013, <doi:10.1007/978-1-4614-6868-4>) and the paper by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2018, <doi:10.1080/00031305.2017.1375990>); see 'citation(\"Rcpp\")' for details.", "Imports": ["methods", "utils"], "Suggests": ["tinytest", "inline", "rbenchmark", "pkgKitten (>= 0.1.2)"], "URL": "https://www.rcpp.org, https://dirk.eddelbuettel.com/code/rcpp.html, https://github.com/RcppCore/Rcpp", "License": "GPL (>= 2)", "BugReports": "https://github.com/RcppCore/Rcpp/issues", "MailingList": "<EMAIL>", "RoxygenNote": "6.1.1", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-6419-907X>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-2444-4226>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0003-0174-9868>), <PERSON> [aut] (<https://orcid.org/0000-0003-2880-7407>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-6786-5453>), <PERSON> [aut], <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-6403-5550>), <PERSON> [aut] (<https://orcid.org/0000-0001-8316-9503>), <PERSON> [aut]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "RcppTOML": {"Package": "RcppTOML", "Version": "0.2.3", "Source": "Repository", "Type": "Package", "Title": "'<PERSON><PERSON><PERSON>' Bindings to <PERSON><PERSON><PERSON> for \"Tom's Obvious Mark<PERSON>\"", "Date": "2025-03-08", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = \"Author of 'toml++' header library\"))", "Description": "The configuration format defined by 'TOML' (which expands to \"Tom's Obvious Markup Language\") specifies an excellent format (described at <https://toml.io/en/>) suitable for both human editing as well as the common uses of a machine-readable format. This package uses 'Rcpp' to connect to the 'toml++' parser written by <PERSON> to R.", "SystemRequirements": "A C++17 compiler", "BugReports": "https://github.com/eddelbuettel/rcpptoml/issues", "URL": "http://dirk.eddelbuettel.com/code/rcpp.toml.html", "Imports": ["Rcpp (>= 1.0.8)"], "Depends": ["R (>= 3.3.0)"], "LinkingTo": ["Rcpp"], "Suggests": ["tinytest"], "License": "GPL (>= 2)", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-6419-907X>), <PERSON> [aut] (Author of 'toml++' header library)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "askpass": {"Package": "askpass", "Version": "1.2.1", "Source": "Repository", "Type": "Package", "Title": "Password Entry Utilities for R, Git, and SSH", "Authors@R": "person(\"<PERSON><PERSON><PERSON>\", \"O<PERSON>\", role = c(\"aut\", \"cre\"),  email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\"))", "Description": "Cross-platform utilities for prompting the user for credentials or a  passphrase, for example to authenticate with a server or read a protected key. Includes native programs for MacOS and Windows, hence no 'tcltk' is required.  Password entry can be invoked in two different ways: directly from R via the  askpass() function, or indirectly as password-entry back-end for 'ssh-agent'  or 'git-credential' via the SSH_ASKPASS and GIT_ASKPASS environment variables. Thereby the user can be prompted for credentials or a passphrase if needed  when R calls out to git or ssh.", "License": "MIT + file LICENSE", "URL": "https://r-lib.r-universe.dev/askpass", "BugReports": "https://github.com/r-lib/askpass/issues", "Encoding": "UTF-8", "Imports": ["sys (>= 2.1)"], "RoxygenNote": "7.2.3", "Suggests": ["testthat"], "Language": "en-US", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "base64enc": {"Package": "base64enc", "Version": "0.1-3", "Source": "Repository", "Title": "Tools for base64 encoding", "Author": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Maintainer": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Depends": ["R (>= 2.9.0)"], "Enhances": ["png"], "Description": "This package provides tools for handling base64 encoding. It is more flexible than the orphaned base64 package.", "License": "GPL-2 | GPL-3", "URL": "http://www.rforge.net/base64enc", "NeedsCompilation": "yes", "Repository": "CRAN"}, "bs4Dash": {"Package": "bs4Dash", "Version": "2.3.4", "Source": "Repository", "Type": "Package", "Title": "A 'Bootstrap 4' Version of 'shinydashboard'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", email = \"dgran<PERSON>@ymail.com\", role = c(\"aut\", \"cre\")), person(family = \"RinteRface\", role = \"cph\"), person(family = \"Almasaeed Studio\", role = c(\"ctb\", \"cph\"), comment = \"AdminLTE3 theme for Bootstrap 4\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Utils functions from shinydashboard\"))", "Maintainer": "<PERSON> <dgran<PERSON>@ymail.com>", "Description": "Make 'Bootstrap 4' Shiny dashboards. Use the full power of 'AdminLTE3', a dashboard template built on top of 'Bootstrap 4'  <https://github.com/ColorlibHQ/AdminLTE>.", "URL": "https://github.com/RinteRface/bs4Dash, https://bs4dash.rinterface.com/", "BugReports": "https://github.com/RinteRface/bs4Dash/issues", "License": "GPL (>= 2) | file LICENSE", "Imports": ["shiny (>= 1.6.0)", "htmltools (>= *******)", "jsonlite (>= 0.9.16)", "fresh", "waiter (>= 0.2.3)", "httpuv (>= 1.5.2)", "lifecycle", "bslib (>= 0.2.4)", "httr"], "Suggests": ["knitr", "rmarkdown", "testthat (>= 2.1.0)", "golem", "DT", "thematic (>= 0.1.2)"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "knitr", "Collate": "'feedbacks.R' 'useful-items.R' 'tabs.R' 'render-functions.R' 'cards.R' 'dashboardSidebar.R' 'dashboardBody.R' 'dashboardFooter.R' 'dashboardControlbar.R' 'dashboardHeader.R' 'dashboardPage.R' 'aliases.R' 'auto-color.R' 'bs4Dash-package.r' 'bs4DashGallery.R' 'deps.R' 'grid.R' 'inputs.R' 'skinSelector.R' 'utils.R'", "RdMacros": "lifecycle", "Depends": ["R (>= 2.10)"], "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON><PERSON><PERSON> [cph], Almasaeed Studio [ctb, cph] (AdminLTE3 theme for Bootstrap 4), <PERSON> [ctb, cph] (Utils functions from shinydashboard)", "Repository": "CRAN"}, "bslib": {"Package": "bslib", "Version": "0.9.0", "Source": "Repository", "Title": "Custom 'Bootstrap' 'Sass' Themes for 'shiny' and 'rmarkdown'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-7111-0077\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(, \"Bootstrap contributors\", role = \"ctb\", comment = \"Bootstrap library\"), person(, \"Twitter, Inc\", role = \"cph\", comment = \"Bootstrap library\"), person(\"<PERSON><PERSON>\", \"<PERSON>gu<PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Bootstrap colorpicker library\"), person(\"<PERSON>\", \"Park\", role = c(\"ctb\", \"cph\"), comment = \"Bootswatch library\"), person(, \"PayP<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Bootstrap accessibility plugin\") )", "Description": "Simplifies custom 'CSS' styling of both 'shiny' and 'rmarkdown' via 'Bootstrap' 'Sass'. Supports 'Bootstrap' 3, 4 and 5 as well as their various 'Bootswatch' themes. An interactive widget is also provided for previewing themes in real time.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/bslib/, https://github.com/rstudio/bslib", "BugReports": "https://github.com/rstudio/bslib/issues", "Depends": ["R (>= 2.10)"], "Imports": ["base64enc", "cachem", "fastmap (>= 1.1.1)", "grDevices", "htmltools (>= 0.5.8)", "jquerylib (>= 0.1.3)", "jsonlite", "lifecycle", "memoise (>= 2.0.1)", "mime", "rlang", "sass (>= 0.4.9)"], "Suggests": ["bsicons", "curl", "fontawesome", "future", "ggplot2", "knitr", "magrit<PERSON>", "rapp<PERSON>s", "rmarkdown (>= 2.7)", "shiny (> 1.8.1)", "testthat", "thematic", "tools", "utils", "withr", "yaml"], "Config/Needs/deploy": "BH, chiflights22, colourpicker, commonmark, cpp11, cpsievert/chiflights22, cpsievert/histoslider, dplyr, DT, ggplot2, ggridges, gt, hexbin, histoslider, htmlwidgets, lattice, leaflet, lubridate, markdown, modelr, plotly, reactable, reshape2, rprojroot, rsconnect, rstudio/shiny, scales, styler, tibble", "Config/Needs/routine": "chromote, desc, renv", "Config/Needs/website": "brio, crosstalk, dplyr, DT, ggplot2, glue, htmlwidgets, leaflet, lorem, palmerpenguins, plotly, purrr, rprojroot, rstudio/htmltools, scales, stringr, tidyr, webshot2", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "zzzz-bs-sass, fonts, zzz-precompile, theme-*, rmd-*", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Collate": "'accordion.R' 'breakpoints.R' 'bs-current-theme.R' 'bs-dependencies.R' 'bs-global.R' 'bs-remove.R' 'bs-theme-layers.R' 'bs-theme-preset-bootswatch.R' 'bs-theme-preset-brand.R' 'bs-theme-preset-builtin.R' 'bs-theme-preset.R' 'utils.R' 'bs-theme-preview.R' 'bs-theme-update.R' 'bs-theme.R' 'bslib-package.R' 'buttons.R' 'card.R' 'deprecated.R' 'files.R' 'fill.R' 'imports.R' 'input-dark-mode.R' 'input-switch.R' 'layout.R' 'nav-items.R' 'nav-update.R' 'navbar_options.R' 'navs-legacy.R' 'navs.R' 'onLoad.R' 'page.R' 'popover.R' 'precompiled.R' 'print.R' 'shiny-devmode.R' 'sidebar.R' 'staticimports.R' 'tooltip.R' 'utils-deps.R' 'utils-shiny.R' 'utils-tags.R' 'value-box.R' 'version-default.R' 'versions.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [aut], <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-7111-0077>), Posit Software, PBC [cph, fnd], Bootstrap contributors [ctb] (Bootstrap library), Twitter, Inc [cph] (Bootstrap library), <PERSON><PERSON> [ctb, cph] (Bootstrap colorpicker library), <PERSON> [ctb, cph] (Bootswatch library), PayPal [ctb, cph] (Bootstrap accessibility plugin)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "cachem": {"Package": "cachem", "Version": "1.1.0", "Source": "Repository", "Title": "<PERSON><PERSON> R Objects with Automatic Pruning", "Description": "Key-value stores with automatic pruning. Caches can limit either their total size or the age of the oldest object (or both), automatically pruning objects to maintain the constraints.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", c(\"aut\", \"cre\")), person(family = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")))", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "ByteCompile": "true", "URL": "https://cachem.r-lib.org/, https://github.com/r-lib/cachem", "Imports": ["rlang", "fastmap (>= 1.2.0)"], "Suggests": ["testthat"], "RoxygenNote": "7.2.3", "Config/Needs/routine": "lobstr", "Config/Needs/website": "pkgdown", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "cli": {"Package": "cli", "Version": "3.6.5", "Source": "Repository", "Title": "Helpers for Developing Command Line Interfaces", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"g<PERSON><PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A suite of tools to build attractive command line interfaces ('CLIs'), from semantic elements: headings, lists, alerts, paragraphs, etc. Supports custom themes via a 'CSS'-like language. It also contains a number of lower level 'CLI' elements: rules, boxes, trees, and 'Unicode' symbols with 'ASCII' alternatives. It support ANSI colors and text styles as well.", "License": "MIT + file LICENSE", "URL": "https://cli.r-lib.org, https://github.com/r-lib/cli", "BugReports": "https://github.com/r-lib/cli/issues", "Depends": ["R (>= 3.4)"], "Imports": ["utils"], "Suggests": ["callr", "covr", "crayon", "digest", "glue (>= 1.6.0)", "grDevices", "htmltools", "htmlwidgets", "knitr", "methods", "processx", "ps (>= 1.3.4.9000)", "rlang (>= 1.0.2.9003)", "rmarkdown", "rprojroot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testthat (>= 3.2.0)", "tibble", "whoami", "withr"], "Config/Needs/website": "r-lib/asciicast, bench, brio, cpp11, decor, desc, fansi, prettyunits, sessioninfo, tidyverse/tidytemplate, usethis, vctrs", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-5329-5987>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "commonmark": {"Package": "commonmark", "Version": "1.9.5", "Source": "Repository", "Type": "Package", "Title": "High Performance CommonMark and Github Markdown Rendering in R", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", ,\"jero<PERSON><PERSON>@gmail.com\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", role = \"cph\", comment = \"Author of cmark\"))", "Description": "The CommonMark specification <https://github.github.com/gfm/> defines a rationalized version of markdown syntax. This package uses the 'cmark'  reference implementation for converting markdown text into various formats including html, latex and groff man. In addition it exposes the markdown parse tree in xml format. Also includes opt-in support for GFM extensions including tables, autolinks, and strikethrough text.", "License": "BSD_2_clause + file LICENSE", "URL": "https://docs.ropensci.org/commonmark/ https://ropensci.r-universe.dev/commonmark", "BugReports": "https://github.com/r-lib/commonmark/issues", "Suggests": ["curl", "testthat", "xml2"], "RoxygenNote": "7.3.2", "Language": "en-US", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>), <PERSON> [cph] (Author of cmark)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "cpp11": {"Package": "cpp11", "Version": "0.5.2", "Source": "Repository", "Title": "A C++11 Interface for R's C Interface", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4777-038X\")), person(\"<PERSON>\",\"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides a header only, C++11 interface to R's C interface.  Compared to other approaches 'cpp11' strives to be safe against long jumps from the C API as well as C++ exceptions, conform to normal R function semantics and supports interaction with 'ALTREP' vectors.", "License": "MIT + file LICENSE", "URL": "https://cpp11.r-lib.org, https://github.com/r-lib/cpp11", "BugReports": "https://github.com/r-lib/cpp11/issues", "Depends": ["R (>= 4.0.0)"], "Suggests": ["bench", "brio", "callr", "cli", "covr", "decor", "desc", "ggplot2", "glue", "knitr", "lobstr", "mockery", "progress", "rmarkdown", "scales", "Rcpp", "testthat (>= 3.2.0)", "tibble", "utils", "vctrs", "withr"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/Needs/cpp11/cpp_register": "brio, cli, decor, desc, glue, tibble, vctrs", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-4777-038X>), <PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-2444-4226>), <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "crosstalk": {"Package": "crosstalk", "Version": "1.2.1", "Source": "Repository", "Type": "Package", "Title": "Inter-Widget Interactivity for HTML Widgets", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(family = \"jQuery Foundation\", role = \"cph\", comment = \"jQuery library and jQuery UI library\"), person(family = \"jQuery contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery library; authors listed in inst/www/shared/jquery-AUTHORS.txt\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"Bootstrap library\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"Bootstrap library\"), person(family = \"Bootstrap contributors\", role = \"ctb\", comment = \"Bootstrap library\"), person(family = \"Twitter, Inc\", role = \"cph\", comment = \"<PERSON><PERSON><PERSON> library\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"selectize.js library\"), person(\"<PERSON><PERSON>her <PERSON>\", \"<PERSON>wal\", role = c(\"ctb\", \"cph\"), comment = \"es5-shim library\"), person(family = \"es5-shim contributors\", role = c(\"ctb\", \"cph\"), comment = \"es5-shim library\"), person(\"Denis\", \"Ineshin\", role = c(\"ctb\", \"cph\"), comment = \"ion.rangeSlider library\"), person(\"Sami\", \"Samhuri\", role = c(\"ctb\", \"cph\"), comment = \"Javascript strftime library\") )", "Description": "Provides building blocks for allowing HTML widgets to communicate with each other, with <PERSON>y or without (i.e. static .html files). Currently supports linked brushing and filtering.", "License": "MIT + file LICENSE", "Imports": ["htmltools (>= 0.3.6)", "jsonlite", "lazyeval", "R6"], "Suggests": ["shiny", "ggplot2", "testthat (>= 2.1.0)", "sass", "bslib"], "URL": "https://rstudio.github.io/crosstalk/, https://github.com/rstudio/crosstalk", "BugReports": "https://github.com/rstudio/crosstalk/issues", "RoxygenNote": "7.2.3", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), Posit Software, PBC [cph, fnd], jQuery Foundation [cph] (jQuery library and jQuery UI library), jQuery contributors [ctb, cph] (jQuery library; authors listed in inst/www/shared/jquery-AUTHORS.txt), <PERSON> [ctb] (Bootstrap library), <PERSON> [ctb] (Bootstrap library), Bootstrap contributors [ctb] (Bootstrap library), Twitter, Inc [cph] (Bootstrap library), <PERSON> [ctb, cph] (selectize.js library), <PERSON><PERSON><PERSON> [ctb, cph] (es5-shim library), es5-shim contributors [ctb, cph] (es5-shim library), <PERSON> [ctb, cph] (ion.rangeSlider library), <PERSON> [ctb, cph] (Javascript strftime library)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "curl": {"Package": "curl", "Version": "6.4.0", "Source": "Repository", "Type": "Package", "Title": "A Modern and Flexible Web Client for R", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"O<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = \"cph\"))", "Description": "Bindings to 'libcurl' <https://curl.se/libcurl/> for performing fully configurable HTTP/FTP requests where responses can be processed in memory, on disk, or streaming via the callback or connection interfaces. Some knowledge of 'libcurl' is recommended; for a more-user-friendly web client see the  'httr2' package which builds on this package with http specific tools and logic.", "License": "MIT + file LICENSE", "SystemRequirements": "libcurl (>= 7.73): libcurl-devel (rpm) or libcurl4-openssl-dev (deb)", "URL": "https://jeroen.r-universe.dev/curl", "BugReports": "https://github.com/jeroen/curl/issues", "Suggests": ["spelling", "testthat (>= 1.0.0)", "knitr", "jsonlite", "later", "rmarkdown", "httpuv (>= 1.4.4)", "webutils"], "VignetteBuilder": "knitr", "Depends": ["R (>= 3.0.0)"], "RoxygenNote": "7.3.2.9000", "Encoding": "UTF-8", "Language": "en-US", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb], Posit Software, PBC [cph]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "data.table": {"Package": "data.table", "Version": "1.17.6", "Source": "Repository", "Title": "Extension of `data.frame`", "Depends": ["R (>= 3.3.0)"], "Imports": ["methods"], "Suggests": ["bit64 (>= 4.0.0)", "bit (>= 4.0.4)", "<PERSON><PERSON>utils", "xts", "zoo (>= 1.8-1)", "yaml", "knitr", "markdown"], "Description": "Fast aggregation of large data (e.g. 100GB in RAM), fast ordered joins, fast add/modify/delete of columns by group using no copies at all, list columns, friendly and fast character-separated-value read/write. Offers a natural and flexible syntax, for faster development.", "License": "MPL-2.0 | file LICENSE", "URL": "https://r-datatable.com, https://Rdatatable.gitlab.io/data.table, https://github.com/Rdatatable/data.table", "BugReports": "https://github.com/Rdatatable/data.table/issues", "VignetteBuilder": "knitr", "Encoding": "UTF-8", "ByteCompile": "TRUE", "Authors@R": "c( person(\"<PERSON>\",\"<PERSON>\",        role=c(\"aut\",\"cre\"), email=\"<EMAIL>\", comment = c(ORCID=\"0000-0002-2137-1391\")), person(\"<PERSON>\",\"<PERSON><PERSON>\",           role=\"aut\",          email=\"mattj<PERSON><PERSON><EMAIL>\"), person(\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",      role=\"aut\",          email=\"as<PERSON>@pm.me\"), person(\"<PERSON>\",\"<PERSON><PERSON>\",          role=\"aut\"), person(\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",      role=\"aut\", comment = c(ORCID=\"0000-0003-0787-087X\")), person(\"<PERSON>\",\"Hocking\",         role=\"aut\", comment = c(ORCID=\"0000-0002-3146-0865\")), person(\"<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",role=\"aut\", comment = c(ORCID=\"0000-0003-3315-8114\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\",         role=\"aut\",          email=\"i<PERSON><PERSON><PERSON>@disroot.org\",   comment = c(ORCID=\"0000-0002-0172-3812\")), person(\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",      role=\"ctb\"), person(\"<PERSON>\",\"<PERSON>\",            role=\"ctb\"), person(\"<PERSON>\",\"<PERSON>noglou\",      role=\"ctb\"), person(\"<PERSON>\",\"<PERSON><PERSON>\",      role=\"ctb\"), person(\"<PERSON>\",\"<PERSON><PERSON>\",        role=\"ctb\"), person(\"<PERSON>\",\"<PERSON>rsonage\",       role=\"ctb\"), person(\"<PERSON>\",\"<PERSON>\",        role=\"ctb\"), person(\"<PERSON>n\",\"<PERSON>\",              role=\"ctb\"), person(\"Xianying\",\"Tan\",         role=\"ctb\"), person(\"Rick\",\"Saporta\",         role=\"ctb\"), person(\"Otto\",\"Seiskari\",        role=\"ctb\"), person(\"Xianghui\",\"Dong\",        role=\"ctb\"), person(\"Michel\",\"Lang\",          role=\"ctb\"), person(\"Watal\",\"Iwasaki\",        role=\"ctb\"), person(\"Seth\",\"Wenchel\",         role=\"ctb\"), person(\"Karl\",\"Broman\",          role=\"ctb\"), person(\"Tobias\",\"Schmidt\",       role=\"ctb\"), person(\"David\",\"Arenburg\",       role=\"ctb\"), person(\"Ethan\",\"Smith\",          role=\"ctb\"), person(\"Francois\",\"Cocquemas\",   role=\"ctb\"), person(\"Matthieu\",\"Gomez\",       role=\"ctb\"), person(\"Philippe\",\"Chataignon\",  role=\"ctb\"), person(\"Nello\",\"Blaser\",         role=\"ctb\"), person(\"Dmitry\",\"Selivanov\",     role=\"ctb\"), person(\"Andrey\",\"Riabushenko\",   role=\"ctb\"), person(\"Cheng\",\"Lee\",            role=\"ctb\"), person(\"Declan\",\"Groves\",        role=\"ctb\"), person(\"Daniel\",\"Possenriede\",   role=\"ctb\"), person(\"Felipe\",\"Parages\",       role=\"ctb\"), person(\"Denes\",\"Toth\",           role=\"ctb\"), person(\"Mus\",\"Yaramaz-David\",    role=\"ctb\"), person(\"Ayappan\",\"Perumal\",      role=\"ctb\"), person(\"James\",\"Sams\",           role=\"ctb\"), person(\"Martin\",\"Morgan\",        role=\"ctb\"), person(\"Michael\",\"Quinn\",        role=\"ctb\"), person(\"@javrucebo\",\"\",          role=\"ctb\"), person(\"@marc-outins\",\"\",        role=\"ctb\"), person(\"Roy\",\"Storey\",           role=\"ctb\"), person(\"Manish\",\"Saraswat\",      role=\"ctb\"), person(\"Morgan\",\"Jacob\",         role=\"ctb\"), person(\"Michael\",\"Schubmehl\",    role=\"ctb\"), person(\"Davis\",\"Vaughan\",        role=\"ctb\"), person(\"Leonardo\",\"Silvestri\",   role=\"ctb\"), person(\"Jim\",\"Hester\",           role=\"ctb\"), person(\"Anthony\",\"Damico\",       role=\"ctb\"), person(\"Sebastian\",\"Freundt\",    role=\"ctb\"), person(\"David\",\"Simons\",         role=\"ctb\"), person(\"Elliott\",\"Sales de Andrade\", role=\"ctb\"), person(\"Cole\",\"Miller\",          role=\"ctb\"), person(\"Jens Peder\",\"Meldgaard\", role=\"ctb\"), person(\"Vaclav\",\"Tlapak\",        role=\"ctb\"), person(\"Kevin\",\"Ushey\",          role=\"ctb\"), person(\"Dirk\",\"Eddelbuettel\",    role=\"ctb\"), person(\"Tony\",\"Fischetti\",       role=\"ctb\"), person(\"Ofek\",\"Shilon\",          role=\"ctb\"), person(\"Vadim\",\"Khotilovich\",    role=\"ctb\"), person(\"Hadley\",\"Wickham\",       role=\"ctb\"), person(\"Bennet\",\"Becker\",        role=\"ctb\"), person(\"Kyle\",\"Haynes\",          role=\"ctb\"), person(\"Boniface Christian\",\"Kamgang\", role=\"ctb\"), person(\"Olivier\",\"Delmarcell\",   role=\"ctb\"), person(\"Josh\",\"O'Brien\",         role=\"ctb\"), person(\"Dereck\",\"de Mezquita\",   role=\"ctb\"), person(\"Michael\",\"Czekanski\",    role=\"ctb\"), person(\"Dmitry\", \"Shemetov\",     role=\"ctb\"), person(\"Nitish\", \"Jha\",          role=\"ctb\"), person(\"Joshua\", \"Wu\",           role=\"ctb\"), person(\"Iago\", \"Giné-Vázquez\",   role=\"ctb\"), person(\"Anirban\", \"Chetia\",      role=\"ctb\"), person(\"Doris\", \"Amoakohene\",    role=\"ctb\"), person(\"Angel\", \"Feliz\",         role=\"ctb\"), person(\"Michael\",\"Young\",        role=\"ctb\"), person(\"Mark\", \"Seeto\",          role=\"ctb\"), person(\"Philippe\", \"Grosjean\",   role=\"ctb\"), person(\"Vincent\", \"Runge\",       role=\"ctb\"), person(\"Christian\", \"Wia\",       role=\"ctb\"), person(\"Elise\", \"Maigné\",        role=\"ctb\"), person(\"Vincent\", \"Rocher\",      role=\"ctb\"), person(\"Vijay\", \"Lulla\",         role=\"ctb\"), person(\"Aljaž\", \"Sluga\",         role=\"ctb\"), person(\"Bill\", \"Evans\",          role=\"ctb\") )", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-2137-1391>), <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-0787-087X>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-3146-0865>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-3315-8114>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-0172-3812>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON><PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON> [c<PERSON>b], <PERSON> <PERSON> [c<PERSON><PERSON>], <PERSON> <PERSON><PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> <PERSON>ramaz-David [ctb], Ayappan Perumal [ctb], James Sams [ctb], Martin Morgan [ctb], Michael Quinn [ctb], @javrucebo [ctb], @marc-outins [ctb], Roy Storey [ctb], Manish Saraswat [ctb], Morgan Jacob [ctb], Michael Schubmehl [ctb], Davis Vaughan [ctb], Leonardo Silvestri [ctb], Jim Hester [ctb], Anthony Damico [ctb], Sebastian Freundt [ctb], David Simons [ctb], Elliott Sales de Andrade [ctb], Cole Miller [ctb], Jens Peder Meldgaard [ctb], Vaclav Tlapak [ctb], Kevin Ushey [ctb], Dirk Eddelbuettel [ctb], Tony Fischetti [ctb], Ofek Shilon [ctb], Vadim Khotilovich [ctb], Hadley Wickham [ctb], Bennet Becker [ctb], Kyle Haynes [ctb], Boniface Christian Kamgang [ctb], Olivier Delmarcell [ctb], Josh O'Brien [ctb], Dereck de Mezquita [ctb], Michael Czekanski [ctb], Dmitry Shemetov [ctb], Nitish Jha [ctb], Joshua Wu [ctb], Iago Giné-Vázquez [ctb], Anirban Chetia [ctb], Doris Amoakohene [ctb], Angel Feliz [ctb], Michael Young [ctb], Mark Seeto [ctb], Philippe Grosjean [ctb], Vincent Runge [ctb], Christian Wia [ctb], Elise Maigné [ctb], Vincent Rocher [ctb], Vijay Lulla [ctb], Aljaž Sluga [ctb], Bill Evans [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "digest": {"Package": "digest", "Version": "0.6.37", "Source": "Repository", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", comment = c(ORCID = \"0000-0002-7579-5165\")), person(\"<PERSON>\", \"<PERSON>ek\", role=\"ctb\", comment = c(ORCID = \"0000-0003-2297-1732\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\", comment = c(ORCID = \"0000-0001-5180-0567\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\", comment = c(ORCID = \"0000-0001-6786-5453\")), person(\"Thierry\", \"Onkelinx\", role=\"ctb\", comment = c(ORCID = \"0000-0001-8804-4216\")), person(\"Michel\", \"Lang\", role=\"ctb\", comment = c(ORCID = \"0000-0001-9754-0393\")), person(\"Viliam\", \"Simko\", role=\"ctb\"), person(\"Kurt\", \"Hornik\", role=\"ctb\", comment = c(ORCID = \"0000-0003-4198-9911\")), person(\"Radford\", \"Neal\", role=\"ctb\", comment = c(ORCID = \"0000-0002-2473-3407\")), person(\"Kendon\", \"Bell\", role=\"ctb\", comment = c(ORCID = \"0000-0002-9093-8312\")), person(\"Matthew\", \"de Queljoe\", role=\"ctb\"), person(\"Dmitry\", \"Selivanov\", role=\"ctb\"), person(\"Ion\", \"Suruceanu\", role=\"ctb\"), person(\"Bill\", \"Denney\", role=\"ctb\"), person(\"Dirk\", \"Schumacher\", role=\"ctb\"), person(\"András\", \"Svraka\", role=\"ctb\"), person(\"Sergey\", \"Fedorov\", role=\"ctb\"), person(\"Will\", \"Landau\", role=\"ctb\", comment = c(ORCID = \"0000-0003-1878-3253\")), person(\"Floris\", \"Vanderhaeghe\", role=\"ctb\", comment = c(ORCID = \"0000-0002-6378-6229\")), person(\"Kevin\", \"Tappe\", role=\"ctb\"), person(\"Harris\", \"McGehee\", role=\"ctb\"), person(\"Tim\", \"Mastny\", role=\"ctb\"), person(\"Aaron\", \"Peikert\", role=\"ctb\", comment = c(ORCID = \"0000-0001-7813-818X\")), person(\"Mark\", \"van der Loo\", role=\"ctb\", comment = c(ORCID = \"0000-0002-9807-4686\")), person(\"Chris\", \"Muir\", role=\"ctb\", comment = c(ORCID = \"0000-0003-2555-3878\")), person(\"Moritz\", \"Beller\", role=\"ctb\", comment = c(ORCID = \"0000-0003-4852-0526\")), person(\"Sebastian\", \"Campbell\", role=\"ctb\"), person(\"Winston\", \"Chang\", role=\"ctb\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"Dean\", \"Attali\", role=\"ctb\", comment = c(ORCID = \"0000-0002-5645-3493\")), person(\"Michael\", \"Chirico\", role=\"ctb\", comment = c(ORCID = \"0000-0003-0787-087X\")), person(\"Kevin\", \"Ushey\", role=\"ctb\"))", "Date": "2024-08-19", "Title": "Create Compact Hash Digests of R Objects", "Description": "Implementation of a function 'digest()' for the creation of hash digests of arbitrary R objects (using the 'md5', 'sha-1', 'sha-256', 'crc32', 'xxhash', 'murmurhash', 'spookyhash', 'blake3', 'crc32c', 'xxh3_64', and 'xxh3_128' algorithms) permitting easy comparison of R language objects, as well as functions such as'hmac()' to create hash-based message authentication code. Please note that this package is not meant to be deployed for cryptographic purposes for which more comprehensive (and widely tested) libraries such as 'OpenSSL' should be used.", "URL": "https://github.com/eddelbuettel/digest, https://dirk.eddelbuettel.com/code/digest.html", "BugReports": "https://github.com/eddelbuettel/digest/issues", "Depends": ["R (>= 3.3.0)"], "Imports": ["utils"], "License": "GPL (>= 2)", "Suggests": ["tinytest", "simplermarkdown"], "VignetteBuilder": "simplermarkdown", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-6419-907X>), <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0002-7579-5165>), <PERSON> [ctb] (<https://orcid.org/0000-0003-2297-1732>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-5180-0567>), <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-6786-5453>), <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-8804-4216>), <PERSON> [ctb] (<https://orcid.org/0000-0001-9754-0393>), <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0003-4198-9911>), <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-2473-3407>), <PERSON>don <PERSON> [ctb] (<https://or<PERSON>.org/0000-0002-9093-8312>), <PERSON> de <PERSON>l<PERSON>e [ctb], <PERSON> <PERSON><PERSON><PERSON> [ctb], <PERSON> <PERSON><PERSON><PERSON> [ctb], <PERSON> <PERSON><PERSON> [ctb], <PERSON> <PERSON> [ctb], Andr<PERSON> <PERSON>v<PERSON><PERSON> [ctb], <PERSON> <PERSON><PERSON>v [ctb], Will Landau [ctb] (<https://orcid.org/0000-0003-1878-3253>), Floris Vanderhaeghe [ctb] (<https://orcid.org/0000-0002-6378-6229>), Kevin Tappe [ctb], Harris McGehee [ctb], Tim Mastny [ctb], Aaron Peikert [ctb] (<https://orcid.org/0000-0001-7813-818X>), Mark van der Loo [ctb] (<https://orcid.org/0000-0002-9807-4686>), Chris Muir [ctb] (<https://orcid.org/0000-0003-2555-3878>), Moritz Beller [ctb] (<https://orcid.org/0000-0003-4852-0526>), Sebastian Campbell [ctb], Winston Chang [ctb] (<https://orcid.org/0000-0002-1576-2126>), Dean Attali [ctb] (<https://orcid.org/0000-0002-5645-3493>), Michael Chirico [ctb] (<https://orcid.org/0000-0003-0787-087X>), Kevin Ushey [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "dplyr": {"Package": "dplyr", "Version": "1.1.4", "Source": "Repository", "Type": "Package", "Title": "A Grammar of Data Manipulation", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-4777-038X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A fast, consistent tool for working with data frame like objects, both in memory and out of memory.", "License": "MIT + file LICENSE", "URL": "https://dplyr.tidyverse.org, https://github.com/tidyverse/dplyr", "BugReports": "https://github.com/tidyverse/dplyr/issues", "Depends": ["R (>= 3.5.0)"], "Imports": ["cli (>= 3.4.0)", "generics", "glue (>= 1.3.2)", "lifecycle (>= 1.0.3)", "magrittr (>= 1.5)", "methods", "pillar (>= 1.9.0)", "R6", "rlang (>= 1.1.0)", "tibble (>= 3.2.0)", "tidyselect (>= 1.2.0)", "utils", "vctrs (>= 0.6.4)"], "Suggests": ["bench", "broom", "callr", "covr", "DBI", "dbplyr (>= 2.2.1)", "ggplot2", "knitr", "<PERSON><PERSON>", "lobstr", "microbenchmark", "nycflights13", "purrr", "rmarkdown", "RMySQL", "RPostgreSQL", "RSQLite", "stringi (>= 1.7.6)", "testthat (>= 3.1.5)", "tidyr (>= 1.3.0)", "withr"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse, shiny, pkgdown, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-4757-117X>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-2444-4226>), <PERSON> [aut], <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut] (<https://orcid.org/0000-0003-4777-038X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "evaluate": {"Package": "evaluate", "Version": "1.0.4", "Source": "Repository", "Type": "Package", "Title": "Parsing and Evaluation Tools that Provide More Details than the Default", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Parsing and evaluation tools that make it easy to recreate the command line behaviour of R.", "License": "MIT + file LICENSE", "URL": "https://evaluate.r-lib.org/, https://github.com/r-lib/evaluate", "BugReports": "https://github.com/r-lib/evaluate/issues", "Depends": ["R (>= 3.6.0)"], "Suggests": ["callr", "covr", "ggplot2 (>= 3.3.6)", "lattice", "methods", "pkgload", "ragg (>= 1.4.0)", "rlang (>= 1.1.5)", "knitr", "testthat (>= 3.0.0)", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-0645-5666>), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "farver": {"Package": "farver", "Version": "2.1.2", "Source": "Repository", "Type": "Package", "Title": "High Performance Colour Space Manipulation", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"cre\", \"aut\"), comment = c(ORCID = \"0000-0002-5147-4711\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = \"Author of the ColorSpace C++ library\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "The encoding of colour can be handled in many different ways, using different colour spaces. As different colour spaces have different uses, efficient conversion between these representations are important. The 'farver' package provides a set of functions that gives access to very fast colour space conversion and comparisons implemented in C++, and offers speed improvements over the 'convertColor' function in the 'grDevices' package.", "License": "MIT + file LICENSE", "URL": "https://farver.data-imaginist.com, https://github.com/thomasp85/farver", "BugReports": "https://github.com/thomasp85/farver/issues", "Suggests": ["covr", "testthat (>= 3.0.0)"], "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "yes", "Author": "<PERSON> [cre, aut] (<https://orcid.org/0000-0002-5147-4711>), <PERSON><PERSON><PERSON> [aut] (Author of the ColorSpace C++ library), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-2444-4226>), <PERSON><PERSON>t, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fastmap": {"Package": "fastmap", "Version": "1.2.0", "Source": "Repository", "Title": "Fast Data Structures", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(given = \"Tessil\", role = \"cph\", comment = \"hopscotch_map library\") )", "Description": "Fast implementation of data structures, including a key-value store, stack, and queue. Environments are commonly used as key-value stores in R, but every time a new key is used, it is added to R's global symbol table, causing a small amount of memory leakage. This can be problematic in cases where many different keys are used. Fastmap avoids this memory leak issue by implementing the map using data structures in C++.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "Suggests": ["testthat (>= 2.1.1)"], "URL": "https://r-lib.github.io/fastmap/, https://github.com/r-lib/fastmap", "BugReports": "https://github.com/r-lib/fastmap/issues", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd], Tessil [cph] (hopscotch_map library)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "flexdashboard": {"Package": "flexdashboard", "Version": "0.6.2", "Source": "Repository", "Type": "Package", "Title": "R Markdown Format for Flexible Dashboards", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-7111-0077\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-3925-190X\")), person(\"<PERSON><PERSON>\", \"Allaire\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(, \"<PERSON><PERSON> IO\", role = c(\"ctb\", \"cph\"), comment = \"Dashboard CSS\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Dashboard CSS\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"<PERSON>yTable<PERSON>eaders\"), person(\"<PERSON>\", \"<PERSON>art\", role = c(\"ctb\", \"cph\"), comment = \"<PERSON>atherlight\"), person(\"<PERSON>\", \"Verou\", role = c(\"ctb\", \"cph\"), comment = \"P<PERSON>\"), person(\"<PERSON>\", \"Baranovskiy\", role = c(\"ctb\", \"cph\"), comment = \"<PERSON>.js\"), person(, \"Sencha Labs\", role = c(\"ctb\", \"cph\"), comment = \"Raphael.js\"), person(\"Bojan\", \"Djuricic\", role = c(\"ctb\", \"cph\"), comment = \"JustGage\"), person(\"Tomas\", \"Sardyha\", role = c(\"ctb\", \"cph\"), comment = \"Sly\"), person(\"Bryan\", \"Lewis\", role = c(\"ctb\", \"cph\"), comment = \"Examples\"), person(\"Joshua\", \"Kunst\", role = c(\"ctb\", \"cph\"), comment = \"Examples\"), person(\"Ryan\", \"Hafen\", role = c(\"ctb\", \"cph\"), comment = \"Examples\"), person(\"Bob\", \"Rudis\", role = c(\"ctb\", \"cph\"), comment = \"Examples\"), person(\"Joe\", \"Cheng\", role = \"ctb\", comment = \"Examples\") )", "Description": "Format for converting an R Markdown document to a grid oriented dashboard. The dashboard flexibly adapts the size of it's components to the containing web page.", "License": "MIT + file LICENSE", "URL": "https://pkgs.rstudio.com/flexdashboard/, https://github.com/rstudio/flexdashboard/", "BugReports": "https://github.com/rstudio/flexdashboard/issues", "Depends": ["R (>= 3.0.2)"], "Imports": ["bslib (>= 0.2.5)", "grDevices", "htmltools (>= 0.5.1)", "htmlwidgets (>= 0.6)", "jsonlite", "knitr (>= 1.30)", "rmarkdown (>= 2.8)", "sass", "scales", "shiny (>= 0.13)", "tools", "utils"], "Suggests": ["testthat"], "Config/Needs/deploy": "dplyr, ggplot2, DT, talgalili/d3heatmap, plotly, plyr, biclust, webshot, bit, jcheng5/bubbles, digest, hadley/shinySignals, dygraphs, quantmod, forecast, highcharter, arules, treemap, viridisLite, leaflet, metricsgraphics, rbokeh, readr, tidyr, jsonlite, maptools, purrr, maps, hexbin", "Config/Needs/website": "rstudio/quillt", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-7111-0077>), <PERSON> [aut] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [aut] (<https://orcid.org/0000-0003-3925-190X>), <PERSON><PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd], <PERSON><PERSON> [ctb, cph] (Dashboard CSS), <PERSON> [ctb, cph] (Dashboard CSS), <PERSON> [ctb, cph] (StickyTableHeaders), <PERSON> [ctb, cph] (Featherlight), <PERSON> [ctb, cph] (Prism), <PERSON> [ctb, cph] (Raphael.js), <PERSON><PERSON> [ctb, cph] (Raphael.js), <PERSON><PERSON> [ctb, cph] (JustGage), <PERSON> [ctb, cph] (Sly), <PERSON> [ctb, cph] (Examples), <PERSON> [ctb, cph] (Examples), <PERSON> [ctb, cph] (Examples), <PERSON> [ctb, cph] (Examples), <PERSON> [ctb] (Examples)", "Maintainer": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fontawesome": {"Package": "fontawesome", "Version": "0.5.3", "Source": "Repository", "Type": "Package", "Title": "Easily Work with 'Font Awesome' Icons", "Description": "Easily and flexibly insert 'Font Awesome' icons into 'R Markdown' documents and 'Shiny' apps. These icons can be inserted into HTML content through inline 'SVG' tags or 'i' tags. There is also a utility function for exporting 'Font Awesome' icons as 'PNG' images for those situations where raster graphics are needed.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-3925-190X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-4474-2498\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Font-Awesome font\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "License": "MIT + file LICENSE", "URL": "https://github.com/rstudio/fontawesome, https://rstudio.github.io/fontawesome/", "BugReports": "https://github.com/rstudio/fontawesome/issues", "Encoding": "UTF-8", "ByteCompile": "true", "RoxygenNote": "7.3.2", "Depends": ["R (>= 3.3.0)"], "Imports": ["rlang (>= 1.0.6)", "htmltools (>= *******)"], "Suggests": ["covr", "dplyr (>= 1.0.8)", "gt (>= 0.9.0)", "knitr (>= 1.31)", "testthat (>= 3.0.0)", "rsvg"], "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-3925-190X>), <PERSON> [ctb] (<https://orcid.org/0000-0003-4474-2498>), <PERSON> [ctb], <PERSON> [ctb, cph] (Font-Awesome font), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fresh": {"Package": "fresh", "Version": "0.2.1", "Source": "Repository", "Title": "Create Custom 'Bootstrap' Themes to Use in 'Shiny'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Bootswatch themes\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"Bootstrap library\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"Bootstrap library\"), person(family = \"Bootstrap contributors\", role = \"ctb\", comment = \"Bootstrap library\"), person(family = \"Twitter, Inc\", role = \"cph\", comment = \"Bootstrap library\"), person(\"onkbear\", role = c(\"ctb\", \"cph\"), comment = \"admin-lte-2-sass\"), person(family = \"Colorlib\", role = c(\"ctb\", \"cph\"), comment = \"AdminLTE\"))", "Description": "Customize 'Bootstrap' and 'Bootswatch' themes, like colors, fonts, grid layout,  to use in 'Shiny' applications, 'rmarkdown' documents and 'flexdashboard'.", "URL": "https://github.com/dreamRs/fresh", "BugReports": "https://github.com/dreamRs/fresh/issues", "License": "GPL-3", "Encoding": "UTF-8", "Imports": ["bslib", "htmltools", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sass", "shiny"], "Suggests": ["bsicons", "shinyWidgets", "shinydashboard", "bs4Dash", "knitr", "rmarkdown", "testthat (>= 2.1.0)", "covr"], "RoxygenNote": "7.3.1", "VignetteBuilder": "knitr", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre, cph], <PERSON> [aut], <PERSON> [ctb, cph] (Bootswatch themes), <PERSON> [ctb] (Bootstrap library), <PERSON> [ctb] (Bootstrap library), Bootstrap contributors [ctb] (Bootstrap library), Twitter, Inc [cph] (Bootstrap library), onkbear [ctb, cph] (admin-lte-2-sass), Colorlib [ctb, cph] (AdminLTE)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fs": {"Package": "fs", "Version": "1.6.6", "Source": "Repository", "Title": "Cross-Platform File System Operations Based on 'libuv'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"libuv project contributors\", role = \"cph\", comment = \"libuv library\"), person(\"Joyent, Inc. and other Node contributors\", role = \"cph\", comment = \"libuv library\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A cross-platform interface to file system operations, built on top of the 'libuv' C library.", "License": "MIT + file LICENSE", "URL": "https://fs.r-lib.org, https://github.com/r-lib/fs", "BugReports": "https://github.com/r-lib/fs/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["covr", "crayon", "knitr", "pillar (>= 1.0.0)", "rmarkdown", "spelling", "testthat (>= 3.0.0)", "tibble (>= 1.1.0)", "vctrs (>= 0.3.0)", "withr"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Copyright": "file COPYRIGHTS", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3", "SystemRequirements": "GNU make", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], libuv project contributors [cph] (libuv library), Joyent, Inc. and other Node contributors [cph] (libuv library), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "generics": {"Package": "generics", "Version": "0.1.4", "Source": "Repository", "Title": "Common S3 Generics not Provided by Base R Methods Related to Model Fitting", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"https://ror.org/03wc8by49\")) )", "Description": "In order to reduce potential package dependencies and conflicts, generics provides a number of commonly used S3 generics.", "License": "MIT + file LICENSE", "URL": "https://generics.r-lib.org, https://github.com/r-lib/generics", "BugReports": "https://github.com/r-lib/generics/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["covr", "pkgload", "testthat (>= 3.0.0)", "tibble", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "ggiraph": {"Package": "ggiraph", "Version": "0.8.13", "Source": "Repository", "Type": "Package", "Title": "Make 'ggplot2' Graphics Interactive", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>agiot<PERSON>\", \"Skintz<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"cph\", comment = \"d3.js\"), person(\"<PERSON><PERSON><PERSON>\", \"Koken<PERSON>\", role = \"cph\", comment = \"d3-lasso\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"cph\", comment = \"saveSvgAsPng js library\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"cph\", comment = \"TinyXML2\"), person(\"<PERSON>\", \"<PERSON>gafonk<PERSON>\", role = \"cph\", comment = \"Flatbush\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"hline and vline geoms\") )", "Description": "Create interactive 'ggplot2' graphics using 'htmlwidgets'.", "License": "GPL-3", "URL": "https://davidgohel.github.io/ggiraph/", "BugReports": "https://github.com/davidgohel/ggiraph/issues", "Imports": ["cli", "ggplot2 (>= 3.5.1)", "grid", "htmltools", "htmlwidgets (>= 1.5)", "purrr", "Rcpp (>= 1.0)", "rlang", "stats", "systemfonts", "uuid", "vctrs"], "Suggests": ["dplyr", "gdtools (>= 0.3.0)", "ggrepel (>= 0.9.1)", "hex<PERSON>", "knitr", "maps", "quantreg", "rmarkdown", "sf (>= 1.0)", "shiny", "tinytest", "xml2 (>= 1.0)"], "LinkingTo": ["Rcpp", "systemfonts"], "VignetteBuilder": "knitr", "Copyright": "See file COPYRIGHTS.", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "SystemRequirements": "libpng", "Collate": "'RcppExports.R' 'ipar.R' 'utils_ggplot2_performance.R' 'utils_ggplot2.R' 'utils.R' 'annotate_interactive.R' 'annotation_raster_interactive.R' 'utils_css.R' 'fonts.R' 'girafe_options.R' 'default.R' 'dsvg.R' 'dsvg_view.R' 'element_interactive.R' 'facet_interactive.R' 'geom_abline_interactive.R' 'geom_path_interactive.R' 'geom_polygon_interactive.R' 'geom_rect_interactive.R' 'geom_bar_interactive.R' 'geom_bin_2d_interactive.R' 'geom_boxplot_interactive.R' 'geom_col_interactive.R' 'geom_contour_interactive.R' 'geom_count_interactive.R' 'geom_crossbar_interactive.R' 'geom_curve_interactive.R' 'geom_density_2d_interactive.R' 'geom_density_interactive.R' 'geom_dotplot_interactive.R' 'geom_errorbar_interactive.R' 'geom_errorbarh_interactive.R' 'geom_freqpoly_interactive.R' 'geom_hex_interactive.R' 'geom_histogram_interactive.R' 'geom_hline_interactive.R' 'geom_jitter_interactive.R' 'geom_label_interactive.R' 'geom_linerange_interactive.R' 'geom_map_interactive.R' 'geom_point_interactive.R' 'geom_pointrange_interactive.R' 'geom_quantile_interactive.R' 'geom_raster_interactive.R' 'geom_ribbon_interactive.R' 'geom_segment_interactive.R' 'geom_sf_interactive.R' 'geom_smooth_interactive.R' 'geom_spoke_interactive.R' 'geom_text_interactive.R' 'geom_text_repel_interactive.R' 'geom_tile_interactive.R' 'geom_violin_interactive.R' 'geom_vline_interactive.R' 'ggiraph.R' 'girafe.R' 'grob_interactive.R' 'guide_bins_interactive.R' 'guide_colourbar_interactive.R' 'guide_coloursteps_interactive.R' 'guide_interactive.R' 'guide_legend_interactive.R' 'interactive_circle_grob.R' 'interactive_curve_grob.R' 'interactive_path_grob.R' 'interactive_points_grob.R' 'interactive_polygon_grob.R' 'interactive_polyline_grob.R' 'interactive_raster_grob.R' 'interactive_rect_grob.R' 'interactive_roundrect_grob.R' 'interactive_segments_grob.R' 'interactive_text_grob.R' 'labeller_interactive.R' 'layer_interactive.R' 'scale_alpha_interactive.R' 'scale_brewer_interactive.R' 'scale_colour_interactive.R' 'scale_gradient_interactive.R' 'scale_interactive.R' 'scale_linetype_interactive.R' 'scale_manual_interactive.R' 'scale_shape_interactive.R' 'scale_size_interactive.R' 'scale_steps_interactive.R' 'scale_viridis_interactive.R' 'tracers.R'", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON><PERSON> [aut], <PERSON> [cph] (d3.js), <PERSON><PERSON><PERSON> [cph] (d3-lasso), <PERSON> [cph] (saveSvgAsPng js library), <PERSON> [cph] (TinyXML2), <PERSON> [cph] (Flatbush), <PERSON> [ctb] (hline and vline geoms)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "ggplot2": {"Package": "ggplot2", "Version": "3.5.2", "Source": "Repository", "Title": "Create Elegant Data Visualisations Using the Grammar of Graphics", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-5147-4711\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-7470-9261\")), person(\"<PERSON>\", \"Woo\", role = \"aut\", comment = c(ORCID = \"0000-0002-5125-4188\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-3385-7233\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-9415-4582\")), person(\"Teun\", \"van den <PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-9335-7468\")), person(\"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A system for 'declaratively' creating graphics, based on \"The Grammar of Graphics\". You provide the data, tell 'ggplot2' how to map variables to aesthetics, what graphical primitives to use, and it takes care of the details.", "License": "MIT + file LICENSE", "URL": "https://ggplot2.tidyverse.org, https://github.com/tidyverse/ggplot2", "BugReports": "https://github.com/tidyverse/ggplot2/issues", "Depends": ["R (>= 3.5)"], "Imports": ["cli", "glue", "grDevices", "grid", "gtable (>= 0.1.1)", "isoband", "lifecycle (> 1.0.1)", "MASS", "mgcv", "rlang (>= 1.1.0)", "scales (>= 1.3.0)", "stats", "tibble", "vctrs (>= 0.6.0)", "withr (>= 2.5.0)"], "Suggests": ["covr", "dplyr", "ggplot2movies", "hex<PERSON>", "Hmisc", "knitr", "map<PERSON><PERSON>j", "maps", "multcomp", "munsell", "nlme", "profvis", "quantreg", "ragg (>= 1.2.6)", "RColorBrewer", "rmarkdown", "rpart", "sf (>= 0.7-3)", "svglite (>= 2.1.2)", "testthat (>= 3.1.2)", "vdiffr (>= 1.0.6)", "xml2"], "Enhances": ["sp"], "VignetteBuilder": "knitr", "Config/Needs/website": "ggtext, tidyr, forcats, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.3.2", "Collate": "'ggproto.R' 'ggplot-global.R' 'aaa-.R' 'aes-colour-fill-alpha.R' 'aes-evaluation.R' 'aes-group-order.R' 'aes-linetype-size-shape.R' 'aes-position.R' 'compat-plyr.R' 'utilities.R' 'aes.R' 'utilities-checks.R' 'legend-draw.R' 'geom-.R' 'annotation-custom.R' 'annotation-logticks.R' 'geom-polygon.R' 'geom-map.R' 'annotation-map.R' 'geom-raster.R' 'annotation-raster.R' 'annotation.R' 'autolayer.R' 'autoplot.R' 'axis-secondary.R' 'backports.R' 'bench.R' 'bin.R' 'coord-.R' 'coord-cartesian-.R' 'coord-fixed.R' 'coord-flip.R' 'coord-map.R' 'coord-munch.R' 'coord-polar.R' 'coord-quickmap.R' 'coord-radial.R' 'coord-sf.R' 'coord-transform.R' 'data.R' 'docs_layer.R' 'facet-.R' 'facet-grid-.R' 'facet-null.R' 'facet-wrap.R' 'fortify-lm.R' 'fortify-map.R' 'fortify-multcomp.R' 'fortify-spatial.R' 'fortify.R' 'stat-.R' 'geom-abline.R' 'geom-rect.R' 'geom-bar.R' 'geom-bin2d.R' 'geom-blank.R' 'geom-boxplot.R' 'geom-col.R' 'geom-path.R' 'geom-contour.R' 'geom-count.R' 'geom-crossbar.R' 'geom-segment.R' 'geom-curve.R' 'geom-defaults.R' 'geom-ribbon.R' 'geom-density.R' 'geom-density2d.R' 'geom-dotplot.R' 'geom-errorbar.R' 'geom-errorbarh.R' 'geom-freqpoly.R' 'geom-function.R' 'geom-hex.R' 'geom-histogram.R' 'geom-hline.R' 'geom-jitter.R' 'geom-label.R' 'geom-linerange.R' 'geom-point.R' 'geom-pointrange.R' 'geom-quantile.R' 'geom-rug.R' 'geom-sf.R' 'geom-smooth.R' 'geom-spoke.R' 'geom-text.R' 'geom-tile.R' 'geom-violin.R' 'geom-vline.R' 'ggplot2-package.R' 'grob-absolute.R' 'grob-dotstack.R' 'grob-null.R' 'grouping.R' 'theme-elements.R' 'guide-.R' 'guide-axis.R' 'guide-axis-logticks.R' 'guide-axis-stack.R' 'guide-axis-theta.R' 'guide-legend.R' 'guide-bins.R' 'guide-colorbar.R' 'guide-colorsteps.R' 'guide-custom.R' 'layer.R' 'guide-none.R' 'guide-old.R' 'guides-.R' 'guides-grid.R' 'hexbin.R' 'import-standalone-obj-type.R' 'import-standalone-types-check.R' 'labeller.R' 'labels.R' 'layer-sf.R' 'layout.R' 'limits.R' 'margins.R' 'performance.R' 'plot-build.R' 'plot-construction.R' 'plot-last.R' 'plot.R' 'position-.R' 'position-collide.R' 'position-dodge.R' 'position-dodge2.R' 'position-identity.R' 'position-jitter.R' 'position-jitterdodge.R' 'position-nudge.R' 'position-stack.R' 'quick-plot.R' 'reshape-add-margins.R' 'save.R' 'scale-.R' 'scale-alpha.R' 'scale-binned.R' 'scale-brewer.R' 'scale-colour.R' 'scale-continuous.R' 'scale-date.R' 'scale-discrete-.R' 'scale-expansion.R' 'scale-gradient.R' 'scale-grey.R' 'scale-hue.R' 'scale-identity.R' 'scale-linetype.R' 'scale-linewidth.R' 'scale-manual.R' 'scale-shape.R' 'scale-size.R' 'scale-steps.R' 'scale-type.R' 'scale-view.R' 'scale-viridis.R' 'scales-.R' 'stat-align.R' 'stat-bin.R' 'stat-bin2d.R' 'stat-bindot.R' 'stat-binhex.R' 'stat-boxplot.R' 'stat-contour.R' 'stat-count.R' 'stat-density-2d.R' 'stat-density.R' 'stat-ecdf.R' 'stat-ellipse.R' 'stat-function.R' 'stat-identity.R' 'stat-qq-line.R' 'stat-qq.R' 'stat-quantilemethods.R' 'stat-sf-coordinates.R' 'stat-sf.R' 'stat-smooth-methods.R' 'stat-smooth.R' 'stat-sum.R' 'stat-summary-2d.R' 'stat-summary-bin.R' 'stat-summary-hex.R' 'stat-summary.R' 'stat-unique.R' 'stat-ydensity.R' 'summarise-plot.R' 'summary.R' 'theme.R' 'theme-defaults.R' 'theme-current.R' 'utilities-break.R' 'utilities-grid.R' 'utilities-help.R' 'utilities-matrix.R' 'utilities-patterns.R' 'utilities-resolution.R' 'utilities-tidy-eval.R' 'zxx.R' 'zzz.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut] (<https://orcid.org/0000-0002-1576-2126>), <PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-5147-4711>), <PERSON><PERSON><PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0002-7470-9261>), <PERSON> [aut] (<https://orcid.org/0000-0002-5125-4188>), <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-3385-7233>), <PERSON> [aut] (<https://orcid.org/0000-0002-9415-4582>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-9335-7468>), <PERSON><PERSON><PERSON>, <PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "glue": {"Package": "glue", "Version": "1.8.0", "Source": "Repository", "Title": "Interpreted String Literals", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "An implementation of interpreted string literals, inspired by Python's Literal String Interpolation <https://www.python.org/dev/peps/pep-0498/> and Docstrings <https://www.python.org/dev/peps/pep-0257/> and <PERSON>'s Triple-Quoted String Literals <https://docs.julialang.org/en/v1.3/manual/strings/#Triple-Quoted-String-Literals-1>.", "License": "MIT + file LICENSE", "URL": "https://glue.tidyverse.org/, https://github.com/tidyverse/glue", "BugReports": "https://github.com/tidyverse/glue/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["crayon", "DBI (>= 1.2.0)", "dplyr", "knitr", "magrit<PERSON>", "rlang", "rmarkdown", "RSQLite", "testthat (>= 3.2.0)", "vctrs (>= 0.3.0)", "waldo (>= 0.5.3)", "withr"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/Needs/website": "bench, forcats, ggbeeswarm, ggplot2, <PERSON><PERSON>utils, rprintf, tidyr, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-6983-2759>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "gtable": {"Package": "gtable", "Version": "0.3.6", "Source": "Repository", "Title": "Arrange 'Grobs' in Tables", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools to make it easier to work with \"tables\" of 'grobs'. The 'gtable' package defines a 'gtable' grob class that specifies a grid along with a list of grobs and their placement in the grid. Further the package makes it easy to manipulate and combine 'gtable' objects so that complex compositions can be built up sequentially.", "License": "MIT + file LICENSE", "URL": "https://gtable.r-lib.org, https://github.com/r-lib/gtable", "BugReports": "https://github.com/r-lib/gtable/issues", "Depends": ["R (>= 4.0)"], "Imports": ["cli", "glue", "grid", "lifecycle", "rlang (>= 1.1.0)", "stats"], "Suggests": ["covr", "ggplot2", "knitr", "profvis", "rmarkdown", "testthat (>= 3.0.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/usethis/last-upkeep": "2024-10-25", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "highr": {"Package": "highr", "Version": "0.11", "Source": "Repository", "Type": "Package", "Title": "Syntax Highlighting for R Source Code", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\") )", "Description": "Provides syntax highlighting for R source code. Currently it supports LaTeX and HTML output. Source code of other languages is supported via <PERSON>'s highlight package (<https://gitlab.com/saalen/highlight>).", "Depends": ["R (>= 3.3.0)"], "Imports": ["xfun (>= 0.18)"], "Suggests": ["knitr", "markdown", "testit"], "License": "GPL", "URL": "https://github.com/yihui/highr", "BugReports": "https://github.com/yihui/highr/issues", "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>), <PERSON><PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "htmltools": {"Package": "htmltools", "Version": "*******", "Source": "Repository", "Type": "Package", "Title": "Tools for HTML", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0001-9986-114X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools for HTML generation and output.", "License": "GPL (>= 2)", "URL": "https://github.com/rstudio/htmltools, https://rstudio.github.io/htmltools/", "BugReports": "https://github.com/rstudio/htmltools/issues", "Depends": ["R (>= 2.14.1)"], "Imports": ["base64enc", "digest", "fastmap (>= 1.1.0)", "grDevices", "rlang (>= 1.0.0)", "utils"], "Suggests": ["Cairo", "markdown", "ragg", "shiny", "testthat", "withr"], "Enhances": ["knitr"], "Config/Needs/check": "knitr", "Config/Needs/website": "rstudio/quillt, bench", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Collate": "'colors.R' 'fill.R' 'html_dependency.R' 'html_escape.R' 'html_print.R' 'htmltools-package.R' 'images.R' 'known_tags.R' 'selector.R' 'staticimports.R' 'tag_query.R' 'utils.R' 'tags.R' 'template.R'", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-9986-114X>), <PERSON> [aut] (<https://orcid.org/0000-0002-1576-2126>), <PERSON><PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "htmlwidgets": {"Package": "htmlwidgets", "Version": "1.6.4", "Source": "Repository", "Type": "Package", "Title": "HTML Widgets for R", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cph\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cph\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A framework for creating HTML widgets that render in various contexts including the R console, 'R Markdown' documents, and 'Shiny' web applications.", "License": "MIT + file LICENSE", "URL": "https://github.com/ramnathv/htmlwidgets", "BugReports": "https://github.com/ramnathv/htmlwidgets/issues", "Imports": ["grDevices", "htmltools (>= 0.5.7)", "jsonlite (>= 0.9.16)", "knitr (>= 1.8)", "rmarkdown", "yaml"], "Suggests": ["testthat"], "Enhances": ["shiny (>= 1.1)"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cph], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON><PERSON> [aut, cph], <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "httpuv": {"Package": "httpuv", "Version": "1.6.16", "Source": "Repository", "Type": "Package", "Title": "HTTP and WebSocket Server Library", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>si<PERSON>, P<PERSON>\", \"fnd\", role = \"cph\"), person(\"<PERSON>\", \"<PERSON><PERSON>da Bravo\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"cph\", comment = \"optional.hpp\"), person(\"libuv project contributors\", role = \"cph\", comment = \"libuv library, see src/libuv/AUTHORS file\"), person(\"Joyent, Inc. and other Node contributors\", role = \"cph\", comment = \"libuv library, see src/libuv/AUTHORS file; and http-parser library, see src/http-parser/AUTHORS file\"), person(\"<PERSON><PERSON>\", \"Provos\", role = \"cph\", comment = \"libuv subcomponent: tree.h\"), person(\"Internet Systems Consortium, Inc.\", role = \"cph\", comment = \"libuv subcomponent: inet_pton and inet_ntop, contained in src/libuv/src/inet.c\"), person(\"<PERSON>\", \"Chemeris\", role = \"cph\", comment = \"libuv subcomponent: stdint-msvc2008.h (from msinttypes)\"), person(\"Google, Inc.\", role = \"cph\", comment = \"libuv subcomponent: pthread-fixes.c\"), person(\"Sony Mobile Communcations AB\", role = \"cph\", comment = \"libuv subcomponent: pthread-fixes.c\"), person(\"Berkeley Software Design Inc.\", role = \"cph\", comment = \"libuv subcomponent: android-ifaddrs.h, android-ifaddrs.c\"), person(\"Kenneth\", \"MacKay\", role = \"cph\", comment = \"libuv subcomponent: android-ifaddrs.h, android-ifaddrs.c\"), person(\"Emergya (Cloud4all, FP7/2007-2013, grant agreement no 289016)\", role = \"cph\", comment = \"libuv subcomponent: android-ifaddrs.h, android-ifaddrs.c\"), person(\"Steve\", \"Reid\", role = \"aut\", comment = \"SHA-1 implementation\"), person(\"James\", \"Brown\", role = \"aut\", comment = \"SHA-1 implementation\"), person(\"Bob\", \"Trower\", role = \"aut\", comment = \"base64 implementation\"), person(\"Alexander\", \"Peslyak\", role = \"aut\", comment = \"MD5 implementation\"), person(\"Trantor Standard Systems\", role = \"cph\", comment = \"base64 implementation\"), person(\"Igor\", \"Sysoev\", role = \"cph\", comment = \"http-parser\") )", "Description": "Provides low-level socket and protocol support for handling HTTP and WebSocket requests directly from within R. It is primarily intended as a building block for other packages, rather than making it particularly easy to create complete web applications using httpuv alone.  httpuv is built on top of the libuv and http-parser C libraries, both of which were developed by Joyent, Inc. (See LICENSE file for libuv and http-parser license information.)", "License": "GPL (>= 2) | file LICENSE", "URL": "https://github.com/rstudio/httpuv", "BugReports": "https://github.com/rstudio/httpuv/issues", "Depends": ["R (>= 2.15.1)"], "Imports": ["later (>= 0.8.0)", "promises", "R6", "Rcpp (>= 1.0.7)", "utils"], "Suggests": ["callr", "curl", "jsonlite", "testthat", "websocket"], "LinkingTo": ["later", "Rcpp"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "SystemRequirements": "GNU make, zlib", "Collate": "'RcppExports.R' 'httpuv.R' 'random_port.R' 'server.R' 'staticServer.R' 'static_paths.R' 'utils.R'", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON><PERSON>, PBC fnd [cph], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [cph] (optional.hpp), libuv project contributors [cph] (libuv library, see src/libuv/AUTHORS file), Joyent, Inc. and other Node contributors [cph] (libuv library, see src/libuv/AUTHORS file; and http-parser library, see src/http-parser/AUTHORS file), <PERSON>els <PERSON> [cph] (libuv subcomponent: tree.h), Internet Systems Consortium, Inc. [cph] (libuv subcomponent: inet_pton and inet_ntop, contained in src/libuv/src/inet.c), <PERSON> [cph] (libuv subcomponent: stdint-msvc2008.h (from msinttypes)), Google, Inc. [cph] (libuv subcomponent: pthread-fixes.c), Sony Mobile Communcations AB [cph] (libuv subcomponent: pthread-fixes.c), Berkeley Software Design Inc. [cph] (libuv subcomponent: android-ifaddrs.h, android-ifaddrs.c), <PERSON> [cph] (libuv subcomponent: android-ifaddrs.h, android-ifaddrs.c), Emergya (Cloud4all, FP7/2007-2013, grant agreement no 289016) [cph] (libuv subcomponent: android-ifaddrs.h, android-ifaddrs.c), Steve Reid [aut] (SHA-1 implementation), James Brown [aut] (SHA-1 implementation), Bob Trower [aut] (base64 implementation), Alexander Peslyak [aut] (MD5 implementation), Trantor Standard Systems [cph] (base64 implementation), Igor Sysoev [cph] (http-parser)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "httr": {"Package": "httr", "Version": "1.4.7", "Source": "Repository", "Title": "Tools for Working with URLs and HTTP", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Useful tools for working with HTTP organised by HTTP verbs (GET(), POST(), etc). Configuration functions make it easy to control additional request components (authenticate(), add_headers() and so on).", "License": "MIT + file LICENSE", "URL": "https://httr.r-lib.org/, https://github.com/r-lib/httr", "BugReports": "https://github.com/r-lib/httr/issues", "Depends": ["R (>= 3.5)"], "Imports": ["curl (>= 5.0.2)", "jsonlite", "mime", "openssl (>= 0.8)", "R6"], "Suggests": ["covr", "httpuv", "jpeg", "knitr", "png", "readr", "rmarkdown", "testthat (>= 0.8.0)", "xml2"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON>, <PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "isoband": {"Package": "isoband", "Version": "0.2.7", "Source": "Repository", "Title": "Generate Isolines and Isobands from Regularly Spaced Elevation Grids", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"W<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(\"Original author\", ORCID = \"0000-0002-7470-9261\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-5147-4711\")) )", "Description": "A fast C++ implementation to generate contour lines (isolines) and contour polygons (isobands) from regularly spaced grids containing elevation data.", "License": "MIT + file LICENSE", "URL": "https://isoband.r-lib.org", "BugReports": "https://github.com/r-lib/isoband/issues", "Imports": ["grid", "utils"], "Suggests": ["covr", "ggplot2", "knitr", "magick", "microbenchmark", "rmarkdown", "sf", "testthat", "xml2"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "SystemRequirements": "C++11", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut] (Original author, <https://orcid.org/0000-0002-7470-9261>), <PERSON> [aut] (<https://orcid.org/0000-0002-5147-4711>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "jose": {"Package": "jose", "Version": "1.2.1", "Source": "Repository", "Type": "Package", "Title": "JavaScript Object Signing and Encryption", "Authors@R": "person(\"<PERSON><PERSON><PERSON>\", \"O<PERSON>\", role = c(\"aut\", \"cre\"),  email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\"))", "Description": "Read and write JSON Web Keys (JWK, rfc7517), generate and verify JSON Web Signatures (JWS, rfc7515) and encode/decode JSON Web Tokens (JWT, rfc7519) <https://datatracker.ietf.org/wg/jose/documents/>. These standards provide  modern signing and encryption formats that are natively supported by browsers via the JavaScript WebCryptoAPI <https://www.w3.org/TR/WebCryptoAPI/#jose>,  and used by services like OAuth 2.0, LetsEncrypt, and Github Apps.", "License": "MIT + file LICENSE", "URL": "https://r-lib.r-universe.dev/jose", "BugReports": "https://github.com/r-lib/jose/issues", "Depends": ["openssl (>= 1.2.1)"], "Imports": ["jsonlite"], "RoxygenNote": "7.1.2", "VignetteBuilder": "knitr", "Suggests": ["spelling", "testthat", "knitr", "rmarkdown"], "Encoding": "UTF-8", "Language": "en-US", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "jquerylib": {"Package": "j<PERSON><PERSON><PERSON>", "Version": "0.1.4", "Source": "Repository", "Title": "Obtain 'jQuery' as an HTML Dependency Object", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(family = \"RStudio\", role = \"cph\"), person(family = \"jQuery Foundation\", role = \"cph\", comment = \"jQuery library and jQuery UI library\"), person(family = \"jQuery contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery library; authors listed in inst/lib/jquery-AUTHORS.txt\") )", "Description": "Obtain any major version of 'jQuery' (<https://code.jquery.com/>) and use it in any webpage generated by 'htmltools' (e.g. 'shiny', 'htmlwidgets', and 'rmarkdown'). Most R users don't need to use this package directly, but other R packages (e.g. 'shiny', 'rmarkdown', etc.) depend on this package to avoid bundling redundant copies of 'jQuery'.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "Config/testthat/edition": "3", "RoxygenNote": "7.0.2", "Imports": ["htmltools"], "Suggests": ["testthat"], "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [aut], <PERSON>tudio [cph], jQuery Foundation [cph] (jQuery library and jQuery UI library), jQuery contributors [ctb, cph] (jQuery library; authors listed in inst/lib/jquery-AUTHORS.txt)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "jsonlite": {"Package": "jsonlite", "Version": "2.0.0", "Source": "Repository", "Title": "A Simple and Robust JSON Parser and Generator for R", "License": "MIT + file LICENSE", "Depends": ["methods"], "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"cph\", comment=\"author of bundled libyajl\"))", "URL": "https://jeroen.r-universe.dev/jsonlite https://arxiv.org/abs/1403.2805", "BugReports": "https://github.com/jeroen/jsonlite/issues", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "VignetteBuilder": "knitr, R.rsp", "Description": "A reasonably fast JSON parser and generator, optimized for statistical  data and the web. Offers simple, flexible tools for working with JSON in R, and is particularly powerful for building pipelines and interacting with a web API.  The implementation is based on the mapping described in the vignette (Ooms, 2014). In addition to converting JSON data from/to R objects, 'jsonlite' contains  functions to stream, validate, and prettify JSON data. The unit tests included  with the package verify that all edge cases are encoded and decoded consistently  for use with dynamic data in systems and applications.", "Suggests": ["httr", "vctrs", "testthat", "knitr", "rmarkdown", "R.rsp", "sf"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb], <PERSON> [cph] (author of bundled libyajl)", "Repository": "CRAN"}, "knitr": {"Package": "knitr", "Version": "1.50", "Source": "Repository", "Type": "Package", "Title": "A General-Purpose Package for Dynamic Report Generation in R", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"the CSS files under inst/themes/ were derived from the Highlight package http://www.andre-simon.de\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-8335-495X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>t\", \"<PERSON><PERSON>rov\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>vie<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>-<PERSON>\", role = \"ctb\"), person(\"David\", \"Robinson\", role = \"ctb\"), person(\"Doug\", \"Hemken\", role = \"ctb\"), person(\"Duncan\", \"Murdoch\", role = \"ctb\"), person(\"Elio\", \"Campitelli\", role = \"ctb\"), person(\"Ellis\", \"Hughes\", role = \"ctb\"), person(\"Emily\", \"Riederer\", role = \"ctb\"), person(\"Fabian\", \"Hirschmann\", role = \"ctb\"), person(\"Fitch\", \"Simeon\", role = \"ctb\"), person(\"Forest\", \"Fang\", role = \"ctb\"), person(c(\"Frank\", \"E\", \"Harrell\", \"Jr\"), role = \"ctb\", comment = \"the Sweavel package at inst/misc/Sweavel.sty\"), person(\"Garrick\", \"Aden-Buie\", role = \"ctb\"), person(\"Gregoire\", \"Detrez\", role = \"ctb\"), person(\"Hadley\", \"Wickham\", role = \"ctb\"), person(\"Hao\", \"Zhu\", role = \"ctb\"), person(\"Heewon\", \"Jeon\", role = \"ctb\"), person(\"Henrik\", \"Bengtsson\", role = \"ctb\"), person(\"Hiroaki\", \"Yutani\", role = \"ctb\"), person(\"Ian\", \"Lyttle\", role = \"ctb\"), person(\"Hodges\", \"Daniel\", role = \"ctb\"), person(\"Jacob\", \"Bien\", role = \"ctb\"), person(\"Jake\", \"Burkhead\", role = \"ctb\"), person(\"James\", \"Manton\", role = \"ctb\"), person(\"Jared\", \"Lander\", role = \"ctb\"), person(\"Jason\", \"Punyon\", role = \"ctb\"), person(\"Javier\", \"Luraschi\", role = \"ctb\"), person(\"Jeff\", \"Arnold\", role = \"ctb\"), person(\"Jenny\", \"Bryan\", role = \"ctb\"), person(\"Jeremy\", \"Ashkenas\", role = c(\"ctb\", \"cph\"), comment = \"the CSS file at inst/misc/docco-classic.css\"), person(\"Jeremy\", \"Stephens\", role = \"ctb\"), person(\"Jim\", \"Hester\", role = \"ctb\"), person(\"Joe\", \"Cheng\", role = \"ctb\"), person(\"Johannes\", \"Ranke\", role = \"ctb\"), person(\"John\", \"Honaker\", role = \"ctb\"), person(\"John\", \"Muschelli\", role = \"ctb\"), person(\"Jonathan\", \"Keane\", role = \"ctb\"), person(\"JJ\", \"Allaire\", role = \"ctb\"), person(\"Johan\", \"Toloe\", role = \"ctb\"), person(\"Jonathan\", \"Sidi\", role = \"ctb\"), person(\"Joseph\", \"Larmarange\", role = \"ctb\"), person(\"Julien\", \"Barnier\", role = \"ctb\"), person(\"Kaiyin\", \"Zhong\", role = \"ctb\"), person(\"Kamil\", \"Slowikowski\", role = \"ctb\"), person(\"Karl\", \"Forner\", role = \"ctb\"), person(c(\"Kevin\", \"K.\"), \"Smith\", role = \"ctb\"), person(\"Kirill\", \"Mueller\", role = \"ctb\"), person(\"Kohske\", \"Takahashi\", role = \"ctb\"), person(\"Lorenz\", \"Walthert\", role = \"ctb\"), person(\"Lucas\", \"Gallindo\", role = \"ctb\"), person(\"Marius\", \"Hofert\", role = \"ctb\"), person(\"Martin\", \"Modrák\", role = \"ctb\"), person(\"Michael\", \"Chirico\", role = \"ctb\"), person(\"Michael\", \"Friendly\", role = \"ctb\"), person(\"Michal\", \"Bojanowski\", role = \"ctb\"), person(\"Michel\", \"Kuhlmann\", role = \"ctb\"), person(\"Miller\", \"Patrick\", role = \"ctb\"), person(\"Nacho\", \"Caballero\", role = \"ctb\"), person(\"Nick\", \"Salkowski\", role = \"ctb\"), person(\"Niels Richard\", \"Hansen\", role = \"ctb\"), person(\"Noam\", \"Ross\", role = \"ctb\"), person(\"Obada\", \"Mahdi\", role = \"ctb\"), person(\"Pavel N.\", \"Krivitsky\", role = \"ctb\", comment=c(ORCID = \"0000-0002-9101-3362\")), person(\"Pedro\", \"Faria\", role = \"ctb\"), person(\"Qiang\", \"Li\", role = \"ctb\"), person(\"Ramnath\", \"Vaidyanathan\", role = \"ctb\"), person(\"Richard\", \"Cotton\", role = \"ctb\"), person(\"Robert\", \"Krzyzanowski\", role = \"ctb\"), person(\"Rodrigo\", \"Copetti\", role = \"ctb\"), person(\"Romain\", \"Francois\", role = \"ctb\"), person(\"Ruaridh\", \"Williamson\", role = \"ctb\"), person(\"Sagiru\", \"Mati\", role = \"ctb\", comment = c(ORCID = \"0000-0003-1413-3974\")), person(\"Scott\", \"Kostyshak\", role = \"ctb\"), person(\"Sebastian\", \"Meyer\", role = \"ctb\"), person(\"Sietse\", \"Brouwer\", role = \"ctb\"), person(c(\"Simon\", \"de\"), \"Bernard\", role = \"ctb\"), person(\"Sylvain\", \"Rousseau\", role = \"ctb\"), person(\"Taiyun\", \"Wei\", role = \"ctb\"), person(\"Thibaut\", \"Assus\", role = \"ctb\"), person(\"Thibaut\", \"Lamadon\", role = \"ctb\"), person(\"Thomas\", \"Leeper\", role = \"ctb\"), person(\"Tim\", \"Mastny\", role = \"ctb\"), person(\"Tom\", \"Torsney-Weir\", role = \"ctb\"), person(\"Trevor\", \"Davis\", role = \"ctb\"), person(\"Viktoras\", \"Veitas\", role = \"ctb\"), person(\"Weicheng\", \"Zhu\", role = \"ctb\"), person(\"Wush\", \"Wu\", role = \"ctb\"), person(\"Zachary\", \"Foster\", role = \"ctb\"), person(\"Zhian N.\", \"Kamvar\", role = \"ctb\", comment = c(ORCID = \"0000-0003-1458-7108\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides a general-purpose tool for dynamic report generation in R using Literate Programming techniques.", "Depends": ["R (>= 3.6.0)"], "Imports": ["evaluate (>= 0.15)", "highr (>= 0.11)", "methods", "tools", "xfun (>= 0.51)", "yaml (>= 2.1.19)"], "Suggests": ["bslib", "codetools", "DBI (>= 0.4-1)", "digest", "formatR", "gifski", "gridSVG", "htmlwidgets (>= 0.7)", "jpeg", "JuliaCall (>= 0.11.1)", "magick", "litedown", "markdown (>= 1.3)", "png", "ragg", "reticulate (>= 1.4)", "rgl (>= 0.95.1201)", "rlang", "rmarkdown", "sass", "showtext", "styler (>= 1.2.0)", "targets (>= 0.6.0)", "testit", "tibble", "tikzDevice (>= 0.10)", "tinytex (>= 0.56)", "webshot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "svglite"], "License": "GPL", "URL": "https://yihui.org/knitr/", "BugReports": "https://github.com/yihui/knitr/issues", "Encoding": "UTF-8", "VignetteBuilder": "litedown, knitr", "SystemRequirements": "Package vignettes based on R Markdown v2 or reStructuredText require Pandoc (http://pandoc.org). The function rst2pdf() requires rst2pdf (https://github.com/rst2pdf/rst2pdf).", "Collate": "'block.R' 'cache.R' 'citation.R' 'hooks-html.R' 'plot.R' 'utils.R' 'defaults.R' 'concordance.R' 'engine.R' 'highlight.R' 'themes.R' 'header.R' 'hooks-asciidoc.R' 'hooks-chunk.R' 'hooks-extra.R' 'hooks-latex.R' 'hooks-md.R' 'hooks-rst.R' 'hooks-textile.R' 'hooks.R' 'output.R' 'package.R' 'pandoc.R' 'params.R' 'parser.R' 'pattern.R' 'rocco.R' 'spin.R' 'table.R' 'template.R' 'utils-conversion.R' 'utils-rd2html.R' 'utils-string.R' 'utils-sweave.R' 'utils-upload.R' 'utils-vignettes.R' 'zzz.R'", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON><PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (the CSS files under inst/themes/ were derived from the Highlight package http://www.andre-simon.de), <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-8335-495X>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (the Sweavel package at inst/mi<PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>.s<PERSON>), <PERSON><PERSON><PERSON> <PERSON>-<PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON> <PERSON> [c<PERSON>b], <PERSON><PERSON> <PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON><PERSON><PERSON> [ctb], James Manton [ctb], Jared Lander [ctb], Jason Punyon [ctb], Javier Luraschi [ctb], Jeff Arnold [ctb], Jenny Bryan [ctb], Jeremy Ashkenas [ctb, cph] (the CSS file at inst/misc/docco-classic.css), Jeremy Stephens [ctb], Jim Hester [ctb], Joe Cheng [ctb], Johannes Ranke [ctb], John Honaker [ctb], John Muschelli [ctb], Jonathan Keane [ctb], JJ Allaire [ctb], Johan Toloe [ctb], Jonathan Sidi [ctb], Joseph Larmarange [ctb], Julien Barnier [ctb], Kaiyin Zhong [ctb], Kamil Slowikowski [ctb], Karl Forner [ctb], Kevin K. Smith [ctb], Kirill Mueller [ctb], Kohske Takahashi [ctb], Lorenz Walthert [ctb], Lucas Gallindo [ctb], Marius Hofert [ctb], Martin Modrák [ctb], Michael Chirico [ctb], Michael Friendly [ctb], Michal Bojanowski [ctb], Michel Kuhlmann [ctb], Miller Patrick [ctb], Nacho Caballero [ctb], Nick Salkowski [ctb], Niels Richard Hansen [ctb], Noam Ross [ctb], Obada Mahdi [ctb], Pavel N. Krivitsky [ctb] (<https://orcid.org/0000-0002-9101-3362>), Pedro Faria [ctb], Qiang Li [ctb], Ramnath Vaidyanathan [ctb], Richard Cotton [ctb], Robert Krzyzanowski [ctb], Rodrigo Copetti [ctb], Romain Francois [ctb], Ruaridh Williamson [ctb], Sagiru Mati [ctb] (<https://orcid.org/0000-0003-1413-3974>), Scott Kostyshak [ctb], Sebastian Meyer [ctb], Sietse Brouwer [ctb], Simon de Bernard [ctb], Sylvain Rousseau [ctb], Taiyun Wei [ctb], Thibaut Assus [ctb], Thibaut Lamadon [ctb], Thomas Leeper [ctb], Tim Mastny [ctb], Tom Torsney-Weir [ctb], Trevor Davis [ctb], Viktoras Veitas [ctb], Weicheng Zhu [ctb], Wush Wu [ctb], Zachary Foster [ctb], Zhian N. Kamvar [ctb] (<https://orcid.org/0000-0003-1458-7108>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "labeling": {"Package": "labeling", "Version": "0.4.3", "Source": "Repository", "Type": "Package", "Title": "Axis Labeling", "Date": "2023-08-29", "Author": "<PERSON>,", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Description": "Functions which provide a range of axis labeling algorithms.", "License": "MIT + file LICENSE | Unlimited", "Collate": "'labeling.R'", "NeedsCompilation": "no", "Imports": ["stats", "graphics"], "Repository": "CRAN"}, "later": {"Package": "later", "Version": "1.4.2", "Source": "Repository", "Type": "Package", "Title": "Utilities for Scheduling Functions to Execute Later with Event Loops", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-0750-061X\")), person(family = \"Posit Software, PBC\", role = \"cph\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"TinyCThread library, https://tinycthread.github.io/\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"TinyCThread library, https://tinycthread.github.io/\") )", "Description": "Executes arbitrary R or C functions some time after the current time, after the R execution stack has emptied. The functions are scheduled in an event loop.", "URL": "https://r-lib.github.io/later/, https://github.com/r-lib/later", "BugReports": "https://github.com/r-lib/later/issues", "License": "MIT + file LICENSE", "Imports": ["Rcpp (>= 0.12.9)", "rlang"], "LinkingTo": ["Rcpp"], "RoxygenNote": "7.3.2", "Suggests": ["knitr", "nanonext", "R6", "rmarkdown", "testthat (>= 2.1.0)"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0002-0750-061X>), Posit Software, PBC [cph], <PERSON> [ctb, cph] (TinyCThread library, https://tinycthread.github.io/), <PERSON> [ctb, cph] (TinyCThread library, https://tinycthread.github.io/)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "lattice": {"Package": "lattice", "Version": "0.22-7", "Source": "Repository", "Date": "2025-03-31", "Priority": "recommended", "Title": "Trellis Graphics for R", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>rka<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-4107-1553\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"documentation\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"miscellaneous improvements\"), person(\"<PERSON><PERSON><PERSON><PERSON> (<PERSON>)\", \"<PERSON>\", role = \"cph\", comment = \"filled contour code\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"Eng\", role = \"ctb\", comment = \"violin plot improvements\"), person(\"<PERSON>chi<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"modern colors\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"generics for larrows, lpolygon, lrect and lsegments\") )", "Description": "A powerful and elegant high-level data visualization system inspired by Trellis graphics, with an emphasis on multivariate data. Lattice is sufficient for typical graphics needs, and is also flexible enough to handle most nonstandard requirements. See ?Lattice for an introduction.", "Depends": ["R (>= 4.0.0)"], "Suggests": ["KernSmooth", "MASS", "latticeExtra", "colorspace"], "Imports": ["grid", "grDevices", "graphics", "stats", "utils"], "Enhances": ["chron", "zoo"], "LazyLoad": "yes", "LazyData": "yes", "License": "GPL (>= 2)", "URL": "https://lattice.r-forge.r-project.org/", "BugReports": "https://github.com/deepayan/lattice/issues", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-4107-1553>), <PERSON> [ctb], <PERSON> [ctb] (documentation), <PERSON> [ctb], <PERSON> [ctb] (miscellaneous improvements), <PERSON><PERSON><PERSON><PERSON> (<PERSON>) <PERSON> [cph] (filled contour code), <PERSON> [ctb], <PERSON> [ctb] (violin plot improvements), <PERSON><PERSON><PERSON> [ctb] (modern colors), <PERSON> [ctb] (generics for larrows, lpolygon, lrect and lsegments)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "lazyeval": {"Package": "lazyeval", "Version": "0.2.2", "Source": "Repository", "Title": "Lazy (Non-Standard) Evaluation", "Description": "An alternative approach to non-standard evaluation using formulas. Provides a full implementation of LISP style 'quasiquotation', making it easier to generate code with other code.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", ,\"<EMAIL>\", c(\"aut\", \"cre\")), person(\"<PERSON>tu<PERSON>\", role = \"cph\") )", "License": "GPL-3", "LazyData": "true", "Depends": ["R (>= 3.1.0)"], "Suggests": ["knitr", "rmarkdown (>= 0.2.65)", "testthat", "covr"], "VignetteBuilder": "knitr", "RoxygenNote": "6.1.1", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON> [cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "leaflet": {"Package": "leaflet", "Version": "2.2.2", "Source": "Repository", "Type": "Package", "Title": "Create Interactive Web Maps with the JavaScript 'Leaflet' Library", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0001-9986-114X\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>fonk<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Leaflet library\"), person(\"CloudMade\", role = \"cph\", comment = \"Leaflet library\"), person(\"Leaflet contributors\", role = \"ctb\", comment = \"Leaflet library\"), person(\"<PERSON>land\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-measure plugin\"), person(\"<PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Leaflet.Terminator plugin\"), person(\"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Leaflet.MagnifyingGlass plugin\"), person(\"Norkart AS\", role = c(\"ctb\", \"cph\"), comment = \"Leaflet.MiniMap plugin\"), person(\"L. Voogdt\", role = c(\"ctb\", \"cph\"), comment = \"Leaflet.awesome-markers plugin\"), person(\"Daniel Montague\", role = c(\"ctb\", \"cph\"), comment = \"Leaflet.EasyButton plugin\"), person(\"Kartena AB\", role = c(\"ctb\", \"cph\"), comment = \"Proj4Leaflet plugin\"), person(\"Robert Kajic\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-locationfilter plugin\"), person(\"Mapbox\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-omnivore plugin\"), person(\"Michael Bostock\", role = c(\"ctb\", \"cph\"), comment = \"topojson\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Create and customize interactive maps using the 'Leaflet' JavaScript library and the 'htmlwidgets' package. These maps can be used directly from the R console, from 'RStudio', in Shiny applications and R Markdown documents.", "License": "GPL-3", "URL": "https://rstudio.github.io/leaflet/, https://github.com/rstudio/leaflet", "BugReports": "https://github.com/rstudio/leaflet/issues", "Depends": ["R (>= 3.1.0)"], "Imports": ["crosstalk", "htmltools", "htmlwidgets (>= 1.5.4)", "j<PERSON><PERSON><PERSON>", "leaflet.providers (>= 2.0.0)", "magrit<PERSON>", "methods", "png", "raster (>= 3.6.3)", "RColorBrewer", "scales (>= 1.0.0)", "sp", "stats", "viridisLite", "xfun"], "Suggests": ["knitr", "maps", "purrr", "R6", "RJSONIO", "rmarkdown", "s2", "sf (>= 0.9-6)", "shiny", "terra", "testthat (>= 3.0.0)"], "Config/testthat/edition": "3", "Config/Needs/website": "dp<PERSON><PERSON>, geo<PERSON><PERSON><PERSON>, ncdf4, tidyverse/tidytemplate", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-9986-114X>), <PERSON><PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb, cph] (Leaflet library), CloudMade [cph] (Leaflet library), Leaflet contributors [ctb] (Leaflet library), <PERSON> [ctb, cph] (leaflet-measure plugin), <PERSON><PERSON> [ctb, cph] (Leaflet.Terminator plugin), <PERSON> [ctb, cph] (Leaflet.MagnifyingGlass plugin), Norkart AS [ctb, cph] (Leaflet.MiniMap plugin), <PERSON><PERSON> [ctb, cph] (Leaflet.awesome-markers plugin), <PERSON> [ctb, cph] (Leaflet.EasyButton plugin), <PERSON><PERSON><PERSON> [ctb, cph] (Proj4Leaflet plugin), <PERSON> [ctb, cph] (leaflet-locationfilter plugin), <PERSON><PERSON> [ctb, cph] (leaflet-omnivore plugin), <PERSON> [ctb, cph] (topojson), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "leaflet.extras": {"Package": "leaflet.extras", "Version": "2.0.1", "Source": "Repository", "Type": "Package", "Title": "Extra Functionality for 'leaflet' Package", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\")), person(\"<PERSON>yo<PERSON>\", \"<PERSON>\", email= \"<EMAIL>\", role = c(\"ctb\"), comment = \"Leaflet-search and Leaflet-GPS plugin integration\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", email= \"<EMAIL>\", role = c(\"ctb\"), comment = \"Fixes for Draw Options\"), person(\"<PERSON>\", \"Voge\", email= \"<EMAIL>\", role = c(\"ctb\"), comment = \"Enhancements for Draw Options\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\"), comment = \"<PERSON><PERSON><PERSON> addition\"), person(family = \"Mapbox\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-omnivore, csv2geo<PERSON>son, and to<PERSON><PERSON><PERSON><PERSON> libraries\"), person(\"<PERSON>\", \"<PERSON>hasler\", role = c(\"ctb\", \"cph\"), comment = \"Leaflet.Geodesic library\"), person(\"Dennis\", \"Wilhelm\", role = c(\"ctb\", \"cph\"), comment = \"Leaflet.StyleEditor library\"), person(\"Kirollos\", \"Risk\", role = c(\"ctb\", \"cph\"), comment = \"fuse.js library\"), person(\"Tim\", \"Wisniewski\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-choropleth library\"), person(family = \"Leaflet\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-draw library\"), person(\"Alexander\", \"Milevski\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-draw-drag library\"), person(\"John\", \"Firebaugh\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-fullscreen library\"), person(\"Stefano\", \"Cudini\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-gps library\"), person(\"Johannes\", \"Rudolph\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-hash library\"), person(\"Per\", \"Liedman\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-measure-path library\"), person(\"Pavel\", \"Shramov\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-plugins library\"), person(\"Filip\", \"Zavadil\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-pulse-icon library\"), person(\"Stefano\", \"Cudini\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-search library\"), person(family = \"CliffCloud\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-sleep library\"), person(family = \"Ursudio\", role = c(\"ctb\", \"cph\"), comment = \"leaflet-webgl-heatmap library\"), person(\"Maxime\", \"Hadjinlian\", role = c(\"ctb\", \"cph\"), comment = \"leaflet.BounceMarker library\"), person(\"Vladimir\", \"Agafonkin\", role = c(\"ctb\", \"cph\"), comment = \"leaflet.heat library\"), person(\"Iván Sánchez\", \"Ortega\", role = c(\"ctb\", \"cph\"), comment = \"leaflet.tilelayer.pouchdbcached library\"), person(\"Dale\", \"Harvey\", role = c(\"ctb\", \"cph\"), comment = \"pouchdb-browser library\"), person(\"Mike\", \"Bostock\", role = c(\"ctb\", \"cph\"), comment = \"topojson library\") )", "Description": "The 'leaflet' JavaScript library provides many plugins some of which are available in the core 'leaflet' package, but there are many more. It is not possible to support them all in the core 'leaflet' package. This package serves as an add-on to the 'leaflet' package by providing extra functionality via 'leaflet' plugins.", "License": "GPL-3 | file LICENSE", "Encoding": "UTF-8", "Depends": ["R (>= 3.1.0)", "leaflet (>= 2.0.0)"], "Imports": ["htmlwidgets", "htmltools", "stringr", "magrit<PERSON>"], "Suggests": ["jsonlite", "readr", "sf", "xfun", "testthat (>= 3.0.0)"], "URL": "https://github.com/trafficonese/leaflet.extras, https://trafficonese.github.io/leaflet.extras/", "BugReports": "https://github.com/trafficonese/leaflet.extras/issues", "RoxygenNote": "7.3.1", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON><PERSON> [ctb] (Leaflet-search and Leaflet-GPS plugin integration), <PERSON> [ctb] (Fixes for Draw Options), <PERSON> [ctb] (Enhancements for Draw Options), <PERSON> [ctb] (<PERSON><PERSON><PERSON> addition), Mapbox [ctb, cph] (leaflet-omnivore, csv2geojson, and toge<PERSON><PERSON>son libraries), <PERSON> [ctb, cph] (Leaflet.Geodesic library), <PERSON> [ctb, cph] (Leaflet.StyleEditor library), Kirollos Risk [ctb, cph] (fuse.js library), <PERSON> [ctb, cph] (leaflet-choropleth library), <PERSON><PERSON> [ctb, cph] (leaflet-draw library), <PERSON> [ctb, cph] (leaflet-draw-drag library), <PERSON> [ctb, cph] (leaflet-fullscreen library), <PERSON> [ctb, cph] (leaflet-gps library), <PERSON> [ctb, cph] (leaflet-hash library), <PERSON> [ctb, cph] (leaflet-measure-path library), <PERSON> [ctb, cph] (leaflet-plugins library), <PERSON>lip <PERSON>avadil [ctb, cph] (leaflet-pulse-icon library), Stefano Cudini [ctb, cph] (leaflet-search library), CliffCloud [ctb, cph] (leaflet-sleep library), Ursudio [ctb, cph] (leaflet-webgl-heatmap library), Maxime Hadjinlian [ctb, cph] (leaflet.BounceMarker library), Vladimir Agafonkin [ctb, cph] (leaflet.heat library), Iván Sánchez Ortega [ctb, cph] (leaflet.tilelayer.pouchdbcached library), Dale Harvey [ctb, cph] (pouchdb-browser library), Mike Bostock [ctb, cph] (topojson library)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "leaflet.providers": {"Package": "leaflet.providers", "Version": "2.0.0", "Source": "Repository", "Type": "Package", "Title": "Leaflet Providers", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"les<PERSON><PERSON><PERSON>@nyu.edu\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"ctb\", \"cre\"), comment = c(ORCID = \"0000-0001-9986-114X\")), person(\"Leaflet Providers contributors\", role = c(\"ctb\", \"cph\"), comment = \"Leaflet Providers plugin\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Contains third-party map tile provider information from 'Leaflet.js', <https://github.com/leaflet-extras/leaflet-providers>, to be used with the 'leaflet' R package. Additionally, 'leaflet.providers' enables users to retrieve up-to-date provider information between package updates.", "License": "BSD_2_clause + file LICENSE", "URL": "https://rstudio.github.io/leaflet.providers/, https://github.com/rstudio/leaflet.providers", "BugReports": "https://github.com/rstudio/leaflet.providers/issues", "Depends": ["R (>= 2.10)"], "Imports": ["htmltools"], "Suggests": ["jsonlite", "testthat (>= 3.0.0)", "V8"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3", "Collate": "'providers_data.R' 'get_current_providers.R' 'leaflet.providers-package.R' 'zzz.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON><PERSON> [ctb, cre] (<https://orcid.org/0000-0001-9986-114X>), Leaflet Providers contributors [ctb, cph] (Leaflet Providers plugin), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "lifecycle": {"Package": "lifecycle", "Version": "1.0.4", "Source": "Repository", "Title": "Manage the Life Cycle of your Package Functions", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Manage the life cycle of your exported functions with shared conventions, documentation badges, and user-friendly deprecation warnings.", "License": "MIT + file LICENSE", "URL": "https://lifecycle.r-lib.org/, https://github.com/r-lib/lifecycle", "BugReports": "https://github.com/r-lib/lifecycle/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli (>= 3.4.0)", "glue", "rlang (>= 1.1.0)"], "Suggests": ["covr", "crayon", "knitr", "lintr", "rmarkdown", "testthat (>= 3.0.1)", "tibble", "tidyverse", "tools", "vctrs", "withr"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate, usethis", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "lubridate": {"Package": "lubridate", "Version": "1.9.4", "Source": "Repository", "Type": "Package", "Title": "Make Dealing with Dates a Little Easier", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON> <PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\") )", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Description": "Functions to work with date-times and time-spans: fast and user friendly parsing of date-time data, extraction and updating of components of a date-time (years, months, days, hours, minutes, and seconds), algebraic manipulation on date-time and time-span objects. The 'lubridate' package has a consistent and memorable syntax that makes working with dates easy and fun.", "License": "GPL (>= 2)", "URL": "https://lubridate.tidyverse.org, https://github.com/tidyverse/lubridate", "BugReports": "https://github.com/tidyverse/lubridate/issues", "Depends": ["methods", "R (>= 3.2)"], "Imports": ["generics", "timechange (>= 0.3.0)"], "Suggests": ["covr", "knitr", "rmarkdown", "testthat (>= 2.1.0)", "vctrs (>= 0.6.5)"], "Enhances": ["chron", "data.table", "timeDate", "tis", "zoo"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.3", "SystemRequirements": "C++11, A system with zoneinfo data (e.g. /usr/share/zoneinfo). On Windows the zoneinfo included with R is used.", "Collate": "'Dates.r' 'POSIXt.r' 'util.r' 'parse.r' 'timespans.r' 'intervals.r' 'difftimes.r' 'durations.r' 'periods.r' 'accessors-date.R' 'accessors-day.r' 'accessors-dst.r' 'accessors-hour.r' 'accessors-minute.r' 'accessors-month.r' 'accessors-quarter.r' 'accessors-second.r' 'accessors-tz.r' 'accessors-week.r' 'accessors-year.r' 'am-pm.r' 'time-zones.r' 'numeric.r' 'coercion.r' 'constants.r' 'cyclic_encoding.r' 'data.r' 'decimal-dates.r' 'deprecated.r' 'format_ISO8601.r' 'guess.r' 'hidden.r' 'instants.r' 'leap-years.r' 'ops-addition.r' 'ops-compare.r' 'ops-division.r' 'ops-integer-division.r' 'ops-m+.r' 'ops-modulo.r' 'ops-multiplication.r' 'ops-subtraction.r' 'package.r' 'pretty.r' 'round.r' 'stamp.r' 'tzdir.R' 'update.r' 'vctrs.R' 'zzz.R'", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb]", "Repository": "CRAN"}, "magrittr": {"Package": "magrit<PERSON>", "Version": "2.0.3", "Source": "Repository", "Type": "Package", "Title": "A Forward-Pipe Operator for R", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cph\"), comment = \"Original author and creator of magrittr\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@rstudio.com\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"cre\"), person(\"RStudio\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides a mechanism for chaining commands with a new forward-pipe operator, %>%. This operator will forward a value, or the result of an expression, into the next function call/expression. There is flexible support for the type of right-hand side expressions. For more information, see package vignette.  To quote <PERSON>, \"Ceci n'est pas un pipe.\"", "License": "MIT + file LICENSE", "URL": "https://magrittr.tidyverse.org, https://github.com/tidyverse/magrittr", "BugReports": "https://github.com/tidyverse/magrittr/issues", "Depends": ["R (>= 3.4.0)"], "Suggests": ["covr", "knitr", "rlang", "rmarkdown", "testthat"], "VignetteBuilder": "knitr", "ByteCompile": "Yes", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cph] (Original author and creator of magrittr), <PERSON> [aut], <PERSON> [cre], <PERSON><PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "memoise": {"Package": "memoise", "Version": "2.0.1", "Source": "Repository", "Title": "'Memoisation' of Functions", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(given = \"Kirill\", family = \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"))", "Description": "Cache the results of a function so that when you call it again with the same arguments it returns the previously computed value.", "License": "MIT + file LICENSE", "URL": "https://memoise.r-lib.org, https://github.com/r-lib/memoise", "BugReports": "https://github.com/r-lib/memoise/issues", "Imports": ["rlang (>= 0.4.10)", "cachem"], "Suggests": ["digest", "aws.s3", "covr", "googleAuthR", "googleCloudStorageR", "httr", "testthat"], "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "mgcv": {"Package": "mgcv", "Version": "1.9-3", "Source": "Repository", "Authors@R": "person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\")", "Title": "Mixed GAM Computation Vehicle with Automatic Smoothness Estimation", "Description": "Generalized additive (mixed) models, some of their extensions and  other generalized ridge regression with multiple smoothing  parameter estimation by (Restricted) Marginal Likelihood,  Generalized Cross Validation and similar, or using iterated  nested Laplace approximation for fully Bayesian inference. See  <PERSON> (2017) <doi:10.1201/9781315370279> for an overview.  Includes a gam() function, a wide variety of smoothers, 'JAGS'  support and distributions beyond the exponential family.", "Priority": "recommended", "Depends": ["R (>= 3.6.0)", "nlme (>= 3.1-64)"], "Imports": ["methods", "stats", "graphics", "Matrix", "splines", "utils"], "Suggests": ["parallel", "survival", "MASS"], "LazyLoad": "yes", "ByteCompile": "yes", "License": "GPL (>= 2)", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "mime": {"Package": "mime", "Version": "0.13", "Source": "Repository", "Type": "Package", "Title": "Map Filenames to MIME Types", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\") )", "Description": "Guesses the MIME type from a filename extension using the data derived from /etc/mime.types in UNIX-type systems.", "Imports": ["tools"], "License": "GPL", "URL": "https://github.com/yihui/mime", "BugReports": "https://github.com/yihui/mime/issues", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "nlme": {"Package": "nlme", "Version": "3.1-168", "Source": "Repository", "Date": "2025-03-31", "Priority": "recommended", "Title": "Linear and Nonlinear Mixed Effects Models", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", comment = \"S version\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = \"up to 2007\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"up to 2002\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"up to 2005\"), person(\"EISPACK authors\", role = \"ctb\", comment = \"src/rs.f\"), person(\"<PERSON><PERSON>\", \"Heisterka<PERSON>\", role = \"ctb\", comment = \"Author fixed sigma\"), person(\"<PERSON>\", \"<PERSON>\",role = \"ctb\", comment = \"Programmer fixed sigma\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"varConstProp()\"), person(\"R Core Team\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ROR = \"02zz1nj61\")))", "Contact": "see 'MailingList'", "Description": "Fit and compare Gaussian linear and nonlinear mixed-effects models.", "Depends": ["R (>= 3.6.0)"], "Imports": ["graphics", "stats", "utils", "lattice"], "Suggests": ["MASS", "SASmixed"], "LazyData": "yes", "Encoding": "UTF-8", "License": "GPL (>= 2)", "BugReports": "https://bugs.r-project.org", "MailingList": "<EMAIL>", "URL": "https://svn.r-project.org/R-packages/trunk/nlme/", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (S version), <PERSON> [aut] (up to 2007), <PERSON><PERSON> [ctb] (up to 2002), <PERSON><PERSON><PERSON> [ctb] (up to 2005), EISPACK authors [ctb] (src/rs.f), <PERSON><PERSON> [ctb] (Author fixed sigma), <PERSON> [ctb] (Programmer fixed sigma), <PERSON> [ctb] (varConstProp()), R Core Team [aut, cre] (02zz1nj61)", "Maintainer": "R Core Team <<EMAIL>>", "Repository": "CRAN"}, "openssl": {"Package": "openssl", "Version": "2.3.3", "Source": "Repository", "Type": "Package", "Title": "Toolkit for Encryption, Signatures and Certificates Based on OpenSSL", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"))", "Description": "Bindings to OpenSSL libssl and libcrypto, plus custom SSH key parsers. Supports RSA, DSA and EC curves P-256, P-384, P-521, and curve25519. Cryptographic signatures can either be created and verified manually or via x509 certificates.  AES can be used in cbc, ctr or gcm mode for symmetric encryption; RSA for asymmetric (public key) encryption or EC for <PERSON><PERSON><PERSON>. High-level envelope functions  combine RSA and AES for encrypting arbitrary sized data. Other utilities include key generators, hash functions (md5, sha1, sha256, etc), base64 encoder, a secure random number generator, and 'bignum' math methods for manually performing crypto  calculations on large multibyte integers.", "License": "MIT + file LICENSE", "URL": "https://jeroen.r-universe.dev/openssl", "BugReports": "https://github.com/jeroen/openssl/issues", "SystemRequirements": "OpenSSL >= 1.0.2", "VignetteBuilder": "knitr", "Imports": ["askpass"], "Suggests": ["curl", "testthat (>= 2.1.0)", "digest", "knitr", "rmarkdown", "jsonlite", "jose", "sodium"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "openxlsx": {"Package": "openxlsx", "Version": "4.2.8", "Source": "Repository", "Type": "Package", "Title": "Read, Write and Edit xlsx Files", "Date": "2025-01-25", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\", email = \"philip<PERSON>@schauberger.co.at\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\", email = \"<PERSON>.<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"Sturm\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cre\"), email = \"<EMAIL>\"), person(given = \"<PERSON> Mark\", family = \"Barbone\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-9788-3628\")), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Reinhold\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"))", "Description": "Simplifies the creation of Excel .xlsx files by providing a high level interface to writing, styling and editing worksheets. Through the use of 'Rcpp', read/write times are comparable to the 'xlsx' and 'XLConnect' packages with the added benefit of removing the dependency on Java.", "License": "MIT + file LICENSE", "URL": "https://ycphs.github.io/openxlsx/index.html, https://github.com/ycphs/openxlsx", "BugReports": "https://github.com/ycphs/openxlsx/issues", "Depends": ["R (>= 3.3.0)"], "Imports": ["grDevices", "methods", "Rcpp", "stats", "stringi", "utils", "zip"], "Suggests": ["curl", "formula.tools", "knitr", "rmarkdown", "testthat"], "LinkingTo": ["Rcpp"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.3.2", "Collate": "'CommentClass.R' 'HyperlinkClass.R' 'RcppExports.R' 'class_definitions.R' 'StyleClass.R' 'WorkbookClass.R' 'asserts.R' 'baseXML.R' 'borderFunctions.R' 'build_workbook.R' 'chartsheet_class.R' 'conditional_formatting.R' 'data-fontSizeLookupTables.R' 'helperFunctions.R' 'loadWorkbook.R' 'onUnload.R' 'openXL.R' 'openxlsx-package.R' 'openxlsx.R' 'openxlsxCoerce.R' 'readWorkbook.R' 'setWindowSize.R' 'sheet_data_class.R' 'utils.R' 'workbook_column_widths.R' 'workbook_read_workbook.R' 'workbook_write_data.R' 'worksheet_class.R' 'wrappers.R' 'writeData.R' 'writeDataTable.R' 'writexlsx.R' 'zzz.R'", "LazyData": "true", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb, cre], <PERSON> [ctb] (<https://orcid.org/0000-0001-9788-3628>), <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "<PERSON> <jan.garbus<PERSON><PERSON>@ruhr-uni-bochum.de>", "Repository": "CRAN"}, "packrat": {"Package": "packrat", "Version": "0.9.3", "Source": "Repository", "Type": "Package", "Title": "A Dependency Management System for Projects and their R Package Dependencies", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Manage the R packages your project depends on in an isolated, portable, and reproducible way.", "License": "GPL-2", "URL": "https://github.com/rstudio/packrat", "BugReports": "https://github.com/rstudio/packrat/issues", "Depends": ["R (>= 3.0.0)"], "Imports": ["tools", "utils"], "Suggests": ["devtools", "httr", "knitr", "mockery", "rmarkdown", "testthat (>= 3.0.0)", "webfakes", "withr"], "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "pillar": {"Package": "pillar", "Version": "1.10.2", "Source": "Repository", "Title": "Coloured Formatting for Columns", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"Wickham\", role = \"aut\"), person(given = \"RStudio\", role = \"cph\"))", "Description": "Provides 'pillar' and 'colonnade' generics designed for formatting columns of data using the full range of colours provided by modern terminals.", "License": "MIT + file LICENSE", "URL": "https://pillar.r-lib.org/, https://github.com/r-lib/pillar", "BugReports": "https://github.com/r-lib/pillar/issues", "Imports": ["cli (>= 2.3.0)", "glue", "lifecycle", "rlang (>= 1.0.2)", "utf8 (>= 1.1.0)", "utils", "vctrs (>= 0.5.0)"], "Suggests": ["bit64", "DBI", "debugme", "DiagrammeR", "dplyr", "formattable", "ggplot2", "knitr", "lubridate", "nanotime", "nycflights13", "palmerpenguins", "rmarkdown", "scales", "stringi", "survival", "testthat (>= 3.1.1)", "tibble", "units (>= 0.7.2)", "vdiffr", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "format_multi_fuzz, format_multi_fuzz_2, format_multi, ctl_colonnade, ctl_colonnade_1, ctl_colonnade_2", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "true", "Config/gha/extra-packages": "units=?ignore-before-r=4.3.0", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut], <PERSON>tudio [cph]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "pkgconfig": {"Package": "pkgconfig", "Version": "2.0.3", "Source": "Repository", "Title": "Private Configuration for 'R' Packages", "Author": "<PERSON><PERSON><PERSON>", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Set configuration options on a per-package basis. Options set by a given package only apply to that package, other packages are unaffected.", "License": "MIT + file LICENSE", "LazyData": "true", "Imports": ["utils"], "Suggests": ["covr", "testthat", "disposables (>= 1.0.3)"], "URL": "https://github.com/r-lib/pkgconfig#readme", "BugReports": "https://github.com/r-lib/pkgconfig/issues", "Encoding": "UTF-8", "NeedsCompilation": "no", "Repository": "CRAN"}, "plotly": {"Package": "plotly", "Version": "4.11.0", "Source": "Repository", "Title": "Create Interactive Web Graphics via 'plotly.js'", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"Hocking\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1994-3581\")), person(\"<PERSON>\", \"Despouy\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"Plotly Technologies Inc.\", role = \"cph\"))", "License": "MIT + file LICENSE", "Description": "Create interactive web graphics from 'ggplot2' graphs and/or a custom interface to the (MIT-licensed) JavaScript library 'plotly.js' inspired by the grammar of graphics.", "URL": "https://plotly-r.com, https://github.com/plotly/plotly.R, https://plotly.com/r/", "BugReports": "https://github.com/plotly/plotly.R/issues", "Depends": ["R (>= 3.2.0)", "ggplot2 (>= 3.0.0)"], "Imports": ["tools", "scales", "httr (>= 1.3.0)", "jsonlite (>= 1.6)", "magrit<PERSON>", "digest", "viridisLite", "base64enc", "htmltools (>= 0.3.6)", "htmlwidgets (>= 1.5.2.9001)", "tidyr (>= 1.0.0)", "RColorBrewer", "dplyr", "vctrs", "tibble", "lazyeval (>= 0.2.0)", "rlang (>= 1.0.0)", "crosstalk", "purrr", "data.table", "promises"], "Suggests": ["MASS", "maps", "hex<PERSON>", "ggthemes", "GGally", "ggalluvial", "testthat", "knitr", "shiny (>= 1.1.0)", "shinytest2", "curl", "rmarkdown", "Cairo", "broom", "webshot", "listviewer", "dendextend", "sf", "png", "IRdisplay", "processx", "plotlyGeoAssets", "forcats", "withr", "palmerpenguins", "rversions", "reticulate", "rsvg", "ggridges"], "LazyData": "true", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "Config/Needs/check": "tidyverse/ggplot2, ggobi/GGally, rcmdcheck, devtools, reshape2, s2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-4958-2844>), <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-1994-3581>), <PERSON> [aut], <PERSON><PERSON> [ctb] (ORCID: <https://orcid.org/0000-0002-5329-5987>), Plotly Technologies Inc. [cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "png": {"Package": "png", "Version": "0.1-8", "Source": "Repository", "Title": "Read and write PNG images", "Author": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Maintainer": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Depends": ["R (>= 2.9.0)"], "Description": "This package provides an easy and simple way to read, write and display bitmap images stored in the PNG format. It can read and write both files and in-memory raw vectors.", "License": "GPL-2 | GPL-3", "SystemRequirements": "libpng", "URL": "http://www.rforge.net/png/", "NeedsCompilation": "yes", "Repository": "CRAN"}, "promises": {"Package": "promises", "Version": "1.3.3", "Source": "Repository", "Type": "Package", "Title": "Abstractions for Promise-Based Asynchronous Programming", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Provides fundamental abstractions for doing asynchronous programming in R using promises. Asynchronous programming is useful for allowing a single R process to orchestrate multiple tasks in the background while also attending to something else. Semantics are similar to 'JavaScript' promises, but with a syntax that is idiomatic R.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/promises/, https://github.com/rstudio/promises", "BugReports": "https://github.com/rstudio/promises/issues", "Imports": ["fastmap (>= 1.1.0)", "later", "magrittr (>= 1.5)", "R6", "Rcpp", "rlang", "stats"], "Suggests": ["future (>= 1.21.0)", "knitr", "purrr", "rmarkdown", "spelling", "testthat (>= 3.0.0)", "ve<PERSON><PERSON>"], "LinkingTo": ["later", "Rcpp"], "VignetteBuilder": "knitr", "Config/Needs/website": "rsconnect, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/usethis/last-upkeep": "2025-05-27", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "purrr": {"Package": "purrr", "Version": "1.0.4", "Source": "Repository", "Title": "Functional Programming Tools", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "A complete and consistent functional programming toolkit for R.", "License": "MIT + file LICENSE", "URL": "https://purrr.tidyverse.org/, https://github.com/tidyverse/purrr", "BugReports": "https://github.com/tidyverse/purrr/issues", "Depends": ["R (>= 4.0)"], "Imports": ["cli (>= 3.6.1)", "lifecycle (>= 1.0.3)", "magrittr (>= 1.5.0)", "rlang (>= 1.1.1)", "vctrs (>= 0.6.3)"], "Suggests": ["covr", "dplyr (>= 0.7.8)", "httr", "knitr", "lubridate", "rmarkdown", "testthat (>= 3.0.0)", "tibble", "tidyselect"], "LinkingTo": ["cli"], "VignetteBuilder": "knitr", "Biarch": "true", "Config/build/compilation-database": "true", "Config/Needs/website": "tidyverse/tidytemplate, tidyr", "Config/testthat/edition": "3", "Config/testthat/parallel": "TRUE", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut], Posit Software, PBC [cph, fnd] (03wc8by49)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rappdirs": {"Package": "rapp<PERSON>s", "Version": "0.3.3", "Source": "Repository", "Type": "Package", "Title": "Application Directories: Determine Where to Save Data, Caches, and Logs", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = c(\"trl\", \"cre\", \"cph\"), email = \"<EMAIL>\"), person(given = \"RStudio\", role = \"cph\"), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\"), person(given = \"ActiveState\", role = \"cph\", comment = \"R/appdir.r, R/cache.r, R/data.r, R/log.r translated from appdirs\"), person(given = \"<PERSON>\", family = \"Petrisor\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"trl\", \"aut\")), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"ctb\"))", "Description": "An easy way to determine which directories on the users computer you should use to save data, caches and logs. A port of Python's 'Appdirs' (<https://github.com/ActiveState/appdirs>) to R.", "License": "MIT + file LICENSE", "URL": "https://rappdirs.r-lib.org, https://github.com/r-lib/rappdirs", "BugReports": "https://github.com/r-lib/rappdirs/issues", "Depends": ["R (>= 3.2)"], "Suggests": ["roxygen2", "testthat (>= 3.0.0)", "covr", "withr"], "Copyright": "Original python appdirs module copyright (c) 2010 ActiveState Software Inc. R port copyright Hadley <PERSON>, RStudio. See file LICENSE for details.", "Encoding": "UTF-8", "RoxygenNote": "7.1.1", "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON> [trl, cre, cph], <PERSON><PERSON><PERSON> [cph], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut], ActiveState [cph] (R/appdir.r, R/cache.r, R/data.r, R/log.r translated from appdirs), <PERSON> [ctb], <PERSON> [trl, aut], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "raster": {"Package": "raster", "Version": "3.6-32", "Source": "Repository", "Type": "Package", "Title": "Geographic Data Analysis and Modeling", "Date": "2025-03-27", "Imports": ["Rcpp", "methods", "terra (>= 1.8-5)"], "LinkingTo": ["Rcpp"], "Depends": ["sp (>= 1.4-5)", "R (>= 3.5.0)"], "Suggests": ["ncdf4", "igraph", "tcltk", "parallel", "rasterVis", "MASS", "sf", "tinytest", "gstat", "fields", "exactextractr"], "Description": "Reading, writing, manipulating, analyzing and modeling of spatial data. This package has been superseded by the \"terra\" package <https://CRAN.R-project.org/package=terra>.", "License": "GPL (>= 3)", "URL": "https://rspatial.org/raster", "BugReports": "https://github.com/rspatial/raster/issues/", "Authors@R": "c( person(\"<PERSON>\", \"Hij<PERSON>\", role = c(\"cre\", \"aut\"),   email = \"<EMAIL>\",  comment = c(ORCID = \"0000-0001-5872-2872\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"Mo<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>ngee\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON> for <PERSON> <PERSON> <PERSON>eo<PERSON>s\", role=\"cph\"), person(\"<PERSON>\", \"<PERSON>rney\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>sher\", role = \"ctb\"), person(\"<PERSON>k\", \"Naimi\", role = \"ctb\"),\t person(\"Jakub\", \"Nowosad\", role = \"ctb\"), person(\"Edzer\", \"Pebesma\", role = \"ctb\"), person(\"Oscar\", \"Perpinan Lamigueiro\", role = \"ctb\"), person(\"Etienne B.\", \"Racine\", role = \"ctb\"), person(\"Barry\", \"Rowlingson\", role = \"ctb\"), person(\"Ashton\", \"Shortridge\", role = \"ctb\"), person(\"Bill\", \"Venables\", role = \"ctb\"), person(\"Rafael\", \"Wueest\", role = \"ctb\") )", "NeedsCompilation": "yes", "Author": "<PERSON> [cre, aut] (<https://orcid.org/0000-0001-5872-2872>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], Institute for Mathematics Applied Geosciences [cph], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "renv": {"Package": "renv", "Version": "1.1.4", "Source": "Repository", "Type": "Package", "Title": "Project Environments", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-2880-7407\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A dependency management toolkit for R. Using 'renv', you can create and manage project-local R libraries, save the state of these libraries to a 'lockfile', and later restore your library as required. Together, these tools can help make your projects more isolated, portable, and reproducible.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/renv/, https://github.com/rstudio/renv", "BugReports": "https://github.com/rstudio/renv/issues", "Imports": ["utils"], "Suggests": ["BiocManager", "cli", "compiler", "covr", "cpp11", "devtools", "gitcreds", "jsonlite", "jsonvalidate", "knitr", "miniUI", "modules", "packrat", "pak", "R6", "remotes", "reticulate", "rmarkdown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiny", "testthat", "uuid", "waldo", "yaml", "webfakes"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "bioconductor,python,install,restore,snapshot,retrieve,remotes", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-2880-7407>), <PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rlang": {"Package": "rlang", "Version": "1.1.6", "Source": "Repository", "Title": "Functions for Base Types and Core R and 'Tidyverse' Features", "Description": "A toolbox for working with base types, core R features like the condition system, and core 'Tidyverse' features like tidy evaluation.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", ,\"<EMAIL>\", c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", ,\"<EMAIL>\", \"aut\"), person(given = \"mikefc\", email = \"<EMAIL>\", role = \"cph\", comment = \"Hash implementation based on <PERSON>'s xxhashlite\"), person(given = \"Yann\", family = \"Collet\", role = \"cph\", comment = \"Author of the embedded xxHash library\"), person(given = \"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "License": "MIT + file LICENSE", "ByteCompile": "true", "Biarch": "true", "Depends": ["R (>= 3.5.0)"], "Imports": ["utils"], "Suggests": ["cli (>= 3.1.0)", "covr", "crayon", "desc", "fs", "glue", "knitr", "magrit<PERSON>", "methods", "pillar", "pkgload", "rmarkdown", "stats", "testthat (>= 3.2.0)", "tibble", "usethis", "vctrs (>= 0.2.3)", "withr"], "Enhances": ["winch"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "URL": "https://rlang.r-lib.org, https://github.com/r-lib/rlang", "BugReports": "https://github.com/r-lib/rlang/issues", "Config/build/compilation-database": "true", "Config/testthat/edition": "3", "Config/Needs/website": "dplyr, tidyverse/tidytemplate", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], mikef<PERSON> [cph] (Hash implementation based on <PERSON>'s xxhashlite), <PERSON><PERSON> [cph] (Author of the embedded xxHash library), Posit, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rmarkdown": {"Package": "rmarkdown", "Version": "2.29", "Source": "Repository", "Type": "Package", "Title": "Dynamic Documents for R", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-4474-2498\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-3925-190X\")), person(\"Andrew\", \"Dunning\", role = \"ctb\", comment = c(ORCID = \"0000-0003-0464-5036\")), person(\"Atsushi\", \"Yasumoto\", role = c(\"ctb\", \"cph\"), comment = c(ORCID = \"0000-0002-8335-495X\", cph = \"Number sections Lua filter\")), person(\"Barret\", \"Schloerke\", role = \"ctb\"), person(\"Carson\", \"Sievert\", role = \"ctb\", comment = c(ORCID = \"0000-0002-4958-2844\")),  person(\"Devon\", \"Ryan\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-8549-0971\")), person(\"Frederik\", \"Aust\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-4900-788X\")), person(\"Jeff\", \"Allen\", , \"<EMAIL>\", role = \"ctb\"),  person(\"JooYoung\", \"Seo\", role = \"ctb\", comment = c(ORCID = \"0000-0002-4064-6012\")), person(\"Malcolm\", \"Barrett\", role = \"ctb\"), person(\"Rob\", \"Hyndman\", , \"<EMAIL>\", role = \"ctb\"), person(\"Romain\", \"Lesur\", role = \"ctb\"), person(\"Roy\", \"Storey\", role = \"ctb\"), person(\"Ruben\", \"Arslan\", , \"<EMAIL>\", role = \"ctb\"), person(\"Sergio\", \"Oller\", role = \"ctb\"), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(, \"jQuery UI contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery UI library; authors listed in inst/rmd/h/jqueryui/AUTHORS.txt\"), person(\"Mark\", \"Otto\", role = \"ctb\", comment = \"Bootstrap library\"), person(\"Jacob\", \"Thornton\", role = \"ctb\", comment = \"Bootstrap library\"), person(, \"Bootstrap contributors\", role = \"ctb\", comment = \"Bootstrap library\"), person(, \"Twitter, Inc\", role = \"cph\", comment = \"Bootstrap library\"), person(\"Alexander\", \"Farkas\", role = c(\"ctb\", \"cph\"), comment = \"html5shiv library\"), person(\"Scott\", \"Jehl\", role = c(\"ctb\", \"cph\"), comment = \"Respond.js library\"), person(\"Ivan\", \"Sagalaev\", role = c(\"ctb\", \"cph\"), comment = \"highlight.js library\"), person(\"Greg\", \"Franko\", role = c(\"ctb\", \"cph\"), comment = \"tocify library\"), person(\"John\", \"MacFarlane\", role = c(\"ctb\", \"cph\"), comment = \"Pandoc templates\"), person(, \"Google, Inc.\", role = c(\"ctb\", \"cph\"), comment = \"ioslides library\"), person(\"Dave\", \"Raggett\", role = \"ctb\", comment = \"slidy library\"), person(, \"W3C\", role = \"cph\", comment = \"slidy library\"), person(\"Dave\", \"Gandy\", role = c(\"ctb\", \"cph\"), comment = \"Font-Awesome\"), person(\"Ben\", \"Sperry\", role = \"ctb\", comment = \"Ionicons\"), person(, \"Drifty\", role = \"cph\", comment = \"Ionicons\"), person(\"Aidan\", \"Lister\", role = c(\"ctb\", \"cph\"), comment = \"jQuery StickyTabs\"), person(\"Benct Philip\", \"Jonsson\", role = c(\"ctb\", \"cph\"), comment = \"pagebreak Lua filter\"), person(\"Albert\", \"Krewinkel\", role = c(\"ctb\", \"cph\"), comment = \"pagebreak Lua filter\") )", "Description": "Convert R Markdown documents into a variety of formats.", "License": "GPL-3", "URL": "https://github.com/rstudio/rmarkdown, https://pkgs.rstudio.com/rmarkdown/", "BugReports": "https://github.com/rstudio/rmarkdown/issues", "Depends": ["R (>= 3.0)"], "Imports": ["bslib (>= *******)", "evaluate (>= 0.13)", "fontawesome (>= 0.5.0)", "htmltools (>= 0.5.1)", "j<PERSON><PERSON><PERSON>", "jsonlite", "knitr (>= 1.43)", "methods", "tinytex (>= 0.31)", "tools", "utils", "xfun (>= 0.36)", "yaml (>= 2.1.19)"], "Suggests": ["digest", "dygraphs", "fs", "rsconnect", "downlit (>= 0.4.0)", "katex (>= 1.4.0)", "sass (>= 0.4.0)", "shiny (>= 1.6.0)", "testthat (>= 3.0.3)", "tibble", "vctrs", "cleanrmd", "withr (>= 2.4.2)", "xml2"], "VignetteBuilder": "knitr", "Config/Needs/website": "rstudio/quillt, pkgdown", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "SystemRequirements": "pandoc (>= 1.14) - http://pandoc.org", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut], <PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>), <PERSON> [aut] (<https://orcid.org/0000-0003-4474-2498>), <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0003-3925-190X>), <PERSON> [ctb] (<https://orcid.org/0000-0003-0464-5036>), <PERSON><PERSON><PERSON> [ctb, cph] (<https://orcid.org/0000-0002-8335-495X>, Number sections Lua filter), <PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [ctb] (<https://orcid.org/0000-0002-8549-0971>), <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0003-4900-788X>), <PERSON> [ctb], <PERSON><PERSON><PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-4064-6012>), <PERSON> <PERSON> [ctb], Rob Hyn<PERSON> [ctb], <PERSON>in <PERSON>ur [ctb], <PERSON> <PERSON>y [ctb], Ruben Arslan [ctb], <PERSON> Oller [ctb], Posit Software, P<PERSON> [cph, fnd], jQuery UI contributors [ctb, cph] (jQuery UI library; authors listed in inst/rmd/h/jqueryui/AUTHORS.txt), Mark Otto [ctb] (Bootstrap library), Jacob Thornton [ctb] (Bootstrap library), Bootstrap contributors [ctb] (Bootstrap library), Twitter, Inc [cph] (Bootstrap library), Alexander Farkas [ctb, cph] (html5shiv library), Scott Jehl [ctb, cph] (Respond.js library), Ivan Sagalaev [ctb, cph] (highlight.js library), Greg Franko [ctb, cph] (tocify library), John MacFarlane [ctb, cph] (Pandoc templates), Google, Inc. [ctb, cph] (ioslides library), Dave Raggett [ctb] (slidy library), W3C [cph] (slidy library), Dave Gandy [ctb, cph] (Font-Awesome), Ben Sperry [ctb] (Ionicons), Drifty [cph] (Ionicons), Aidan Lister [ctb, cph] (jQuery StickyTabs), Benct Philip Jonsson [ctb, cph] (pagebreak Lua filter), Albert Krewinkel [ctb, cph] (pagebreak Lua filter)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rsconnect": {"Package": "rsconnect", "Version": "1.4.2", "Source": "Repository", "Type": "Package", "Title": "Deploy Docs, Apps, and APIs to 'Posit Connect', 'shinyapps.io', and 'RPubs'", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>aire\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Programmatic deployment interface for 'RPubs', 'shinyapps.io', and 'Posit Connect'. Supported content types include R Markdown documents, Shiny applications, Plumber APIs, plots, and static web content.", "License": "GPL-2", "URL": "https://rstudio.github.io/rsconnect/, https://github.com/rstudio/rsconnect", "BugReports": "https://github.com/rstudio/rsconnect/issues", "Depends": ["R (>= 3.5.0)"], "Imports": ["cli", "curl", "digest", "jsonlite", "lifecycle", "openssl (>= 2.0.0)", "PKI", "packrat (>= 0.6)", "renv (>= 1.0.0)", "rlang (>= 1.0.0)", "rstudioa<PERSON> (>= 0.5)", "tools", "yaml (>= 2.1.5)", "RcppTOML", "jose", "utils"], "Suggests": ["Biobase", "BiocManager", "foreign", "knitr", "MASS", "plumber (>= 0.3.2)", "quarto", "<PERSON><PERSON><PERSON>", "reticulate", "rmarkdown (>= 1.1)", "shiny", "testthat (>= 3.1.9)", "webfakes", "withr"], "VignetteBuilder": "knitr, rmarkdown", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rstudioapi": {"Package": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Version": "0.17.1", "Source": "Repository", "Title": "Safely Access the RStudio API", "Description": "Access the RStudio API (if available) and provide informative error messages when it's not.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(family = \"RStudio\", role = \"cph\") )", "Maintainer": "<PERSON> <<EMAIL>>", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/rstudioapi/, https://github.com/rstudio/rstudioapi", "BugReports": "https://github.com/rstudio/rstudioapi/issues", "RoxygenNote": "7.3.2", "Suggests": ["testthat", "knitr", "rmarkdown", "clipr", "covr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [cph]", "Repository": "CRAN"}, "sass": {"Package": "sass", "Version": "0.4.10", "Source": "Repository", "Type": "Package", "Title": "Syntactically Awesome Style Sheets ('Sass')", "Description": "An 'SCSS' compiler, powered by the 'LibSass' library. With this, R developers can use variables, inheritance, and functions to generate dynamic style sheets. The package uses the 'Sass CSS' extension language, which is stable, powerful, and CSS compatible.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", \"aut\", comment = c(ORCID = \"0000-0003-3925-190X\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", \"aut\", comment = c(ORCID = \"0000-0001-9986-114X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", c(\"ctb\"), comment = c(ORCID = \"0000-0003-4474-2498\")), person(family = \"RStudio\", role = c(\"cph\", \"fnd\")), person(family = \"Sass Open Source Foundation\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"Mifsud\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"<PERSON>\", \"Catlin\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"<PERSON>\", \"Weizenbaum\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"Chris\", \"Eppstein\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"Adams\", \"Joseph\", role = c(\"ctb\", \"cph\"), comment = \"json.cpp\"), person(\"Trifunovic\", \"Nemanja\", role = c(\"ctb\", \"cph\"), comment = \"utf8.h\") )", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/sass/, https://github.com/rstudio/sass", "BugReports": "https://github.com/rstudio/sass/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "SystemRequirements": "GNU make", "Imports": ["fs (>= 1.2.4)", "rlang (>= 0.4.10)", "htmltools (>= 0.5.1)", "R6", "rapp<PERSON>s"], "Suggests": ["testthat", "knitr", "rmarkdown", "withr", "shiny", "curl"], "VignetteBuilder": "knitr", "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0003-3925-190X>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-9986-114X>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [ctb] (<https://orcid.org/0000-0003-4474-2498>), <PERSON>tu<PERSON> [cph, fnd], Sass Open Source Foundation [ctb, cph] (LibSass library), <PERSON><PERSON><PERSON> [ctb, cph] (LibSass library), <PERSON><PERSON><PERSON> [ctb, cph] (LibSass library), <PERSON> [ctb, cph] (LibSass library), <PERSON> [ctb, cph] (LibSass library), <PERSON> [ctb, cph] (LibSass library), <PERSON> [ctb, cph] (json.cpp), <PERSON><PERSON><PERSON><PERSON> [ctb, cph] (utf8.h)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "scales": {"Package": "scales", "Version": "1.4.0", "Source": "Repository", "Title": "Scale Functions for Visualization", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"cre\", \"aut\"), comment = c(ORCID = \"0000-0002-5147-4711\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Graphical scales map data to aesthetics, and provide methods for automatically determining breaks and labels for axes and legends.", "License": "MIT + file LICENSE", "URL": "https://scales.r-lib.org, https://github.com/r-lib/scales", "BugReports": "https://github.com/r-lib/scales/issues", "Depends": ["R (>= 4.1)"], "Imports": ["cli", "farver (>= 2.0.3)", "glue", "labeling", "lifecycle", "R6", "RColorBrewer", "rlang (>= 1.1.0)", "viridisLite"], "Suggests": ["bit64", "covr", "dichromat", "ggplot2", "hms (>= 0.5.0)", "stringi", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/usethis/last-upkeep": "2025-04-23", "Encoding": "UTF-8", "LazyLoad": "yes", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [cre, aut] (<https://orcid.org/0000-0002-5147-4711>), <PERSON> [aut], Posit Software, PBC [cph, fnd] (03wc8by49)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "shiny": {"Package": "shiny", "Version": "1.11.0", "Source": "Repository", "Type": "Package", "Title": "Web Application Framework for R", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON>aire\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-9986-114X\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(family = \"jQuery Foundation\", role = \"cph\", comment = \"jQuery library and jQuery UI library\"), person(family = \"jQuery contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery library; authors listed in inst/www/shared/jquery-AUTHORS.txt\"), person(family = \"jQuery UI contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery UI library; authors listed in inst/www/shared/jqueryui/AUTHORS.txt\"), person(\"Mark\", \"Otto\", role = \"ctb\", comment = \"Bootstrap library\"), person(\"Jacob\", \"Thornton\", role = \"ctb\", comment = \"Bootstrap library\"), person(family = \"Bootstrap contributors\", role = \"ctb\", comment = \"Bootstrap library\"), person(family = \"Twitter, Inc\", role = \"cph\", comment = \"Bootstrap library\"), person(\"Prem Nawaz\", \"Khan\", role = \"ctb\", comment = \"Bootstrap accessibility plugin\"), person(\"Victor\", \"Tsaran\", role = \"ctb\", comment = \"Bootstrap accessibility plugin\"), person(\"Dennis\", \"Lembree\", role = \"ctb\", comment = \"Bootstrap accessibility plugin\"), person(\"Srinivasu\", \"Chakravarthula\", role = \"ctb\", comment = \"Bootstrap accessibility plugin\"), person(\"Cathy\", \"O'Connor\", role = \"ctb\", comment = \"Bootstrap accessibility plugin\"), person(family = \"PayPal, Inc\", role = \"cph\", comment = \"Bootstrap accessibility plugin\"), person(\"Stefan\", \"Petre\", role = c(\"ctb\", \"cph\"), comment = \"Bootstrap-datepicker library\"), person(\"Andrew\", \"Rowls\", role = c(\"ctb\", \"cph\"), comment = \"Bootstrap-datepicker library\"), person(\"Brian\", \"Reavis\", role = c(\"ctb\", \"cph\"), comment = \"selectize.js library\"), person(\"Salmen\", \"Bejaoui\", role = c(\"ctb\", \"cph\"), comment = \"selectize-plugin-a11y library\"), person(\"Denis\", \"Ineshin\", role = c(\"ctb\", \"cph\"), comment = \"ion.rangeSlider library\"), person(\"Sami\", \"Samhuri\", role = c(\"ctb\", \"cph\"), comment = \"Javascript strftime library\"), person(family = \"SpryMedia Limited\", role = c(\"ctb\", \"cph\"), comment = \"DataTables library\"), person(\"John\", \"Fraser\", role = c(\"ctb\", \"cph\"), comment = \"showdown.js library\"), person(\"John\", \"Gruber\", role = c(\"ctb\", \"cph\"), comment = \"showdown.js library\"), person(\"Ivan\", \"Sagalaev\", role = c(\"ctb\", \"cph\"), comment = \"highlight.js library\"), person(given = \"R Core Team\", role = c(\"ctb\", \"cph\"), comment = \"tar implementation from R\") )", "Description": "Makes it incredibly easy to build interactive web applications with R. Automatic \"reactive\" binding between inputs and outputs and extensive prebuilt widgets make it possible to build beautiful, responsive, and powerful applications with minimal effort.", "License": "GPL-3 | file LICENSE", "Depends": ["R (>= 3.0.2)", "methods"], "Imports": ["utils", "grDevices", "httpuv (>= 1.5.2)", "mime (>= 0.3)", "jsonlite (>= 0.9.16)", "xtable", "fontawesome (>= 0.4.0)", "htmltools (>= 0.5.4)", "R6 (>= 2.0)", "sourcetools", "later (>= 1.0.0)", "promises (>= 1.3.2)", "tools", "cli", "rlang (>= 0.4.10)", "fastmap (>= 1.1.1)", "withr", "commonmark (>= 1.7)", "glue (>= 1.3.2)", "bslib (>= 0.6.0)", "cachem (>= 1.1.0)", "lifecycle (>= 0.2.0)"], "Suggests": ["coro (>= 1.1.0)", "datasets", "DT", "Cairo (>= 1.5-5)", "testthat (>= 3.2.1)", "knitr (>= 1.6)", "markdown", "rmarkdown", "ggplot2", "reactlog (>= 1.0.0)", "magrit<PERSON>", "yaml", "mirai", "future", "dygraphs", "ragg", "showtext", "sass", "watcher"], "URL": "https://shiny.posit.co/, https://github.com/rstudio/shiny", "BugReports": "https://github.com/rstudio/shiny/issues", "Collate": "'globals.R' 'app-state.R' 'app_template.R' 'bind-cache.R' 'bind-event.R' 'bookmark-state-local.R' 'bookmark-state.R' 'bootstrap-deprecated.R' 'bootstrap-layout.R' 'conditions.R' 'map.R' 'utils.R' 'bootstrap.R' 'busy-indicators-spinners.R' 'busy-indicators.R' 'cache-utils.R' 'deprecated.R' 'devmode.R' 'diagnose.R' 'extended-task.R' 'fileupload.R' 'graph.R' 'reactives.R' 'reactive-domains.R' 'history.R' 'hooks.R' 'html-deps.R' 'image-interact-opts.R' 'image-interact.R' 'imageutils.R' 'input-action.R' 'input-checkbox.R' 'input-checkboxgroup.R' 'input-date.R' 'input-daterange.R' 'input-file.R' 'input-numeric.R' 'input-password.R' 'input-radiobuttons.R' 'input-select.R' 'input-slider.R' 'input-submit.R' 'input-text.R' 'input-textarea.R' 'input-utils.R' 'insert-tab.R' 'insert-ui.R' 'jqueryui.R' 'knitr.R' 'middleware-shiny.R' 'middleware.R' 'timer.R' 'shiny.R' 'mock-session.R' 'modal.R' 'modules.R' 'notifications.R' 'priorityqueue.R' 'progress.R' 'react.R' 'reexports.R' 'render-cached-plot.R' 'render-plot.R' 'render-table.R' 'run-url.R' 'runapp.R' 'serializers.R' 'server-input-handlers.R' 'server-resource-paths.R' 'server.R' 'shiny-options.R' 'shiny-package.R' 'shinyapp.R' 'shinyui.R' 'shinywrappers.R' 'showcase.R' 'snapshot.R' 'staticimports.R' 'tar.R' 'test-export.R' 'test-server.R' 'test.R' 'update-input.R' 'utils-lang.R' 'version_bs_date_picker.R' 'version_ion_range_slider.R' 'version_jquery.R' 'version_jqueryui.R' 'version_selectize.R' 'version_strftime.R' 'viewer.R'", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "Config/testthat/edition": "3", "Config/Needs/check": "shinytest2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1576-2126>), <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-4958-2844>), <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0001-9986-114X>), <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd], jQuery Foundation [cph] (jQuery library and jQuery UI library), jQuery contributors [ctb, cph] (jQuery library; authors listed in inst/www/shared/jquery-AUTHORS.txt), jQuery UI contributors [ctb, cph] (jQuery UI library; authors listed in inst/www/shared/jqueryui/AUTHORS.txt), <PERSON> [ctb] (Bootstrap library), <PERSON> [ctb] (Bootstrap library), Bootstrap contributors [ctb] (Bootstrap library), <PERSON>, Inc [cph] (Bootstrap library), <PERSON><PERSON> [ctb] (Bootstrap accessibility plugin), <PERSON>an [ctb] (Bootstrap accessibility plugin), Dennis Lembree [ctb] (Bootstrap accessibility plugin), Srinivasu Chakravarthula [ctb] (Bootstrap accessibility plugin), Cathy O'Connor [ctb] (Bootstrap accessibility plugin), PayPal, Inc [cph] (Bootstrap accessibility plugin), Stefan Petre [ctb, cph] (Bootstrap-datepicker library), Andrew Rowls [ctb, cph] (Bootstrap-datepicker library), Brian Reavis [ctb, cph] (selectize.js library), Salmen Bejaoui [ctb, cph] (selectize-plugin-a11y library), Denis Ineshin [ctb, cph] (ion.rangeSlider library), Sami Samhuri [ctb, cph] (Javascript strftime library), SpryMedia Limited [ctb, cph] (DataTables library), John Fraser [ctb, cph] (showdown.js library), John Gruber [ctb, cph] (showdown.js library), Ivan Sagalaev [ctb, cph] (highlight.js library), R Core Team [ctb, cph] (tar implementation from R)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "shinydashboard": {"Package": "shinydashboard", "Version": "0.7.3", "Source": "Repository", "Title": "Create Dashboards with 'Shiny'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON>ibe<PERSON>\", role = \"aut\"), person(, \"Posit Software, PBC\", role = \"cph\"), person(, \"Almasaeed Studio\", role = c(\"ctb\", \"cph\"), comment = \"AdminLTE theme for Bootstrap\"), person(, \"Adobe Systems Incorporated\", role = c(\"ctb\", \"cph\"), comment = \"Source Sans Pro font\") )", "Description": "Create dashboards with 'Shiny'. This package provides a theme on top of 'Shiny', making it easy to create attractive dashboards.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/shinydashboard/, https://github.com/rstudio/shinydashboard", "BugReports": "https://github.com/rstudio/shinydashboard/issues", "Depends": ["R (>= 3.0)"], "Imports": ["htmltools (>= 0.2.6)", "promises", "shiny (>= 1.0.0)", "utils"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut], Posit Software, PBC [cph], Almasaeed Studio [ctb, cph] (AdminLTE theme for Bootstrap), Adobe Systems Incorporated [ctb, cph] (Source Sans Pro font)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "shinyjs": {"Package": "<PERSON><PERSON><PERSON>", "Version": "2.1.0", "Source": "Repository", "Title": "E<PERSON> Improve the User Experience of Your Shiny Apps in Seconds", "Authors@R": "person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\",  email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment= c(ORCID=\"0000-0002-5645-3493\"))", "Description": "Perform common useful JavaScript operations in Shiny apps that will greatly improve your apps without having to know any JavaScript. Examples include: hiding an element, disabling an input, resetting an input back to its original value, delaying code execution by a few seconds, and many more useful functions for both the end user and the developer. 'shinyjs' can also be used to easily call your own custom JavaScript functions from R.", "URL": "https://deanattali.com/shinyjs/", "BugReports": "https://github.com/daattali/shinyjs/issues", "Depends": ["R (>= 3.1.0)"], "Imports": ["digest (>= 0.6.8)", "jsonlite", "shiny (>= 1.0.0)"], "Suggests": ["htmltools (>= 0.2.9)", "knitr (>= 1.7)", "rmarkdown", "shinyAce", "shinydisconnect", "testthat (>= 0.9.1)"], "License": "MIT + file LICENSE", "VignetteBuilder": "knitr", "RoxygenNote": "7.1.1", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-5645-3493>)", "Maintainer": "<PERSON> <da<PERSON><PERSON>@gmail.com>", "Repository": "CRAN"}, "sourcetools": {"Package": "sourcetools", "Version": "0.1.7-1", "Source": "Repository", "Type": "Package", "Title": "Tools for Reading, Tokenizing and Parsing R Code", "Author": "<PERSON>", "Maintainer": "<PERSON> <kev<PERSON><PERSON>@gmail.com>", "Description": "Tools for the reading and tokenization of R code. The 'sourcetools' package provides both an R and C++ interface for the tokenization of R code, and helpers for interacting with the tokenized representation of R code.", "License": "MIT + file LICENSE", "Depends": ["R (>= 3.0.2)"], "Suggests": ["testthat"], "RoxygenNote": "5.0.1", "BugReports": "https://github.com/kevinushey/sourcetools/issues", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Repository": "CRAN"}, "sp": {"Package": "sp", "Version": "2.2-0", "Source": "Repository", "Title": "Classes and Methods for Spatial Data", "Authors@R": "c(person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>iva<PERSON>\", role = \"aut\", email = \"<PERSON>.<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"Gomez-R<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"Hij<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>Rourke\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"))", "Depends": ["R (>= 3.5.0)", "methods"], "Imports": ["utils", "stats", "graphics", "grDevices", "lattice", "grid"], "Suggests": ["RColorBrewer", "gstat", "<PERSON><PERSON>", "knitr", "maps", "mapview", "rmarkdown", "sf", "terra", "raster"], "Description": "Classes and methods for spatial data; the classes document where the spatial location information resides, for 2D or 3D data. Utility functions are provided, e.g. for plotting data as maps, spatial selection, as well as methods for retrieving coordinates, for subsetting, print, summary, etc. From this version, 'rgdal', 'maptools', and 'rgeos' are no longer used at all, see <https://r-spatial.org/r/2023/05/15/evolution4.html> for details.", "License": "GPL (>= 2)", "URL": "https://github.com/edzer/sp/ https://edzer.github.io/sp/", "BugReports": "https://github.com/edzer/sp/issues", "Collate": "bpy.colors.R AAA.R Class-CRS.R CRS-methods.R Class-Spatial.R Spatial-methods.R projected.R Class-SpatialPoints.R SpatialPoints-methods.R Class-SpatialPointsDataFrame.R SpatialPointsDataFrame-methods.R Class-SpatialMultiPoints.R SpatialMultiPoints-methods.R Class-SpatialMultiPointsDataFrame.R SpatialMultiPointsDataFrame-methods.R Class-GridTopology.R Class-SpatialGrid.R Class-SpatialGridDataFrame.R Class-SpatialLines.R SpatialLines-methods.R Class-SpatialLinesDataFrame.R SpatialLinesDataFrame-methods.R Class-SpatialPolygons.R Class-SpatialPolygonsDataFrame.R SpatialPolygons-methods.R SpatialPolygonsDataFrame-methods.R GridTopology-methods.R SpatialGrid-methods.R SpatialGridDataFrame-methods.R SpatialPolygons-internals.R point.in.polygon.R SpatialPolygons-displayMethods.R zerodist.R image.R stack.R bubble.R mapasp.R select.spatial.R gridded.R asciigrid.R spplot.R over.R spsample.R recenter.R dms.R gridlines.R spdists.R rbind.R flipSGDF.R chfids.R loadmeuse.R compassRose.R surfaceArea.R spOptions.R subset.R disaggregate.R sp_spat1.R merge.R aggregate.R elide.R sp2Mondrian.R", "VignetteBuilder": "knitr", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre], <PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "stringi": {"Package": "stringi", "Version": "1.8.7", "Source": "Repository", "Date": "2025-03-27", "Title": "Fast and Portable Character String Processing Facilities", "Description": "A collection of character string/text/natural language processing tools for pattern searching (e.g., with 'Java'-like regular expressions or the 'Unicode' collation algorithm), random string generation, case mapping, string transliteration, concatenation, sorting, padding, wrapping, Unicode normalisation, date-time formatting and parsing, and many more. They are fast, consistent, convenient, and - thanks to 'ICU' (International Components for Unicode) - portable across all locales and platforms. Documentation about 'stringi' is provided via its website at <https://stringi.gagolewski.com/> and the paper by <PERSON><PERSON><PERSON><PERSON> (2022, <doi:10.18637/jss.v103.i02>).", "URL": "https://stringi.gagolewski.com/, https://github.com/gagolews/stringi, https://icu.unicode.org/", "BugReports": "https://github.com/gagolews/stringi/issues", "SystemRequirements": "ICU4C (>= 61, optional)", "Type": "Package", "Depends": ["R (>= 3.4)"], "Imports": ["tools", "utils", "stats"], "Biarch": "TRUE", "License": "file LICENSE", "Authors@R": "c(person(given = \"<PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0637-6028\")), person(given = \"<PERSON><PERSON>\", family = \"Tartanus\", role = \"ctb\"), person(\"Unicode, Inc. and others\", role=\"ctb\", comment = \"ICU4C source code, Unicode Character Database\") )", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0003-0637-6028>), <PERSON><PERSON> [ctb], Unicode, Inc. and others [ctb] (ICU4C source code, Unicode Character Database)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "License_is_FOSS": "yes", "Repository": "CRAN"}, "stringr": {"Package": "stringr", "Version": "1.5.1", "Source": "Repository", "Title": "Simple, Consistent Wrappers for Common String Operations", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A consistent, simple and easy to use set of wrappers around the fantastic 'stringi' package. All function and argument names (and positions) are consistent, all functions deal with \"NA\"'s and zero length vectors in the same way, and the output from one function is easy to feed into the input of another.", "License": "MIT + file LICENSE", "URL": "https://stringr.tidyverse.org, https://github.com/tidyverse/stringr", "BugReports": "https://github.com/tidyverse/stringr/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli", "glue (>= 1.6.1)", "lifecycle (>= 1.0.3)", "magrit<PERSON>", "rlang (>= 1.0.0)", "stringi (>= 1.5.3)", "vctrs (>= 0.4.0)"], "Suggests": ["covr", "dplyr", "gt", "htmltools", "htmlwidgets", "knitr", "rmarkdown", "testthat (>= 3.0.0)", "tibble"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre, cph], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "sys": {"Package": "sys", "Version": "3.4.3", "Source": "Repository", "Type": "Package", "Title": "Powerful and Reliable Tools for Running System Commands in R", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"),  email = \"jeroeno<PERSON>@gmail.com\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\"))", "Description": "Drop-in replacements for the base system2() function with fine control and consistent behavior across platforms. Supports clean interruption, timeout,  background tasks, and streaming STDIN / STDOUT / STDERR over binary or text  connections. Arguments on Windows automatically get encoded and quoted to work  on different locales.", "License": "MIT + file LICENSE", "URL": "https://jeroen.r-universe.dev/sys", "BugReports": "https://github.com/jeroen/sys/issues", "Encoding": "UTF-8", "RoxygenNote": "7.1.1", "Suggests": ["unix (>= 1.4)", "spelling", "testthat"], "Language": "en-US", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>), <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "systemfonts": {"Package": "systemfonts", "Version": "1.2.3", "Source": "Repository", "Type": "Package", "Title": "System Native Font Finding", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-5147-4711\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", , \"j<PERSON><PERSON>@berkeley.edu\", role = \"aut\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", comment = \"Author of font-manager\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Provides system native access to the font catalogue. As font handling varies between systems it is difficult to correctly locate installed fonts across different operating systems. The 'systemfonts' package provides bindings to the native libraries on Windows, macOS and Linux for finding font files that can then be used further by e.g. graphic devices. The main use is intended to be from compiled code but 'systemfonts' also provides access from R.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/systemfonts, https://systemfonts.r-lib.org", "BugReports": "https://github.com/r-lib/systemfonts/issues", "Depends": ["R (>= 3.2.0)"], "Imports": ["base64enc", "grid", "jsonlite", "lifecycle", "tools", "utils"], "Suggests": ["covr", "farver", "graphics", "knitr", "rmarkdown", "testthat (>= 2.1.0)"], "LinkingTo": ["cpp11 (>= 0.2.1)"], "VignetteBuilder": "knitr", "Config/build/compilation-database": "true", "Config/Needs/website": "tidyverse/tidytemplate", "Config/usethis/last-upkeep": "2025-04-23", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "SystemRequirements": "fontconfig, freetype2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-5147-4711>), <PERSON><PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-4035-0289>), <PERSON> [aut] (Author of font-manager), Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "terra": {"Package": "terra", "Version": "1.8-54", "Source": "Repository", "Type": "Package", "Title": "Spatial Data Analysis", "Date": "2025-06-01", "Depends": ["R (>= 3.5.0)"], "Suggests": ["parallel", "tinytest", "ncdf4", "sf (>= 0.9-8)", "<PERSON><PERSON>", "XML", "leaflet (>= 2.2.1)", "htmlwidgets"], "LinkingTo": ["Rcpp"], "Imports": ["methods", "Rcpp (>= 1.0-10)"], "SystemRequirements": "C++17, <PERSON><PERSON><PERSON> (>= 2.2.3), GEOS (>= 3.4.0), PROJ (>= 4.9.3), TBB, sqlite3", "Encoding": "UTF-8", "Language": "en-US", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Methods for spatial data analysis with vector (points, lines, polygons) and raster (grid) data. Methods for vector data include geometric operations such as intersect and buffer. Raster methods include local, focal, global, zonal and geometric operations. The predict and interpolate methods facilitate the use of regression type (interpolation, machine learning) models for spatial prediction, including with satellite remote sensing data. Processing of very large files is supported. See the manual and tutorials on <https://rspatial.org/> to get started. 'terra' replaces the 'raster' package ('terra' can do more, and it is faster and easier to use).", "License": "GPL (>= 3)", "URL": "https://rspatial.org/, https://rspatial.github.io/terra/", "BugReports": "https://github.com/rspatial/terra/issues/", "LazyLoad": "yes", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"cre\", \"aut\"),   email=\"<EMAIL>\", comment=c(ORCID=\"0000-0001-5872-2872\")),\t\t\t person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", comment=c(ORCID=\"0000-0003-2392-6140\")), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\",comment=c(ORCID=\"0000-0002-3508-5898\")), person(\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", comment=c(ORCID=\"0000-0002-8614-3816\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\", comment=c(ORCID=\"0000-0001-8049-7069\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"))", "NeedsCompilation": "yes", "Author": "<PERSON> [cre, aut] (ORCID: <https://orcid.org/0000-0001-5872-2872>), <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (ORCID: <https://orcid.org/0000-0003-2392-6140>), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb] (ORCID: <https://orcid.org/0000-0002-3508-5898>), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [ctb] (ORCID: <https://orcid.org/0000-0002-8614-3816>), <PERSON><PERSON> [ctb] (ORCID: <https://orcid.org/0000-0001-8049-7069>), <PERSON> [ctb], <PERSON> [ctb]", "Repository": "CRAN"}, "tibble": {"Package": "tibble", "Version": "3.3.0", "Source": "Repository", "Title": "Simple Data Frames", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"Wickham\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>in\", family = \"<PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"RStudio\", role = c(\"cph\", \"fnd\")))", "Description": "Provides a 'tbl_df' class (the 'tibble') with stricter checking and better formatting than the traditional data frame.", "License": "MIT + file LICENSE", "URL": "https://tibble.tidyverse.org/, https://github.com/tidyverse/tibble", "BugReports": "https://github.com/tidyverse/tibble/issues", "Depends": ["R (>= 3.4.0)"], "Imports": ["cli", "lifecycle (>= 1.0.0)", "magrit<PERSON>", "methods", "pillar (>= 1.8.1)", "pkgconfig", "rlang (>= 1.0.2)", "utils", "vctrs (>= 0.5.0)"], "Suggests": ["bench", "bit64", "blob", "brio", "callr", "DiagrammeR", "dplyr", "evaluate", "formattable", "ggplot2", "here", "hms", "htmltools", "knitr", "lubridate", "nycflights13", "pkgload", "purrr", "rmarkdown", "stringi", "testthat (>= 3.0.2)", "tidyr", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "vignette-formats, as_tibble, add, invariants", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "true", "Config/autostyle/rmd": "false", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tidyr": {"Package": "tidyr", "Version": "1.3.1", "Source": "Repository", "Title": "Tidy Messy Data", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools to help to create tidy data, where each column is a variable, each row is an observation, and each cell contains a single value.  'tidyr' contains tools for changing the shape (pivoting) and hierarchy (nesting and 'unnesting') of a dataset, turning deeply nested lists into rectangular data frames ('rectangling'), and extracting values out of string columns. It also includes tools for working with missing values (both implicit and explicit).", "License": "MIT + file LICENSE", "URL": "https://tidyr.tidyverse.org, https://github.com/tidyverse/tidyr", "BugReports": "https://github.com/tidyverse/tidyr/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli (>= 3.4.1)", "dplyr (>= 1.0.10)", "glue", "lifecycle (>= 1.0.3)", "magrit<PERSON>", "purrr (>= 1.0.1)", "rlang (>= 1.1.1)", "stringr (>= 1.5.0)", "tibble (>= 2.1.1)", "tidyselect (>= 1.2.0)", "utils", "vctrs (>= 0.5.2)"], "Suggests": ["covr", "data.table", "knitr", "readr", "repurrrsive (>= 1.1.0)", "rmarkdown", "testthat (>= 3.0.0)"], "LinkingTo": ["cpp11 (>= 0.4.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.3.0", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tidyselect": {"Package": "tidyselect", "Version": "1.2.1", "Source": "Repository", "Title": "Select from a Set of Strings", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A backend for the selecting functions of the 'tidyverse'.  It makes it easy to implement select-like functions in your own packages in a way that is consistent with other 'tidyverse' interfaces for selection.", "License": "MIT + file LICENSE", "URL": "https://tidyselect.r-lib.org, https://github.com/r-lib/tidyselect", "BugReports": "https://github.com/r-lib/tidyselect/issues", "Depends": ["R (>= 3.4)"], "Imports": ["cli (>= 3.3.0)", "glue (>= 1.3.0)", "lifecycle (>= 1.0.3)", "rlang (>= 1.0.4)", "vctrs (>= 0.5.2)", "withr"], "Suggests": ["covr", "crayon", "dplyr", "knitr", "magrit<PERSON>", "rmarkdown", "stringr", "testthat (>= 3.1.1)", "tibble (>= 2.1.3)"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/testthat/edition": "3", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.3.0.9000", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "timechange": {"Package": "timechange", "Version": "0.3.0", "Source": "Repository", "Title": "Efficient Manipulation of Date-Times", "Authors@R": "c(person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Google Inc.\", role = c(\"ctb\", \"cph\")))", "Description": "Efficient routines for manipulation of date-time objects while accounting for time-zones and daylight saving times. The package includes utilities for updating of date-time components (year, month, day etc.), modification of time-zones, rounding of date-times, period addition and subtraction etc. Parts of the 'CCTZ' source code, released under the Apache 2.0 License, are included in this package. See <https://github.com/google/cctz> for more details.", "Depends": ["R (>= 3.3)"], "License": "GPL (>= 3)", "Encoding": "UTF-8", "LinkingTo": ["cpp11 (>= 0.2.7)"], "Suggests": ["testthat (>= ********)", "knitr"], "SystemRequirements": "A system with zoneinfo data (e.g. /usr/share/zoneinfo) as well as a recent-enough C++11 compiler (such as g++-4.8 or later). On Windows the zoneinfo included with R is used.", "BugReports": "https://github.com/vspinu/timechange/issues", "URL": "https://github.com/vspinu/timechange/", "RoxygenNote": "7.2.1", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre], Google Inc. [ctb, cph]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tinytex": {"Package": "tinytex", "Version": "0.57", "Source": "Repository", "Type": "Package", "Title": "Helper Functions to Install and Maintain TeX Live, and Compile LaTeX Documents", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-4474-2498\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-8549-0971\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person() )", "Description": "Helper functions to install and maintain the 'LaTeX' distribution named 'TinyTeX' (<https://yihui.org/tinytex/>), a lightweight, cross-platform, portable, and easy-to-maintain version of 'TeX Live'. This package also contains helper functions to compile 'LaTeX' documents, and install missing 'LaTeX' packages automatically.", "Imports": ["xfun (>= 0.48)"], "Suggests": ["testit", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "License": "MIT + file LICENSE", "URL": "https://github.com/rstudio/tinytex", "BugReports": "https://github.com/rstudio/tinytex/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0003-0645-5666>), Posit Software, PBC [cph, fnd], <PERSON> [ctb] (<https://orcid.org/0000-0003-4474-2498>), <PERSON> [ctb] (<https://orcid.org/0000-0002-8549-0971>), <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "utf8": {"Package": "utf8", "Version": "1.2.6", "Source": "Repository", "Title": "Unicode Text Processing", "Authors@R": "c(person(given = c(\"<PERSON>\", \"<PERSON><PERSON>\"), family = \"<PERSON>\", role = c(\"aut\", \"cph\")), person(given = \"Kirill\", family = \"M\\u00fcller\", role = \"cre\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"Unicode, Inc.\", role = c(\"cph\", \"dtc\"), comment = \"Unicode Character Database\"))", "Description": "Process and print 'UTF-8' encoded international text (Unicode). Input, validate, normalize, encode, format, and display.", "License": "Apache License (== 2.0) | file LICENSE", "URL": "https://krlmlr.github.io/utf8/, https://github.com/krlmlr/utf8", "BugReports": "https://github.com/krlmlr/utf8/issues", "Depends": ["R (>= 2.10)"], "Suggests": ["cli", "covr", "knitr", "rlang", "rmarkdown", "testthat (>= 3.0.0)", "withr"], "VignetteBuilder": "knitr, rmarkdown", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cph], <PERSON><PERSON> [cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), Unicode, Inc. [cph, dtc] (Unicode Character Database)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "uuid": {"Package": "uuid", "Version": "1.2-1", "Source": "Repository", "Title": "Tools for Generating and Handling of UUIDs", "Author": "<PERSON> [aut, cre, cph] (https://urbanek.org, <https://orcid.org/0000-0003-2297-1732>), <PERSON> [aut, cph] (libuuid)", "Maintainer": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON>\", role=c(\"aut\",\"cre\",\"cph\"), email=\"<EMAIL>\", comment=c(\"https://urbanek.org\", ORCID=\"0000-0003-2297-1732\")), person(\"<PERSON>\",\"Ts'o\", email=\"<EMAIL>\", role=c(\"aut\",\"cph\"), comment=\"libuuid\"))", "Depends": ["R (>= 2.9.0)"], "Description": "Tools for generating and handling of UUIDs (Universally Unique Identifiers).", "License": "MIT + file LICENSE", "URL": "https://www.rforge.net/uuid", "BugReports": "https://github.com/s-u/uuid/issues", "NeedsCompilation": "yes", "Repository": "CRAN"}, "vctrs": {"Package": "vctrs", "Version": "0.6.5", "Source": "Repository", "Title": "Vector Helpers", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"data.table team\", role = \"cph\", comment = \"Radix sort based on data.table's forder() and their contribution to R's order()\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Defines new notions of prototype and size that are used to provide tools for consistent and well-founded type-coercion and size-recycling, and are in turn connected to ideas of type- and size-stability useful for analysing function interfaces.", "License": "MIT + file LICENSE", "URL": "https://vctrs.r-lib.org/, https://github.com/r-lib/vctrs", "BugReports": "https://github.com/r-lib/vctrs/issues", "Depends": ["R (>= 3.5.0)"], "Imports": ["cli (>= 3.4.0)", "glue", "lifecycle (>= 1.0.3)", "rlang (>= 1.1.0)"], "Suggests": ["bit64", "covr", "crayon", "dplyr (>= 0.8.5)", "generics", "knitr", "pillar (>= 1.4.4)", "pkgdown (>= 2.0.1)", "rmarkdown", "testthat (>= 3.0.0)", "tibble (>= 3.1.3)", "waldo (>= 0.2.0)", "withr", "xml2", "zeallot"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-GB", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre], data.table team [cph] (Radix sort based on data.table's forder() and their contribution to R's order()), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "viridisLite": {"Package": "viridisLite", "Version": "0.4.2", "Source": "Repository", "Type": "Package", "Title": "Colorblind-Friendly Color Maps (Lite Version)", "Date": "2023-05-02", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\", \"cph\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\", \"cph\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\", \"cph\")), person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>here<PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\", \"cph\")) )", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Color maps designed to improve graph readability for readers with  common forms of color blindness and/or color vision deficiency. The color  maps are also perceptually-uniform, both in regular form and also when  converted to black-and-white for printing. This is the 'lite' version of the  'viridis' package that also contains 'ggplot2' bindings for discrete and  continuous color and fill scales and can be found at  <https://cran.r-project.org/package=viridis>.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "Depends": ["R (>= 2.10)"], "Suggests": ["hexbin (>= 1.27.0)", "ggplot2 (>= 1.0.1)", "testthat", "covr"], "URL": "https://sjmgarnier.github.io/viridisLite/, https://github.com/sjmgarnier/viridisLite/", "BugReports": "https://github.com/sjmgarnier/viridisLite/issues/", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [ctb, cph], <PERSON> [ctb, cph], <PERSON> [ctb, cph], <PERSON><PERSON><PERSON><PERSON> [ctb, cph], <PERSON><PERSON><PERSON> [ctb, cph]", "Repository": "CRAN"}, "waiter": {"Package": "waiter", "Version": "0.2.5", "Source": "Repository", "Title": "Loading Screen for 'Shiny'", "Date": "2022-01-02", "Authors@R": "c( person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = '<EMAIL>'), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = c(\"ctb\"), email = \"victorgra<PERSON><PERSON><PERSON>@gmail.com\", comment = c(ORCID = \"0000-0002-0469-1991\")))", "Description": "Full screen and partial loading screens for 'Shiny' with spinners, progress bars, and notifications.", "License": "MIT + file LICENSE", "URL": "https://waiter.john-coene.com/, https://github.com/JohnCoene/waiter", "BugReports": "https://github.com/JohnCoene/waiter/issues", "Encoding": "UTF-8", "Imports": ["R6", "shiny", "htmltools"], "RoxygenNote": "7.1.2", "Suggests": ["httr", "knitr", "packer", "rmarkdown"], "VignetteBuilder": "knitr", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0002-0469-1991>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "withr": {"Package": "withr", "Version": "3.0.2", "Source": "Repository", "Title": "Run Code 'With' Temporarily Modified Global State", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"kev<PERSON><PERSON>@gmail.com\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A set of functions to run code 'with' safely and temporarily modified global state. Many of these functions were originally a part of the 'devtools' package, this provides a simple package with limited dependencies to provide access to these functions.", "License": "MIT + file LICENSE", "URL": "https://withr.r-lib.org, https://github.com/r-lib/withr#readme", "BugReports": "https://github.com/r-lib/withr/issues", "Depends": ["R (>= 3.6.0)"], "Imports": ["graphics", "grDevices"], "Suggests": ["callr", "DBI", "knitr", "methods", "rlang", "rmarkdown (>= 2.12)", "RSQLite", "testthat (>= 3.0.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Collate": "'aaa.R' 'collate.R' 'connection.R' 'db.R' 'defer-exit.R' 'standalone-defer.R' 'defer.R' 'devices.R' 'local_.R' 'with_.R' 'dir.R' 'env.R' 'file.R' 'language.R' 'libpaths.R' 'locale.R' 'makevars.R' 'namespace.R' 'options.R' 'par.R' 'path.R' 'rng.R' 'seed.R' 'wrap.R' 'sink.R' 'tempfile.R' 'timezone.R' 'torture.R' 'utils.R' 'with.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "xfun": {"Package": "xfun", "Version": "0.52", "Source": "Repository", "Type": "Package", "Title": "Supporting Functions for Packages Maintained by '<PERSON><PERSON>'", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"Dai<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person() )", "Description": "Miscellaneous functions commonly used in other packages maintained by '<PERSON><PERSON>'.", "Depends": ["R (>= 3.2.0)"], "Imports": ["grDevices", "stats", "tools"], "Suggests": ["testit", "parallel", "codetools", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinytex (>= 0.30)", "mime", "litedown (>= 0.4)", "commonmark", "knitr (>= 1.50)", "remotes", "pak", "curl", "xml2", "jsonlite", "magick", "yaml", "qs"], "License": "MIT + file LICENSE", "URL": "https://github.com/yihui/xfun", "BugReports": "https://github.com/yihui/xfun/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "litedown", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-5329-5987>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "xtable": {"Package": "xtable", "Version": "1.8-4", "Source": "Repository", "Date": "2019-04-08", "Title": "Export Tables to LaTeX or HTML", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role=c(\"aut\",\"cre\"), email=\"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>fa<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"Andronic\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>echer\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>ubri\", role=\"ctb\"), person(\"<PERSON>hi<PERSON>\", \"<PERSON>igler\", role=\"ctb\"), person(\"Robert\", \"Castelo\", role=\"ctb\"), person(\"Seth\", \"Falcon\", role=\"ctb\"), person(\"Stefan\", \"Edwards\", role=\"ctb\"), person(\"Sven\", \"Garbade\", role=\"ctb\"), person(\"Uwe\", \"Ligges\", role=\"ctb\"))", "Maintainer": "<PERSON> <<EMAIL>>", "Imports": ["stats", "utils"], "Suggests": ["knitr", "plm", "zoo", "survival"], "VignetteBuilder": "knitr", "Description": "Coerce data to LaTeX and HTML tables.", "URL": "http://xtable.r-forge.r-project.org/", "Depends": ["R (>= 2.10.0)"], "License": "GPL (>= 2)", "Repository": "CRAN", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb]"}, "yaml": {"Package": "yaml", "Version": "2.3.10", "Source": "Repository", "Type": "Package", "Title": "Methods to Convert R Data to YAML and Back", "Date": "2024-07-22", "Suggests": ["RUnit"], "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "License": "BSD_3_clause + file LICENSE", "Description": "Implements the 'libyaml' 'YAML' 1.1 parser and emitter (<https://pyyaml.org/wiki/LibYAML>) for R.", "URL": "https://github.com/vubiostat/r-yaml/", "BugReports": "https://github.com/vubiostat/r-yaml/issues", "NeedsCompilation": "yes", "Repository": "CRAN"}, "zip": {"Package": "zip", "Version": "2.3.3", "Source": "Repository", "Title": "Cross-Platform 'zip' Compression", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"Podgórsk<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Cross-Platform 'zip' Compression Library. A replacement for the 'zip' function, that does not require any additional external tools on any platform.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/zip, https://r-lib.github.io/zip/", "BugReports": "https://github.com/r-lib/zip/issues", "Suggests": ["covr", "pillar", "processx", "R6", "testthat", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/usethis/last-upkeep": "2025-05-07", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON><PERSON> [ctb], <PERSON> [ctb], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "zoo": {"Package": "zoo", "Version": "1.8-14", "Source": "Repository", "Date": "2025-04-09", "Title": "S3 Infrastructure for Regular and Irregular Time Series (Z's Ordered Observations)", "Authors@R": "c(person(given = \"Achim\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0918-3766\")), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = c(\"<PERSON>\", \"<PERSON><PERSON>\"), family = \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = c(\"<PERSON>\", \"<PERSON><PERSON>\"), family = \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\"))", "Description": "An S3 class with methods for totally ordered indexed observations. It is particularly aimed at irregular time series of numeric vectors/matrices and factors. zoo's key design goals are independence of a particular index/date/time class and consistency with ts and base R by providing methods to extend standard generics.", "Depends": ["R (>= 3.1.0)", "stats"], "Suggests": ["AER", "coda", "chron", "ggplot2 (>= 3.5.0)", "mondate", "scales", "stinepack", "strucchange", "timeDate", "timeSeries", "tinyplot", "tis", "tseries", "xts"], "Imports": ["utils", "graphics", "grDevices", "lattice (>= 0.20-27)"], "License": "GPL-2 | GPL-3", "URL": "https://zoo.R-Forge.R-project.org/", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0918-3766>), <PERSON><PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}}}