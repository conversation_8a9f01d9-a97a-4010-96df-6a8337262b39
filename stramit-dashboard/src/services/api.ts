import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  AuthToken, 
  LoginCredentials, 
  RegisterData, 
  SalesData, 
  LeadData,
  DistributorData,
  PostcodeData,
  APIResponse,
  PaginatedResponse,
  FilterState
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('access_token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Authentication endpoints
  async login(credentials: LoginCredentials): Promise<AuthToken> {
    const response: AxiosResponse<AuthToken> = await this.api.post('/auth/login', credentials);
    return response.data;
  }

  async register(userData: RegisterData): Promise<AuthToken> {
    const response: AxiosResponse<AuthToken> = await this.api.post('/auth/register', userData);
    return response.data;
  }

  async refreshToken(): Promise<AuthToken> {
    const response: AxiosResponse<AuthToken> = await this.api.post('/auth/refresh');
    return response.data;
  }

  async getCurrentUser(): Promise<any> {
    const response = await this.api.get('/auth/me');
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await this.api.get('/health');
    return response.data;
  }

  // Sales data endpoints
  async getSalesData(filters?: Partial<FilterState>): Promise<SalesData[]> {
    const params = this.buildFilterParams(filters);
    const response: AxiosResponse<APIResponse<SalesData[]>> = await this.api.get('/api/sales', { params });
    return response.data.data;
  }

  async getSalesStats(filters?: Partial<FilterState>): Promise<any> {
    const params = this.buildFilterParams(filters);
    const response = await this.api.get('/api/sales/stats', { params });
    return response.data;
  }

  async getGaugeData(filters?: Partial<FilterState>): Promise<any> {
    const params = this.buildFilterParams(filters);
    const response = await this.api.get('/api/sales/gauge', { params });
    return response.data;
  }

  // Leads data endpoints
  async getLeadsData(filters?: Partial<FilterState>): Promise<LeadData[]> {
    const params = this.buildFilterParams(filters);
    const response: AxiosResponse<APIResponse<LeadData[]>> = await this.api.get('/api/leads', { params });
    return response.data.data;
  }

  async getLeadsStats(filters?: Partial<FilterState>): Promise<any> {
    const params = this.buildFilterParams(filters);
    const response = await this.api.get('/api/leads/stats', { params });
    return response.data;
  }

  // Map data endpoints
  async getDistributorData(): Promise<DistributorData[]> {
    const response: AxiosResponse<APIResponse<DistributorData[]>> = await this.api.get('/api/distributors');
    return response.data.data;
  }

  async getPostcodeData(): Promise<PostcodeData[]> {
    const response: AxiosResponse<APIResponse<PostcodeData[]>> = await this.api.get('/api/postcodes');
    return response.data.data;
  }

  async getMapSalesData(filters?: Partial<FilterState>): Promise<any> {
    const params = this.buildFilterParams(filters);
    const response = await this.api.get('/api/map/sales', { params });
    return response.data;
  }

  async getMapLeadsData(filters?: Partial<FilterState>): Promise<any> {
    const params = this.buildFilterParams(filters);
    const response = await this.api.get('/api/map/leads', { params });
    return response.data;
  }

  // Export endpoints
  async exportData(type: string, format: string, filters?: Partial<FilterState>): Promise<Blob> {
    const params = { 
      ...this.buildFilterParams(filters),
      format 
    };
    const response = await this.api.get(`/api/export/${type}`, { 
      params,
      responseType: 'blob'
    });
    return response.data;
  }

  // Utility methods
  private buildFilterParams(filters?: Partial<FilterState>): Record<string, any> {
    if (!filters) return {};

    const params: Record<string, any> = {};

    if (filters.categories && filters.categories.length > 0) {
      params.categories = filters.categories.join(',');
    }

    if (filters.unitPrice) {
      params.unit_price = filters.unitPrice;
    }

    if (filters.dateRange) {
      params.start_date = filters.dateRange.start.toISOString().split('T')[0];
      params.end_date = filters.dateRange.end.toISOString().split('T')[0];
    }

    if (filters.selectedArea && filters.selectedArea !== 'All') {
      params.area = filters.selectedArea;
    }

    if (filters.selectedDistributor && filters.selectedDistributor !== 'All') {
      params.distributor_id = filters.selectedDistributor;
    }

    if (filters.selectedPostcode) {
      params.postcode = filters.selectedPostcode;
    }

    if (filters.selectedState && filters.selectedState !== 'All') {
      params.state = filters.selectedState;
    }

    return params;
  }

  // Download helper
  downloadFile(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
}

export const apiService = new ApiService();
export default apiService;
