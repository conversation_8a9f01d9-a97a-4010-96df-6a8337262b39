import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  CircularProgress,
  Alert,
  Chip,
  Card,
  CardContent,
} from '@mui/material';
import AustralianMap from '../../components/maps/AustralianMap';
import { AustralianState } from '../../types';

interface LeadData {
  id: string;
  latitude: number;
  longitude: number;
  value: number;
  label: string;
  state: string;
  postcode: string;
  status: 'New' | 'Contacted' | 'Qualified' | 'Proposal' | 'Won' | 'Lost';
  source: 'Web' | 'App' | 'Referral' | 'Direct';
  category: string;
  estimatedValue: number;
}

const LEAD_STATUSES = ['All', 'New', 'Contacted', 'Qualified', 'Proposal', 'Won', 'Lost'] as const;
const LEAD_SOURCES = ['All', 'Web', 'App', 'Referral', 'Direct'] as const;

const STATUS_COLORS: Record<string, string> = {
  'New': '#84A98C',
  'Contacted': '#52796F',
  'Qualified': '#354F52',
  'Proposal': '#2F3E46',
  'Won': '#4CAF50',
  'Lost': '#F44336',
};

const LeadMapPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [mapType, setMapType] = useState<'sales' | 'frequency'>('frequency');
  const [selectedState, setSelectedState] = useState<AustralianState>('All');
  const [selectedPostcode, setSelectedPostcode] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('All');
  const [selectedSource, setSelectedSource] = useState<string>('All');
  const [leadData, setLeadData] = useState<LeadData[]>([]);

  useEffect(() => {
    loadLeadData();
  }, []);

  const loadLeadData = async () => {
    try {
      setLoading(true);
      setError('');

      // Mock data - replace with actual API call
      const mockData: LeadData[] = [
        {
          id: '1',
          latitude: -33.8688,
          longitude: 151.2093,
          value: 25,
          label: 'Sydney CBD',
          state: 'NSW',
          postcode: '2000',
          status: 'New',
          source: 'Web',
          category: 'FDB Sheds',
          estimatedValue: 45000,
        },
        {
          id: '2',
          latitude: -37.8136,
          longitude: 144.9631,
          value: 18,
          label: 'Melbourne CBD',
          state: 'VIC',
          postcode: '3000',
          status: 'Contacted',
          source: 'App',
          category: 'SB Sheds',
          estimatedValue: 38000,
        },
        {
          id: '3',
          latitude: -27.4698,
          longitude: 153.0251,
          value: 22,
          label: 'Brisbane CBD',
          state: 'QLD',
          postcode: '4000',
          status: 'Qualified',
          source: 'Referral',
          category: 'FDB Patios',
          estimatedValue: 52000,
        },
        {
          id: '4',
          latitude: -34.9285,
          longitude: 138.6007,
          value: 12,
          label: 'Adelaide CBD',
          state: 'SA',
          postcode: '5000',
          status: 'Proposal',
          source: 'Direct',
          category: 'Lifestyle',
          estimatedValue: 65000,
        },
        {
          id: '5',
          latitude: -31.9505,
          longitude: 115.8605,
          value: 16,
          label: 'Perth CBD',
          state: 'WA',
          postcode: '6000',
          status: 'Won',
          source: 'Web',
          category: 'FDB Sheds',
          estimatedValue: 42000,
        },
        {
          id: '6',
          latitude: -42.8821,
          longitude: 147.3272,
          value: 5,
          label: 'Hobart',
          state: 'TAS',
          postcode: '7000',
          status: 'Lost',
          source: 'App',
          category: 'SB Sheds',
          estimatedValue: 35000,
        },
        {
          id: '7',
          latitude: -35.2809,
          longitude: 149.1300,
          value: 8,
          label: 'Canberra',
          state: 'ACT',
          postcode: '2600',
          status: 'New',
          source: 'Referral',
          category: 'NZ Sheds',
          estimatedValue: 48000,
        },
        {
          id: '8',
          latitude: -32.9267,
          longitude: 151.7789,
          value: 14,
          label: 'Newcastle',
          state: 'NSW',
          postcode: '2300',
          status: 'Contacted',
          source: 'Web',
          category: 'FDB Patios',
          estimatedValue: 39000,
        },
        {
          id: '9',
          latitude: -38.1499,
          longitude: 144.3617,
          value: 9,
          label: 'Geelong',
          state: 'VIC',
          postcode: '3220',
          status: 'Qualified',
          source: 'Direct',
          category: 'Lifestyle',
          estimatedValue: 55000,
        },
        {
          id: '10',
          latitude: -19.2590,
          longitude: 146.8169,
          value: 7,
          label: 'Townsville',
          state: 'QLD',
          postcode: '4810',
          status: 'Proposal',
          source: 'App',
          category: 'FDB Sheds',
          estimatedValue: 41000,
        },
      ];

      setLeadData(mockData);

    } catch (err: any) {
      setError('Failed to load lead data');
      console.error('Lead data error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getFilteredData = () => {
    let filtered = leadData;

    if (selectedStatus !== 'All') {
      filtered = filtered.filter(item => item.status === selectedStatus);
    }

    if (selectedSource !== 'All') {
      filtered = filtered.filter(item => item.source === selectedSource);
    }

    return filtered.map(item => ({
      id: item.id,
      latitude: item.latitude,
      longitude: item.longitude,
      value: mapType === 'sales' ? item.estimatedValue : item.value,
      label: item.label,
      state: item.state,
      postcode: item.postcode,
      category: item.status,
    }));
  };

  const getStatusStats = () => {
    const stats: Record<string, number> = {};
    LEAD_STATUSES.slice(1).forEach(status => {
      stats[status] = leadData.filter(lead => lead.status === status).length;
    });
    return stats;
  };

  const getSourceStats = () => {
    const stats: Record<string, number> = {};
    LEAD_SOURCES.slice(1).forEach(source => {
      stats[source] = leadData.filter(lead => lead.source === source).length;
    });
    return stats;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} sx={{ color: '#84A98C' }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  const statusStats = getStatusStats();
  const sourceStats = getSourceStats();

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold', color: '#84A98C' }}>
        Lead Map
      </Typography>

      {/* Filters */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel>Status</InputLabel>
            <Select
              value={selectedStatus}
              onChange={(e: SelectChangeEvent<string>) => setSelectedStatus(e.target.value)}
              label="Status"
            >
              {LEAD_STATUSES.map((status) => (
                <MenuItem key={status} value={status}>
                  {status}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel>Source</InputLabel>
            <Select
              value={selectedSource}
              onChange={(e: SelectChangeEvent<string>) => setSelectedSource(e.target.value)}
              label="Source"
            >
              {LEAD_SOURCES.map((source) => (
                <MenuItem key={source} value={source}>
                  {source}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* Status Overview */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Lead Status Overview
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {Object.entries(statusStats).map(([status, count]) => (
                  <Chip
                    key={status}
                    label={`${status}: ${count}`}
                    sx={{
                      backgroundColor: STATUS_COLORS[status],
                      color: 'white',
                      fontWeight: 'bold',
                    }}
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Map */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <AustralianMap
            data={getFilteredData()}
            mapType={mapType}
            onMapTypeChange={setMapType}
            selectedState={selectedState}
            onStateChange={setSelectedState}
            selectedPostcode={selectedPostcode}
            onPostcodeChange={setSelectedPostcode}
            title="Lead Distribution"
          />
        </Grid>
      </Grid>

      {/* Source Statistics */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Leads by Source
              </Typography>
              {Object.entries(sourceStats).map(([source, count]) => (
                <Box key={source} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>{source}</Typography>
                  <Typography sx={{ fontWeight: 'bold' }}>{count}</Typography>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Summary
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Total Leads</Typography>
                <Typography sx={{ fontWeight: 'bold' }}>{leadData.length}</Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Total Estimated Value</Typography>
                <Typography sx={{ fontWeight: 'bold' }}>
                  ${leadData.reduce((sum, lead) => sum + lead.estimatedValue, 0).toLocaleString()}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Conversion Rate</Typography>
                <Typography sx={{ fontWeight: 'bold' }}>
                  {((statusStats.Won / leadData.length) * 100).toFixed(1)}%
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default LeadMapPage;
