import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  IconButton,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Download,
  Visibility,
  Edit,
  Delete,
  FilterList,
} from '@mui/icons-material';
import { SelectChangeEvent } from '@mui/material/Select';

interface LeadRecord {
  id: string;
  outlet: string;
  customerName: string;
  email: string;
  phone: string;
  state: string;
  postcode: string;
  category: string;
  estimatedValue: number;
  status: 'New' | 'Contacted' | 'Qualified' | 'Proposal' | 'Won' | 'Lost';
  source: 'Web' | 'App' | 'Referral' | 'Direct';
  dateCreated: string;
  lastContact: string;
  assignedTo: string;
}

type Order = 'asc' | 'desc';

const LeadsDataPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [leads, setLeads] = useState<LeadRecord[]>([]);
  const [filteredLeads, setFilteredLeads] = useState<LeadRecord[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [order, setOrder] = useState<Order>('desc');
  const [orderBy, setOrderBy] = useState<keyof LeadRecord>('dateCreated');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('All');
  const [sourceFilter, setSourceFilter] = useState<string>('All');
  const [outletFilter, setOutletFilter] = useState<string>('All');

  useEffect(() => {
    loadLeadsData();
  }, []);

  useEffect(() => {
    filterLeads();
  }, [leads, searchTerm, statusFilter, sourceFilter, outletFilter]);

  const loadLeadsData = async () => {
    try {
      setLoading(true);
      setError('');

      // Mock data - replace with actual API call
      const mockData: LeadRecord[] = [
        {
          id: '1',
          outlet: 'Sydney Outlet',
          customerName: 'John Smith',
          email: '<EMAIL>',
          phone: '0412345678',
          state: 'NSW',
          postcode: '2000',
          category: 'FDB Sheds',
          estimatedValue: 45000,
          status: 'New',
          source: 'Web',
          dateCreated: '2024-01-15',
          lastContact: '2024-01-15',
          assignedTo: 'Sarah Johnson',
        },
        {
          id: '2',
          outlet: 'Melbourne Outlet',
          customerName: 'Emma Wilson',
          email: '<EMAIL>',
          phone: '0423456789',
          state: 'VIC',
          postcode: '3000',
          category: 'SB Sheds',
          estimatedValue: 38000,
          status: 'Contacted',
          source: 'App',
          dateCreated: '2024-01-14',
          lastContact: '2024-01-16',
          assignedTo: 'Mike Chen',
        },
        {
          id: '3',
          outlet: 'Brisbane Outlet',
          customerName: 'David Brown',
          email: '<EMAIL>',
          phone: '0434567890',
          state: 'QLD',
          postcode: '4000',
          category: 'FDB Patios',
          estimatedValue: 52000,
          status: 'Qualified',
          source: 'Referral',
          dateCreated: '2024-01-13',
          lastContact: '2024-01-17',
          assignedTo: 'Lisa Wang',
        },
        {
          id: '4',
          outlet: 'Adelaide Outlet',
          customerName: 'Sophie Taylor',
          email: '<EMAIL>',
          phone: '0445678901',
          state: 'SA',
          postcode: '5000',
          category: 'Lifestyle',
          estimatedValue: 65000,
          status: 'Proposal',
          source: 'Direct',
          dateCreated: '2024-01-12',
          lastContact: '2024-01-18',
          assignedTo: 'Tom Anderson',
        },
        {
          id: '5',
          outlet: 'Perth Outlet',
          customerName: 'Michael Davis',
          email: '<EMAIL>',
          phone: '0456789012',
          state: 'WA',
          postcode: '6000',
          category: 'FDB Sheds',
          estimatedValue: 42000,
          status: 'Won',
          source: 'Web',
          dateCreated: '2024-01-11',
          lastContact: '2024-01-19',
          assignedTo: 'Rachel Green',
        },
        // Add more mock data...
      ];

      // Generate more mock data
      for (let i = 6; i <= 50; i++) {
        mockData.push({
          id: i.toString(),
          outlet: ['Sydney Outlet', 'Melbourne Outlet', 'Brisbane Outlet', 'Adelaide Outlet', 'Perth Outlet'][Math.floor(Math.random() * 5)],
          customerName: `Customer ${i}`,
          email: `customer${i}@email.com`,
          phone: `04${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
          state: ['NSW', 'VIC', 'QLD', 'SA', 'WA'][Math.floor(Math.random() * 5)],
          postcode: (Math.floor(Math.random() * 9000) + 1000).toString(),
          category: ['FDB Sheds', 'SB Sheds', 'FDB Patios', 'Lifestyle', 'NZ Sheds'][Math.floor(Math.random() * 5)],
          estimatedValue: Math.floor(Math.random() * 50000) + 30000,
          status: ['New', 'Contacted', 'Qualified', 'Proposal', 'Won', 'Lost'][Math.floor(Math.random() * 6)] as any,
          source: ['Web', 'App', 'Referral', 'Direct'][Math.floor(Math.random() * 4)] as any,
          dateCreated: `2024-01-${Math.floor(Math.random() * 28) + 1}`,
          lastContact: `2024-01-${Math.floor(Math.random() * 28) + 1}`,
          assignedTo: ['Sarah Johnson', 'Mike Chen', 'Lisa Wang', 'Tom Anderson', 'Rachel Green'][Math.floor(Math.random() * 5)],
        });
      }

      setLeads(mockData);

    } catch (err: any) {
      setError('Failed to load leads data');
      console.error('Leads data error:', err);
    } finally {
      setLoading(false);
    }
  };

  const filterLeads = () => {
    let filtered = leads;

    if (searchTerm) {
      filtered = filtered.filter(lead =>
        lead.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.outlet.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'All') {
      filtered = filtered.filter(lead => lead.status === statusFilter);
    }

    if (sourceFilter !== 'All') {
      filtered = filtered.filter(lead => lead.source === sourceFilter);
    }

    if (outletFilter !== 'All') {
      filtered = filtered.filter(lead => lead.outlet === outletFilter);
    }

    setFilteredLeads(filtered);
  };

  const handleRequestSort = (property: keyof LeadRecord) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleExport = () => {
    // Export functionality
    console.log('Exporting leads data...');
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'New': '#84A98C',
      'Contacted': '#52796F',
      'Qualified': '#354F52',
      'Proposal': '#2F3E46',
      'Won': '#4CAF50',
      'Lost': '#F44336',
    };
    return colors[status] || '#84A98C';
  };

  const sortedLeads = React.useMemo(() => {
    return filteredLeads.sort((a, b) => {
      if (orderBy === 'estimatedValue') {
        return order === 'asc' ? a[orderBy] - b[orderBy] : b[orderBy] - a[orderBy];
      }
      const aValue = a[orderBy].toString();
      const bValue = b[orderBy].toString();
      return order === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });
  }, [filteredLeads, order, orderBy]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} sx={{ color: '#84A98C' }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  const outlets = Array.from(new Set(leads.map(lead => lead.outlet)));

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold', color: '#84A98C' }}>
        Leads Data by Outlet
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary">Total Leads</Typography>
              <Typography variant="h4" sx={{ color: '#84A98C' }}>{leads.length}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary">Won Leads</Typography>
              <Typography variant="h4" sx={{ color: '#4CAF50' }}>
                {leads.filter(lead => lead.status === 'Won').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary">Total Value</Typography>
              <Typography variant="h4" sx={{ color: '#84A98C' }}>
                ${leads.reduce((sum, lead) => sum + lead.estimatedValue, 0).toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary">Conversion Rate</Typography>
              <Typography variant="h4" sx={{ color: '#84A98C' }}>
                {((leads.filter(lead => lead.status === 'Won').length / leads.length) * 100).toFixed(1)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              size="small"
              label="Search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e: SelectChangeEvent<string>) => setStatusFilter(e.target.value)}
                label="Status"
              >
                <MenuItem value="All">All</MenuItem>
                <MenuItem value="New">New</MenuItem>
                <MenuItem value="Contacted">Contacted</MenuItem>
                <MenuItem value="Qualified">Qualified</MenuItem>
                <MenuItem value="Proposal">Proposal</MenuItem>
                <MenuItem value="Won">Won</MenuItem>
                <MenuItem value="Lost">Lost</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Source</InputLabel>
              <Select
                value={sourceFilter}
                onChange={(e: SelectChangeEvent<string>) => setSourceFilter(e.target.value)}
                label="Source"
              >
                <MenuItem value="All">All</MenuItem>
                <MenuItem value="Web">Web</MenuItem>
                <MenuItem value="App">App</MenuItem>
                <MenuItem value="Referral">Referral</MenuItem>
                <MenuItem value="Direct">Direct</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Outlet</InputLabel>
              <Select
                value={outletFilter}
                onChange={(e: SelectChangeEvent<string>) => setOutletFilter(e.target.value)}
                label="Outlet"
              >
                <MenuItem value="All">All</MenuItem>
                {outlets.map(outlet => (
                  <MenuItem key={outlet} value={outlet}>{outlet}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<Download />}
              onClick={handleExport}
              sx={{ backgroundColor: '#84A98C' }}
            >
              Export
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Data Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'customerName'}
                  direction={orderBy === 'customerName' ? order : 'asc'}
                  onClick={() => handleRequestSort('customerName')}
                >
                  Customer
                </TableSortLabel>
              </TableCell>
              <TableCell>Contact</TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'outlet'}
                  direction={orderBy === 'outlet' ? order : 'asc'}
                  onClick={() => handleRequestSort('outlet')}
                >
                  Outlet
                </TableSortLabel>
              </TableCell>
              <TableCell>Location</TableCell>
              <TableCell>Category</TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'estimatedValue'}
                  direction={orderBy === 'estimatedValue' ? order : 'asc'}
                  onClick={() => handleRequestSort('estimatedValue')}
                >
                  Est. Value
                </TableSortLabel>
              </TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Source</TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'dateCreated'}
                  direction={orderBy === 'dateCreated' ? order : 'asc'}
                  onClick={() => handleRequestSort('dateCreated')}
                >
                  Created
                </TableSortLabel>
              </TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedLeads
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((lead) => (
                <TableRow key={lead.id} hover>
                  <TableCell>{lead.customerName}</TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2">{lead.email}</Typography>
                      <Typography variant="body2" color="text.secondary">{lead.phone}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{lead.outlet}</TableCell>
                  <TableCell>{lead.state} {lead.postcode}</TableCell>
                  <TableCell>{lead.category}</TableCell>
                  <TableCell>${lead.estimatedValue.toLocaleString()}</TableCell>
                  <TableCell>
                    <Chip
                      label={lead.status}
                      size="small"
                      sx={{
                        backgroundColor: getStatusColor(lead.status),
                        color: 'white',
                        fontWeight: 'bold',
                      }}
                    />
                  </TableCell>
                  <TableCell>{lead.source}</TableCell>
                  <TableCell>{lead.dateCreated}</TableCell>
                  <TableCell>
                    <IconButton size="small" color="primary">
                      <Visibility />
                    </IconButton>
                    <IconButton size="small" color="primary">
                      <Edit />
                    </IconButton>
                    <IconButton size="small" color="error">
                      <Delete />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={filteredLeads.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>
    </Box>
  );
};

export default LeadsDataPage;
