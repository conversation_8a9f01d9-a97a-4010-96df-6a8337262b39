import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import GaugeChart from '../../components/charts/GaugeChart';
import apiService from '../../services/api';

interface GaugeData {
  label: string;
  value: number;
  budget: number;
  forecast: number;
  color: string;
}

interface ChartData {
  date: string;
  sales: number;
  forecast: number;
  budget: number;
}

const PlotsPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [gaugeData, setGaugeData] = useState<GaugeData[]>([]);
  const [monthlyData, setMonthlyData] = useState<ChartData[]>([]);
  const [shedsData, setShedsData] = useState<any[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError('');

      // Load gauge data
      const gauges: GaugeData[] = [
        {
          label: 'Today',
          value: 45000,
          budget: 50000,
          forecast: 48000,
          color: '#84A98C',
        },
        {
          label: 'Yesterday',
          value: 52000,
          budget: 50000,
          forecast: 48000,
          color: '#52796F',
        },
        {
          label: 'This Week',
          value: 280000,
          budget: 300000,
          forecast: 290000,
          color: '#354F52',
        },
        {
          label: 'Last Week',
          value: 310000,
          budget: 300000,
          forecast: 290000,
          color: '#2F3E46',
        },
      ];
      setGaugeData(gauges);

      // Load monthly data
      const monthly: ChartData[] = [
        { date: 'Jan', sales: 1200000, forecast: 1150000, budget: 1100000 },
        { date: 'Feb', sales: 1350000, forecast: 1300000, budget: 1250000 },
        { date: 'Mar', sales: 1180000, forecast: 1200000, budget: 1150000 },
        { date: 'Apr', sales: 1420000, forecast: 1400000, budget: 1350000 },
        { date: 'May', sales: 1380000, forecast: 1350000, budget: 1300000 },
        { date: 'Jun', sales: 1520000, forecast: 1500000, budget: 1450000 },
      ];
      setMonthlyData(monthly);

      // Load sheds data
      const sheds = [
        { category: 'FDB Sheds', count: 45, value: 450000 },
        { category: 'SB Sheds', count: 32, value: 320000 },
        { category: 'FDB Patios', count: 28, value: 280000 },
        { category: 'Lifestyle', count: 15, value: 150000 },
        { category: 'NZ Sheds', count: 8, value: 80000 },
      ];
      setShedsData(sheds);

    } catch (err: any) {
      setError('Failed to load dashboard data');
      console.error('Dashboard data error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} sx={{ color: '#84A98C' }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  const COLORS = ['#84A98C', '#52796F', '#354F52', '#2F3E46', '#CAD2C5'];

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold', color: '#84A98C' }}>
        Sales Dashboard
      </Typography>

      {/* Gauge Charts Row */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {gaugeData.map((gauge, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Typography variant="h6" align="center" sx={{ mb: 2 }}>
                  {gauge.label}
                </Typography>
                <GaugeChart
                  value={gauge.value}
                  budget={gauge.budget}
                  forecast={gauge.forecast}
                  color={gauge.color}
                />
                <Box sx={{ mt: 2, textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    Actual: ${gauge.value.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Budget: ${gauge.budget.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Forecast: ${gauge.forecast.toLocaleString()}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Charts Row */}
      <Grid container spacing={3}>
        {/* Monthly Sales Chart */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Monthly Sales Performance
              </Typography>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip formatter={(value: number) => `$${value.toLocaleString()}`} />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="sales" 
                    stroke="#84A98C" 
                    strokeWidth={3}
                    name="Actual Sales"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="forecast" 
                    stroke="#52796F" 
                    strokeDasharray="5 5"
                    name="Forecast"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="budget" 
                    stroke="#354F52" 
                    strokeDasharray="10 5"
                    name="Budget"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Sheds Distribution */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Sheds by Category
              </Typography>
              <ResponsiveContainer width="100%" height={400}>
                <PieChart>
                  <Pie
                    data={shedsData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ category, count }) => `${category}: ${count}`}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {shedsData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Number of Sheds Bar Chart */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Sales Value by Category
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={shedsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="category" />
                  <YAxis />
                  <Tooltip formatter={(value: number) => `$${value.toLocaleString()}`} />
                  <Bar dataKey="value" fill="#84A98C" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PlotsPage;
