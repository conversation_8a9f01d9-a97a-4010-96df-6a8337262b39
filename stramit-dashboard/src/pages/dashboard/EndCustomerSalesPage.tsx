import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
} from '@mui/material';
import AustralianMap from '../../components/maps/AustralianMap';
import { AustralianState } from '../../types';

interface CustomerSalesData {
  id: string;
  latitude: number;
  longitude: number;
  salesValue: number;
  frequency: number;
  label: string;
  state: string;
  postcode: string;
  customerCount: number;
}

const EndCustomerSalesPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [mapType, setMapType] = useState<'sales' | 'frequency'>('sales');
  const [selectedState, setSelectedState] = useState<AustralianState>('All');
  const [selectedPostcode, setSelectedPostcode] = useState<string>('');
  const [customerData, setCustomerData] = useState<CustomerSalesData[]>([]);
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    loadCustomerData();
  }, []);

  const loadCustomerData = async () => {
    try {
      setLoading(true);
      setError('');

      // Mock data - replace with actual API call
      const mockData: CustomerSalesData[] = [
        // NSW
        {
          id: '1',
          latitude: -33.8688,
          longitude: 151.2093,
          salesValue: 2500000,
          frequency: 45,
          label: 'Sydney Metro',
          state: 'NSW',
          postcode: '2000',
          customerCount: 120,
        },
        {
          id: '2',
          latitude: -32.9267,
          longitude: 151.7789,
          salesValue: 850000,
          frequency: 18,
          label: 'Newcastle',
          state: 'NSW',
          postcode: '2300',
          customerCount: 35,
        },
        {
          id: '3',
          latitude: -34.4278,
          longitude: 150.8931,
          salesValue: 1200000,
          frequency: 28,
          label: 'Wollongong',
          state: 'NSW',
          postcode: '2500',
          customerCount: 52,
        },
        // VIC
        {
          id: '4',
          latitude: -37.8136,
          longitude: 144.9631,
          salesValue: 2200000,
          frequency: 42,
          label: 'Melbourne Metro',
          state: 'VIC',
          postcode: '3000',
          customerCount: 98,
        },
        {
          id: '5',
          latitude: -38.1499,
          longitude: 144.3617,
          salesValue: 650000,
          frequency: 15,
          label: 'Geelong',
          state: 'VIC',
          postcode: '3220',
          customerCount: 28,
        },
        // QLD
        {
          id: '6',
          latitude: -27.4698,
          longitude: 153.0251,
          salesValue: 1800000,
          frequency: 35,
          label: 'Brisbane Metro',
          state: 'QLD',
          postcode: '4000',
          customerCount: 75,
        },
        {
          id: '7',
          latitude: -16.9186,
          longitude: 145.7781,
          salesValue: 450000,
          frequency: 12,
          label: 'Cairns',
          state: 'QLD',
          postcode: '4870',
          customerCount: 18,
        },
        {
          id: '8',
          latitude: -23.6980,
          longitude: 133.8807,
          salesValue: 320000,
          frequency: 8,
          label: 'Alice Springs',
          state: 'NT',
          postcode: '0870',
          customerCount: 12,
        },
        // SA
        {
          id: '9',
          latitude: -34.9285,
          longitude: 138.6007,
          salesValue: 950000,
          frequency: 22,
          label: 'Adelaide Metro',
          state: 'SA',
          postcode: '5000',
          customerCount: 42,
        },
        // WA
        {
          id: '10',
          latitude: -31.9505,
          longitude: 115.8605,
          salesValue: 1400000,
          frequency: 28,
          label: 'Perth Metro',
          state: 'WA',
          postcode: '6000',
          customerCount: 58,
        },
        // TAS
        {
          id: '11',
          latitude: -42.8821,
          longitude: 147.3272,
          salesValue: 380000,
          frequency: 9,
          label: 'Hobart',
          state: 'TAS',
          postcode: '7000',
          customerCount: 15,
        },
        // ACT
        {
          id: '12',
          latitude: -35.2809,
          longitude: 149.1300,
          salesValue: 720000,
          frequency: 16,
          label: 'Canberra',
          state: 'ACT',
          postcode: '2600',
          customerCount: 28,
        },
      ];

      setCustomerData(mockData);

    } catch (err: any) {
      setError('Failed to load customer data');
      console.error('Customer data error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getMapData = () => {
    return customerData.map(item => ({
      id: item.id,
      latitude: item.latitude,
      longitude: item.longitude,
      value: mapType === 'sales' ? item.salesValue : item.frequency,
      label: item.label,
      state: item.state,
      postcode: item.postcode,
      category: 'customer',
    }));
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setMapType(newValue === 0 ? 'sales' : 'frequency');
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} sx={{ color: '#84A98C' }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold', color: '#84A98C' }}>
        End Customer Sales
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          {/* Tabs for Sales/Frequency */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={tabValue} onChange={handleTabChange}>
              <Tab label="Sales" />
              <Tab label="Frequency" />
            </Tabs>
          </Box>

          {/* Map */}
          <AustralianMap
            data={getMapData()}
            mapType={mapType}
            onMapTypeChange={setMapType}
            selectedState={selectedState}
            onStateChange={setSelectedState}
            selectedPostcode={selectedPostcode}
            onPostcodeChange={setSelectedPostcode}
            title={`End Customer ${mapType === 'sales' ? 'Sales Value' : 'Purchase Frequency'}`}
          />
        </Grid>
      </Grid>

      {/* Summary Statistics */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={3}>
          <Box sx={{ textAlign: 'center', p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="h6" sx={{ color: '#84A98C' }}>
              Total Sales
            </Typography>
            <Typography variant="h4">
              ${customerData.reduce((sum, item) => sum + item.salesValue, 0).toLocaleString()}
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={12} md={3}>
          <Box sx={{ textAlign: 'center', p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="h6" sx={{ color: '#84A98C' }}>
              Total Customers
            </Typography>
            <Typography variant="h4">
              {customerData.reduce((sum, item) => sum + item.customerCount, 0).toLocaleString()}
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={12} md={3}>
          <Box sx={{ textAlign: 'center', p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="h6" sx={{ color: '#84A98C' }}>
              Avg Order Value
            </Typography>
            <Typography variant="h4">
              ${Math.round(
                customerData.reduce((sum, item) => sum + item.salesValue, 0) /
                customerData.reduce((sum, item) => sum + item.frequency, 0)
              ).toLocaleString()}
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={12} md={3}>
          <Box sx={{ textAlign: 'center', p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="h6" sx={{ color: '#84A98C' }}>
              Active Regions
            </Typography>
            <Typography variant="h4">
              {customerData.length}
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EndCustomerSalesPage;
