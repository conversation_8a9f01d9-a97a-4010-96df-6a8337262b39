import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Alert,
} from '@mui/material';
import { Download, Refresh } from '@mui/icons-material';

interface SalesRecord {
  id: string;
  date: string;
  outlet: string;
  category: string;
  customerName: string;
  state: string;
  postcode: string;
  quantity: number;
  unitPrice: number;
  totalValue: number;
  source: 'App' | 'Web';
  status: string;
}

type Order = 'asc' | 'desc';

const DataTablePage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [tabValue, setTabValue] = useState(0);
  const [rawData, setRawData] = useState<SalesRecord[]>([]);
  const [appData, setAppData] = useState<SalesRecord[]>([]);
  const [webData, setWebData] = useState<SalesRecord[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [order, setOrder] = useState<Order>('desc');
  const [orderBy, setOrderBy] = useState<keyof SalesRecord>('date');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError('');

      // Mock data generation
      const generateMockData = (source: 'App' | 'Web' | 'All', count: number): SalesRecord[] => {
        const data: SalesRecord[] = [];
        const outlets = ['Sydney Outlet', 'Melbourne Outlet', 'Brisbane Outlet', 'Adelaide Outlet', 'Perth Outlet'];
        const categories = ['FDB Sheds', 'SB Sheds', 'FDB Patios', 'Lifestyle', 'NZ Sheds'];
        const states = ['NSW', 'VIC', 'QLD', 'SA', 'WA'];
        const statuses = ['Completed', 'Processing', 'Shipped', 'Delivered'];

        for (let i = 1; i <= count; i++) {
          const recordSource = source === 'All' ? (Math.random() > 0.5 ? 'App' : 'Web') : source;
          const quantity = Math.floor(Math.random() * 5) + 1;
          const unitPrice = Math.floor(Math.random() * 50000) + 30000;
          
          data.push({
            id: `${source}-${i}`,
            date: `2024-01-${Math.floor(Math.random() * 28) + 1}`,
            outlet: outlets[Math.floor(Math.random() * outlets.length)],
            category: categories[Math.floor(Math.random() * categories.length)],
            customerName: `Customer ${i}`,
            state: states[Math.floor(Math.random() * states.length)],
            postcode: (Math.floor(Math.random() * 9000) + 1000).toString(),
            quantity,
            unitPrice,
            totalValue: quantity * unitPrice,
            source: recordSource,
            status: statuses[Math.floor(Math.random() * statuses.length)],
          });
        }
        return data;
      };

      const rawMockData = generateMockData('All', 100);
      const appMockData = rawMockData.filter(record => record.source === 'App');
      const webMockData = rawMockData.filter(record => record.source === 'Web');

      setRawData(rawMockData);
      setAppData(appMockData);
      setWebData(webMockData);

    } catch (err: any) {
      setError('Failed to load data');
      console.error('Data loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getCurrentData = (): SalesRecord[] => {
    switch (tabValue) {
      case 0: return rawData;
      case 1: return appData;
      case 2: return webData;
      default: return rawData;
    }
  };

  const getFilteredData = (): SalesRecord[] => {
    const data = getCurrentData();
    if (!searchTerm) return data;

    return data.filter(record =>
      record.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.outlet.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.state.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const handleRequestSort = (property: keyof SalesRecord) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setPage(0);
  };

  const handleExport = () => {
    const data = getFilteredData();
    const csvContent = [
      // Header
      'Date,Outlet,Category,Customer,State,Postcode,Quantity,Unit Price,Total Value,Source,Status',
      // Data rows
      ...data.map(record => 
        `${record.date},${record.outlet},${record.category},${record.customerName},${record.state},${record.postcode},${record.quantity},${record.unitPrice},${record.totalValue},${record.source},${record.status}`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sales-data-${['raw', 'app', 'web'][tabValue]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const sortedData = React.useMemo(() => {
    const filtered = getFilteredData();
    return filtered.sort((a, b) => {
      if (orderBy === 'quantity' || orderBy === 'unitPrice' || orderBy === 'totalValue') {
        return order === 'asc' ? a[orderBy] - b[orderBy] : b[orderBy] - a[orderBy];
      }
      const aValue = a[orderBy].toString();
      const bValue = b[orderBy].toString();
      return order === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });
  }, [getFilteredData(), order, orderBy]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} sx={{ color: '#84A98C' }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  const currentData = getCurrentData();
  const filteredData = getFilteredData();

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold', color: '#84A98C' }}>
        Data Table
      </Typography>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label={`Raw Data (${rawData.length})`} />
          <Tab label={`App Total (${appData.length})`} />
          <Tab label={`Web Total (${webData.length})`} />
        </Tabs>
      </Box>

      {/* Summary Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h6" color="text.secondary">Total Records</Typography>
            <Typography variant="h4" sx={{ color: '#84A98C' }}>
              {currentData.length}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h6" color="text.secondary">Total Value</Typography>
            <Typography variant="h4" sx={{ color: '#84A98C' }}>
              ${currentData.reduce((sum, record) => sum + record.totalValue, 0).toLocaleString()}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h6" color="text.secondary">Avg Order Value</Typography>
            <Typography variant="h4" sx={{ color: '#84A98C' }}>
              ${Math.round(currentData.reduce((sum, record) => sum + record.totalValue, 0) / currentData.length).toLocaleString()}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h6" color="text.secondary">Total Quantity</Typography>
            <Typography variant="h4" sx={{ color: '#84A98C' }}>
              {currentData.reduce((sum, record) => sum + record.quantity, 0)}
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Controls */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              size="small"
              label="Search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Refresh />}
              onClick={loadData}
            >
              Refresh
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<Download />}
              onClick={handleExport}
              sx={{ backgroundColor: '#84A98C' }}
            >
              Export CSV
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Data Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'date'}
                  direction={orderBy === 'date' ? order : 'asc'}
                  onClick={() => handleRequestSort('date')}
                >
                  Date
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'outlet'}
                  direction={orderBy === 'outlet' ? order : 'asc'}
                  onClick={() => handleRequestSort('outlet')}
                >
                  Outlet
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'category'}
                  direction={orderBy === 'category' ? order : 'asc'}
                  onClick={() => handleRequestSort('category')}
                >
                  Category
                </TableSortLabel>
              </TableCell>
              <TableCell>Customer</TableCell>
              <TableCell>Location</TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'quantity'}
                  direction={orderBy === 'quantity' ? order : 'asc'}
                  onClick={() => handleRequestSort('quantity')}
                >
                  Qty
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'unitPrice'}
                  direction={orderBy === 'unitPrice' ? order : 'asc'}
                  onClick={() => handleRequestSort('unitPrice')}
                >
                  Unit Price
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'totalValue'}
                  direction={orderBy === 'totalValue' ? order : 'asc'}
                  onClick={() => handleRequestSort('totalValue')}
                >
                  Total Value
                </TableSortLabel>
              </TableCell>
              <TableCell>Source</TableCell>
              <TableCell>Status</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedData
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((record) => (
                <TableRow key={record.id} hover>
                  <TableCell>{record.date}</TableCell>
                  <TableCell>{record.outlet}</TableCell>
                  <TableCell>{record.category}</TableCell>
                  <TableCell>{record.customerName}</TableCell>
                  <TableCell>{record.state} {record.postcode}</TableCell>
                  <TableCell>{record.quantity}</TableCell>
                  <TableCell>${record.unitPrice.toLocaleString()}</TableCell>
                  <TableCell>${record.totalValue.toLocaleString()}</TableCell>
                  <TableCell>
                    <Box
                      sx={{
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        backgroundColor: record.source === 'App' ? '#84A98C' : '#52796F',
                        color: 'white',
                        fontSize: '0.75rem',
                        textAlign: 'center',
                        minWidth: '50px',
                      }}
                    >
                      {record.source}
                    </Box>
                  </TableCell>
                  <TableCell>{record.status}</TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={filteredData.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>
    </Box>
  );
};

export default DataTablePage;
