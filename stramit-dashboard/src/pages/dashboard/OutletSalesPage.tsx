import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  CircularProgress,
  Alert,
} from '@mui/material';
import AustralianMap from '../../components/maps/AustralianMap';
import { AustralianState, AUSTRALIAN_STATES } from '../../types';

interface OutletData {
  id: string;
  latitude: number;
  longitude: number;
  value: number;
  label: string;
  state: string;
  postcode: string;
  distributorId: string;
  area: string;
}

const OutletSalesPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [mapType, setMapType] = useState<'sales' | 'frequency'>('sales');
  const [selectedState, setSelectedState] = useState<AustralianState>('All');
  const [selectedPostcode, setSelectedPostcode] = useState<string>('');
  const [selectedArea, setSelectedArea] = useState<string>('All');
  const [outletData, setOutletData] = useState<OutletData[]>([]);
  const [areas, setAreas] = useState<string[]>([]);

  useEffect(() => {
    loadOutletData();
  }, []);

  const loadOutletData = async () => {
    try {
      setLoading(true);
      setError('');

      // Mock data - replace with actual API call
      const mockData: OutletData[] = [
        {
          id: '1',
          latitude: -33.8688,
          longitude: 151.2093,
          value: 150000,
          label: 'Sydney Outlet',
          state: 'NSW',
          postcode: '2000',
          distributorId: 'DIST001',
          area: 'Metro Sydney',
        },
        {
          id: '2',
          latitude: -37.8136,
          longitude: 144.9631,
          value: 120000,
          label: 'Melbourne Outlet',
          state: 'VIC',
          postcode: '3000',
          distributorId: 'DIST002',
          area: 'Metro Melbourne',
        },
        {
          id: '3',
          latitude: -27.4698,
          longitude: 153.0251,
          value: 95000,
          label: 'Brisbane Outlet',
          state: 'QLD',
          postcode: '4000',
          distributorId: 'DIST003',
          area: 'Metro Brisbane',
        },
        {
          id: '4',
          latitude: -34.9285,
          longitude: 138.6007,
          value: 85000,
          label: 'Adelaide Outlet',
          state: 'SA',
          postcode: '5000',
          distributorId: 'DIST004',
          area: 'Metro Adelaide',
        },
        {
          id: '5',
          latitude: -31.9505,
          longitude: 115.8605,
          value: 110000,
          label: 'Perth Outlet',
          state: 'WA',
          postcode: '6000',
          distributorId: 'DIST005',
          area: 'Metro Perth',
        },
        {
          id: '6',
          latitude: -42.8821,
          longitude: 147.3272,
          value: 45000,
          label: 'Hobart Outlet',
          state: 'TAS',
          postcode: '7000',
          distributorId: 'DIST006',
          area: 'Tasmania',
        },
        {
          id: '7',
          latitude: -12.4634,
          longitude: 130.8456,
          value: 35000,
          label: 'Darwin Outlet',
          state: 'NT',
          postcode: '0800',
          distributorId: 'DIST007',
          area: 'Northern Territory',
        },
        {
          id: '8',
          latitude: -35.2809,
          longitude: 149.1300,
          value: 65000,
          label: 'Canberra Outlet',
          state: 'ACT',
          postcode: '2600',
          distributorId: 'DIST008',
          area: 'ACT',
        },
        // Additional regional outlets
        {
          id: '9',
          latitude: -32.9267,
          longitude: 151.7789,
          value: 75000,
          label: 'Newcastle Outlet',
          state: 'NSW',
          postcode: '2300',
          distributorId: 'DIST009',
          area: 'Hunter Valley',
        },
        {
          id: '10',
          latitude: -38.1499,
          longitude: 144.3617,
          value: 55000,
          label: 'Geelong Outlet',
          state: 'VIC',
          postcode: '3220',
          distributorId: 'DIST010',
          area: 'Geelong Region',
        },
      ];

      setOutletData(mockData);

      // Extract unique areas
      const uniqueAreas = Array.from(new Set(mockData.map(item => item.area))).sort();
      setAreas(uniqueAreas);

    } catch (err: any) {
      setError('Failed to load outlet data');
      console.error('Outlet data error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAreaChange = (event: SelectChangeEvent<string>) => {
    setSelectedArea(event.target.value);
  };

  const getFilteredData = () => {
    let filtered = outletData;

    if (selectedArea !== 'All') {
      filtered = filtered.filter(item => item.area === selectedArea);
    }

    // Convert to map data format
    return filtered.map(item => ({
      id: item.id,
      latitude: item.latitude,
      longitude: item.longitude,
      value: mapType === 'sales' ? item.value : Math.floor(item.value / 10000), // Mock frequency
      label: item.label,
      state: item.state,
      postcode: item.postcode,
      category: item.area,
    }));
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} sx={{ color: '#84A98C' }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold', color: '#84A98C' }}>
        Outlet Sales
      </Typography>

      <Grid container spacing={3}>
        {/* Area Filter */}
        <Grid item xs={12} md={3}>
          <FormControl fullWidth>
            <InputLabel>Area</InputLabel>
            <Select
              value={selectedArea}
              onChange={handleAreaChange}
              label="Area"
            >
              <MenuItem value="All">All Areas</MenuItem>
              {areas.map((area) => (
                <MenuItem key={area} value={area}>
                  {area}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Map */}
        <Grid item xs={12}>
          <AustralianMap
            data={getFilteredData()}
            mapType={mapType}
            onMapTypeChange={setMapType}
            selectedState={selectedState}
            onStateChange={setSelectedState}
            selectedPostcode={selectedPostcode}
            onPostcodeChange={setSelectedPostcode}
            title="Outlet Sales Distribution"
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default OutletSalesPage;
