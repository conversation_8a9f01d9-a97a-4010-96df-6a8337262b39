import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Alert,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  TrendingUp,
  Analytics,
  Timeline,
  Assessment,
  Warning,
  Info,
  CheckCircle,
  Schedule,
} from '@mui/icons-material';

const ForecastingPage: React.FC = () => {
  const developmentFeatures = [
    {
      name: 'Sales Forecasting Model',
      status: 'In Development',
      progress: 65,
      description: 'Machine learning model for predicting future sales based on historical data',
      icon: <TrendingUp />,
      color: '#FFB300',
    },
    {
      name: 'Demand Prediction',
      status: 'Planning',
      progress: 25,
      description: 'Predictive analytics for inventory and demand planning',
      icon: <Analytics />,
      color: '#84A98C',
    },
    {
      name: 'Seasonal Analysis',
      status: 'In Development',
      progress: 45,
      description: 'Analysis of seasonal trends and patterns in sales data',
      icon: <Timeline />,
      color: '#FFB300',
    },
    {
      name: 'Performance Metrics',
      status: 'Completed',
      progress: 100,
      description: 'Key performance indicators and forecasting accuracy metrics',
      icon: <Assessment />,
      color: '#4CAF50',
    },
  ];

  const upcomingFeatures = [
    'Advanced time series forecasting with ARIMA models',
    'Integration with external market data sources',
    'Automated forecast alerts and notifications',
    'Custom forecasting parameters and scenarios',
    'Export forecasting reports to PDF/Excel',
    'Real-time forecast updates based on latest sales data',
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return '#4CAF50';
      case 'In Development': return '#FFB300';
      case 'Planning': return '#84A98C';
      default: return '#84A98C';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed': return <CheckCircle />;
      case 'In Development': return <Schedule />;
      case 'Planning': return <Info />;
      default: return <Info />;
    }
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold', color: '#84A98C' }}>
        FORECASTING
      </Typography>

      {/* Development Notice */}
      <Alert 
        severity="info" 
        sx={{ 
          mb: 3,
          backgroundColor: '#EBEBE4',
          '& .MuiAlert-icon': {
            color: '#FFB300',
          },
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
          🚧 Development in Progress
        </Typography>
        <Typography>
          The forecasting module is currently under active development. Advanced predictive analytics 
          and machine learning models are being implemented to provide accurate sales forecasting capabilities.
        </Typography>
      </Alert>

      {/* Development Progress */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {developmentFeatures.map((feature, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card 
              sx={{ 
                height: '100%',
                backgroundColor: feature.status === 'In Development' ? '#EBEBE4' : 'white',
                border: feature.status === 'In Development' ? '2px solid #FFB300' : '1px solid #e0e0e0',
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ color: feature.color, mr: 2 }}>
                    {feature.icon}
                  </Box>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      {feature.name}
                    </Typography>
                    <Chip
                      icon={getStatusIcon(feature.status)}
                      label={feature.status}
                      size="small"
                      sx={{
                        backgroundColor: getStatusColor(feature.status),
                        color: 'white',
                        fontWeight: 'bold',
                        mt: 1,
                      }}
                    />
                  </Box>
                </Box>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {feature.description}
                </Typography>
                
                <Box sx={{ mb: 1 }}>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    Progress: {feature.progress}%
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={feature.progress}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: '#e0e0e0',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: feature.color,
                        borderRadius: 4,
                      },
                    }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Upcoming Features */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', color: '#84A98C' }}>
                📋 Planned Features
              </Typography>
              <List dense>
                {upcomingFeatures.map((feature, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemIcon>
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: '#84A98C',
                          }}
                        />
                      </ListItemIcon>
                      <ListItemText primary={feature} />
                    </ListItem>
                    {index < upcomingFeatures.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', color: '#84A98C' }}>
                📊 Current Capabilities
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle sx={{ color: '#4CAF50' }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Historical Sales Analysis"
                    secondary="View and analyze past sales performance"
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle sx={{ color: '#4CAF50' }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Trend Visualization"
                    secondary="Interactive charts showing sales trends"
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle sx={{ color: '#4CAF50' }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Performance Metrics"
                    secondary="Key performance indicators and benchmarks"
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <Warning sx={{ color: '#FFB300' }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Predictive Models"
                    secondary="Advanced forecasting algorithms (in development)"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Contact Information */}
      <Card sx={{ mt: 3, backgroundColor: '#f5f5f5' }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', color: '#84A98C' }}>
            💬 Development Updates
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            We're actively working on implementing advanced forecasting capabilities. 
            Expected completion for the initial forecasting module is targeted for Q2 2024.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            For questions about forecasting features or to request specific functionality, 
            please contact the development team. We appreciate your patience as we build 
            these advanced analytics capabilities.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ForecastingPage;
