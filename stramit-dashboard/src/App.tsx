import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import DashboardLayout from './components/Layout/DashboardLayout';
import Login from './pages/Login';
import Register from './pages/Register';

// Import dashboard pages (to be created)
import PlotsPage from './pages/dashboard/PlotsPage';
import OutletSalesPage from './pages/dashboard/OutletSalesPage';
import EndCustomerSalesPage from './pages/dashboard/EndCustomerSalesPage';
import LeadMapPage from './pages/dashboard/LeadMapPage';
import LeadsDataPage from './pages/dashboard/LeadsDataPage';
import ForecastingPage from './pages/dashboard/ForecastingPage';
import DataTablePage from './pages/dashboard/DataTablePage';

// Create custom theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#84A98C',
    },
    secondary: {
      main: '#EBEBE4',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <AuthProvider>
          <Router>
            <Routes>
              {/* Public routes */}
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />

              {/* Protected dashboard routes */}
              <Route path="/dashboard/*" element={
                <ProtectedRoute>
                  <DashboardLayout>
                    <Routes>
                      <Route path="plots" element={<PlotsPage />} />
                      <Route path="outlet-sales" element={<OutletSalesPage />} />
                      <Route path="end-customer-sales" element={<EndCustomerSalesPage />} />
                      <Route path="lead-map" element={<LeadMapPage />} />
                      <Route path="leads-data" element={<LeadsDataPage />} />
                      <Route path="forecasting" element={<ForecastingPage />} />
                      <Route path="data-table" element={<DataTablePage />} />
                      <Route path="" element={<Navigate to="plots" replace />} />
                    </Routes>
                  </DashboardLayout>
                </ProtectedRoute>
              } />

              {/* Default redirect */}
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="*" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </Router>
        </AuthProvider>
      </LocalizationProvider>
    </ThemeProvider>
  );
}

export default App;
