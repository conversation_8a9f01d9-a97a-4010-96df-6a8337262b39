// Core data types based on R Shiny application

export interface User {
  username: string;
  role: string;
}

export interface AuthToken {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  password: string;
  role?: string;
}

// Sales data types
export interface SalesData {
  id: string;
  StatusDate: string;
  Ammount: number;
  PaymentTo: string;
  NetworkID: string;
  Category: string;
  dist_id: string;
  OrderIdCode: string;
  CustomerPostcode?: string;
  CustomerState?: string;
}

// Distributor data types
export interface DistributorData {
  DistributorshipID: string;
  area: string;
  latitude: number;
  longitude: number;
  name?: string;
}

// Postcode data types
export interface PostcodeData {
  postcode: string;
  locality: string;
  state: string;
  Lat_precise: number;
  Long_precise: number;
}

// Lead data types
export interface LeadData {
  id: string;
  LeadSource: string;
  xml_program_name: string;
  LeadCategory: string;
  postcode?: string;
  state?: string;
  status?: string;
  created_date: string;
}

// Chart data types
export interface GaugeData {
  value: number;
  min: number;
  max: number;
  label: string;
  budget?: number;
  forecast?: number;
}

export interface ChartDataPoint {
  date: string;
  value: number;
  category?: string;
}

// Filter types
export interface DateRange {
  start: Date;
  end: Date;
}

export interface FilterState {
  categories: string[];
  unitPrice: number;
  dateRange: DateRange;
  selectedArea: string;
  selectedDistributor: string;
  selectedPostcode: string;
  selectedState: string;
}

// API response types
export interface APIResponse<T> {
  data: T;
  message?: string;
  status: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}

// Map types
export interface MapMarker {
  id: string;
  latitude: number;
  longitude: number;
  value: number;
  label: string;
  category?: string;
}

export interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

// Export types
export interface ExportOptions {
  format: 'csv' | 'excel' | 'json';
  filename?: string;
  includeHeaders?: boolean;
}

// Dashboard state types
export interface DashboardState {
  loading: boolean;
  error: string | null;
  filters: FilterState;
  salesData: SalesData[];
  leadsData: LeadData[];
  distributorData: DistributorData[];
  postcodeData: PostcodeData[];
}

// Tab types matching R Shiny structure
export type TabType = 
  | 'plots' 
  | 'outlet-sales' 
  | 'end-customer-sales' 
  | 'lead-map' 
  | 'leads-data' 
  | 'forecasting' 
  | 'data-table';

export type MapType = 'sales' | 'frequency';

// Australian states
export const AUSTRALIAN_STATES = [
  'All',
  'NSW',
  'VIC', 
  'QLD',
  'SA',
  'WA',
  'TAS',
  'NT',
  'ACT'
] as const;

export type AustralianState = typeof AUSTRALIAN_STATES[number];

// Categories matching R application
export const CATEGORIES = [
  'FDB Sheds',
  'SB Sheds', 
  'FDB Patios',
  'Lifestyle',
  'NZ Sheds'
] as const;

export type Category = typeof CATEGORIES[number];

// Lead sources
export const LEAD_SOURCES = [
  'designer.ios',
  'designer.android', 
  'designer.web'
] as const;

export type LeadSource = typeof LEAD_SOURCES[number];
