import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, useMap } from 'react-leaflet';
import { LatLngBounds, LatLng } from 'leaflet';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  Card,
  CardContent,
  SelectChangeEvent,
} from '@mui/material';
import { AUSTRALIAN_STATES, AustralianState } from '../../types';

interface MapData {
  id: string;
  latitude: number;
  longitude: number;
  value: number;
  label: string;
  state: string;
  postcode?: string;
  category?: string;
}

interface AustralianMapProps {
  data: MapData[];
  mapType: 'sales' | 'frequency';
  onMapTypeChange: (type: 'sales' | 'frequency') => void;
  selectedState: AustralianState;
  onStateChange: (state: AustralianState) => void;
  selectedPostcode: string;
  onPostcodeChange: (postcode: string) => void;
  title: string;
}

// Australian bounds
const AUSTRALIA_BOUNDS = new LatLngBounds(
  new LatLng(-44, 113), // Southwest
  new LatLng(-10, 154)  // Northeast
);

const AUSTRALIA_CENTER: [number, number] = [-25.2744, 133.7751];

// State centers for zooming
const STATE_CENTERS: Record<string, [number, number]> = {
  'NSW': [-31.2532, 146.9211],
  'VIC': [-37.4713, 144.7852],
  'QLD': [-20.9176, 142.7028],
  'SA': [-30.0002, 136.2092],
  'WA': [-25.0424, 121.6412],
  'TAS': [-41.6368, 145.5780],
  'NT': [-12.4634, 130.8456],
  'ACT': [-35.4735, 149.0124],
};

const MapController: React.FC<{ selectedState: AustralianState }> = ({ selectedState }) => {
  const map = useMap();

  useEffect(() => {
    if (selectedState === 'All') {
      map.fitBounds(AUSTRALIA_BOUNDS);
    } else if (STATE_CENTERS[selectedState]) {
      map.setView(STATE_CENTERS[selectedState], 7);
    }
  }, [selectedState, map]);

  return null;
};

const AustralianMap: React.FC<AustralianMapProps> = ({
  data,
  mapType,
  onMapTypeChange,
  selectedState,
  onStateChange,
  selectedPostcode,
  onPostcodeChange,
  title,
}) => {
  const [filteredData, setFilteredData] = useState<MapData[]>([]);
  const [postcodes, setPostcodes] = useState<string[]>([]);

  useEffect(() => {
    // Filter data based on selected state
    let filtered = data;
    if (selectedState !== 'All') {
      filtered = data.filter(item => item.state === selectedState);
    }

    // Filter by postcode if selected
    if (selectedPostcode && selectedPostcode !== 'All') {
      filtered = filtered.filter(item => item.postcode === selectedPostcode);
    }

    setFilteredData(filtered);

    // Update available postcodes
    const availablePostcodes = Array.from(
      new Set(filtered.map(item => item.postcode).filter(Boolean))
    ).sort();
    setPostcodes(availablePostcodes);
  }, [data, selectedState, selectedPostcode]);

  const handleStateChange = (event: SelectChangeEvent<AustralianState>) => {
    const newState = event.target.value as AustralianState;
    onStateChange(newState);
    onPostcodeChange(''); // Reset postcode when state changes
  };

  const handlePostcodeChange = (event: SelectChangeEvent<string>) => {
    onPostcodeChange(event.target.value);
  };

  const getMarkerColor = (value: number): string => {
    const maxValue = Math.max(...filteredData.map(d => d.value));
    const intensity = value / maxValue;
    
    if (intensity > 0.8) return '#2F3E46';
    if (intensity > 0.6) return '#354F52';
    if (intensity > 0.4) return '#52796F';
    if (intensity > 0.2) return '#84A98C';
    return '#CAD2C5';
  };

  const getMarkerSize = (value: number): number => {
    const maxValue = Math.max(...filteredData.map(d => d.value));
    const minSize = 5;
    const maxSize = 25;
    const intensity = value / maxValue;
    return minSize + (maxSize - minSize) * intensity;
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            {title}
          </Typography>
          
          {/* Controls */}
          <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>State</InputLabel>
              <Select
                value={selectedState}
                onChange={handleStateChange}
                label="State"
              >
                {AUSTRALIAN_STATES.map((state) => (
                  <MenuItem key={state} value={state}>
                    {state}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Postcode</InputLabel>
              <Select
                value={selectedPostcode}
                onChange={handlePostcodeChange}
                label="Postcode"
              >
                <MenuItem value="">All</MenuItem>
                {postcodes.map((postcode) => (
                  <MenuItem key={postcode} value={postcode}>
                    {postcode}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <ToggleButtonGroup
              value={mapType}
              exclusive
              onChange={(_, newType) => newType && onMapTypeChange(newType)}
              size="small"
            >
              <ToggleButton value="sales">Sales</ToggleButton>
              <ToggleButton value="frequency">Frequency</ToggleButton>
            </ToggleButtonGroup>
          </Box>
        </Box>

        {/* Map */}
        <Box sx={{ height: 600, width: '100%' }}>
          <MapContainer
            center={AUSTRALIA_CENTER}
            zoom={5}
            style={{ height: '100%', width: '100%' }}
            bounds={AUSTRALIA_BOUNDS}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            
            <MapController selectedState={selectedState} />
            
            {filteredData.map((point) => (
              <CircleMarker
                key={point.id}
                center={[point.latitude, point.longitude]}
                radius={getMarkerSize(point.value)}
                fillColor={getMarkerColor(point.value)}
                color="#fff"
                weight={2}
                opacity={1}
                fillOpacity={0.8}
              >
                <Popup>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                      {point.label}
                    </Typography>
                    <Typography variant="body2">
                      {mapType === 'sales' ? 'Sales' : 'Frequency'}: {point.value.toLocaleString()}
                    </Typography>
                    {point.postcode && (
                      <Typography variant="body2">
                        Postcode: {point.postcode}
                      </Typography>
                    )}
                    <Typography variant="body2">
                      State: {point.state}
                    </Typography>
                  </Box>
                </Popup>
              </CircleMarker>
            ))}
          </MapContainer>
        </Box>

        {/* Legend */}
        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2">Intensity:</Typography>
          {['#CAD2C5', '#84A98C', '#52796F', '#354F52', '#2F3E46'].map((color, index) => (
            <Box
              key={index}
              sx={{
                width: 20,
                height: 20,
                backgroundColor: color,
                border: '1px solid #ccc',
                borderRadius: '50%',
              }}
            />
          ))}
          <Typography variant="body2" sx={{ ml: 1 }}>
            Low → High
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default AustralianMap;
