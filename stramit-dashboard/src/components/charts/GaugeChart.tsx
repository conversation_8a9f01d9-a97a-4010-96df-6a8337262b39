import React from 'react';
import { Box } from '@mui/material';

interface GaugeChartProps {
  value: number;
  budget: number;
  forecast: number;
  color: string;
  size?: number;
}

const GaugeChart: React.FC<GaugeChartProps> = ({
  value,
  budget,
  forecast,
  color,
  size = 150,
}) => {
  const maxValue = Math.max(value, budget, forecast) * 1.2;
  const valuePercentage = (value / maxValue) * 100;
  const budgetPercentage = (budget / maxValue) * 100;
  const forecastPercentage = (forecast / maxValue) * 100;

  const radius = size / 2 - 10;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = `${circumference} ${circumference}`;
  
  const valueOffset = circumference - (valuePercentage / 100) * circumference;
  const budgetOffset = circumference - (budgetPercentage / 100) * circumference;
  const forecastOffset = circumference - (forecastPercentage / 100) * circumference;

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
      }}
    >
      <svg width={size} height={size}>
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke="#e0e0e0"
          strokeWidth="8"
        />
        
        {/* Budget line (dashed) */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke="#ccc"
          strokeWidth="4"
          strokeDasharray="5,5"
          strokeDashoffset={budgetOffset}
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
          style={{
            transition: 'stroke-dashoffset 0.5s ease-in-out',
          }}
        />
        
        {/* Forecast line (dotted) */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius - 6}
          fill="none"
          stroke="#999"
          strokeWidth="2"
          strokeDasharray="2,2"
          strokeDashoffset={forecastOffset}
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
          style={{
            transition: 'stroke-dashoffset 0.5s ease-in-out',
          }}
        />
        
        {/* Actual value */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke={color}
          strokeWidth="8"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={valueOffset}
          strokeLinecap="round"
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
          style={{
            transition: 'stroke-dashoffset 0.5s ease-in-out',
          }}
        />
        
        {/* Center text */}
        <text
          x={size / 2}
          y={size / 2 - 5}
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize="14"
          fontWeight="bold"
          fill={color}
        >
          ${(value / 1000).toFixed(0)}K
        </text>
        
        {/* Percentage text */}
        <text
          x={size / 2}
          y={size / 2 + 15}
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize="12"
          fill="#666"
        >
          {((value / budget) * 100).toFixed(0)}%
        </text>
      </svg>
    </Box>
  );
};

export default GaugeChart;
