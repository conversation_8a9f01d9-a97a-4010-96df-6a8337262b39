import React, { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Chip,
  Stack,
  SelectChangeEvent,
} from '@mui/material';
import {
  Dashboard,
  Store,
  People,
  LocationOn,
  TableChart,
  TrendingUp,
  Download,
  Refresh,
  DataUsage,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useNavigate, useLocation } from 'react-router-dom';
import { CATEGORIES, AUSTRALIAN_STATES, Category, AustralianState } from '../../types';

interface SidebarProps {
  onMobileClose?: () => void;
}

const navigationItems = [
  { id: 'plots', label: 'Plots', icon: Dashboard, path: '/dashboard/plots' },
  { id: 'outlet-sales', label: 'Outlet Sales', icon: Store, path: '/dashboard/outlet-sales' },
  { id: 'end-customer-sales', label: 'End Customer Sales', icon: People, path: '/dashboard/end-customer-sales' },
  { id: 'lead-map', label: 'Lead Map', icon: LocationOn, path: '/dashboard/lead-map' },
  { id: 'leads-data', label: 'Leads Data by Outlet', icon: TableChart, path: '/dashboard/leads-data' },
  { id: 'forecasting', label: 'FORECASTING', icon: TrendingUp, path: '/dashboard/forecasting' },
  { id: 'data-table', label: 'Data Table', icon: DataUsage, path: '/dashboard/data-table' },
];

const Sidebar: React.FC<SidebarProps> = ({ onMobileClose }) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Filter state
  const [selectedCategories, setSelectedCategories] = useState<Category[]>(['FDB Sheds', 'SB Sheds']);
  const [unitPrice, setUnitPrice] = useState<number>(286);
  const [startDate, setStartDate] = useState<Date>(new Date(Date.now() - 360 * 24 * 60 * 60 * 1000));
  const [endDate, setEndDate] = useState<Date>(new Date());

  const handleNavigation = (path: string) => {
    navigate(path);
    if (onMobileClose) {
      onMobileClose();
    }
  };

  const handleCategoryChange = (event: SelectChangeEvent<Category[]>) => {
    const value = event.target.value;
    setSelectedCategories(typeof value === 'string' ? value.split(',') as Category[] : value);
  };

  const handleRefresh = () => {
    // Trigger data refresh
    console.log('Refreshing data...');
  };

  const handleLoadData = () => {
    // Trigger data load
    console.log('Loading data...');
  };

  const handleDownload = () => {
    // Trigger data download
    console.log('Downloading data...');
  };

  const isLeadsDataTab = location.pathname.includes('leads-data');

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Logo/Title */}
        <Toolbar>
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#84A98C' }}>
            Visualisations
          </Typography>
        </Toolbar>
        
        <Divider />

        {/* Navigation */}
        <List sx={{ flexGrow: 1 }}>
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            const isForecastingTab = item.id === 'forecasting';
            
            return (
              <ListItem key={item.id} disablePadding>
                <ListItemButton
                  onClick={() => handleNavigation(item.path)}
                  sx={{
                    backgroundColor: isActive ? '#84A98C' : isForecastingTab ? '#EBEBE4' : 'transparent',
                    color: isActive ? 'white' : isForecastingTab ? 'black' : 'inherit',
                    fontWeight: 'bold',
                    '&:hover': {
                      backgroundColor: isForecastingTab ? '#FFB300' : '#84A98C',
                      color: 'white',
                    },
                  }}
                >
                  <ListItemIcon sx={{ color: 'inherit' }}>
                    <Icon />
                  </ListItemIcon>
                  <ListItemText 
                    primary={item.label} 
                    primaryTypographyProps={{ fontWeight: 'bold' }}
                  />
                </ListItemButton>
              </ListItem>
            );
          })}
        </List>

        <Divider />

        {/* Filters */}
        <Box sx={{ p: 2 }}>
          {!isLeadsDataTab ? (
            // Standard filters for all tabs except Leads Data
            <Stack spacing={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Reporting Category</InputLabel>
                <Select
                  multiple
                  value={selectedCategories}
                  onChange={handleCategoryChange}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {CATEGORIES.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField
                fullWidth
                size="small"
                label="Average Selling Price"
                type="number"
                value={unitPrice}
                onChange={(e) => setUnitPrice(Number(e.target.value))}
                inputProps={{ min: 200, max: 500, step: 1 }}
              />

              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => newValue && setStartDate(newValue)}
                slotProps={{ textField: { size: 'small', fullWidth: true } }}
              />

              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => newValue && setEndDate(newValue)}
                slotProps={{ textField: { size: 'small', fullWidth: true } }}
              />

              <Button
                fullWidth
                variant="outlined"
                startIcon={<Refresh />}
                onClick={handleRefresh}
                size="small"
              >
                Refresh
              </Button>

              <Button
                fullWidth
                variant="outlined"
                startIcon={<DataUsage />}
                onClick={handleLoadData}
                size="small"
              >
                Load Data
              </Button>

              <Button
                fullWidth
                variant="contained"
                startIcon={<Download />}
                onClick={handleDownload}
                size="small"
                sx={{ backgroundColor: '#84A98C' }}
              >
                Download
              </Button>
            </Stack>
          ) : (
            // Special filters for Leads Data by Outlet tab
            <Stack spacing={2}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => newValue && setStartDate(newValue)}
                slotProps={{ textField: { size: 'small', fullWidth: true } }}
              />

              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => newValue && setEndDate(newValue)}
                slotProps={{ textField: { size: 'small', fullWidth: true } }}
              />

              <Button
                fullWidth
                variant="contained"
                startIcon={<Download />}
                onClick={handleDownload}
                size="small"
                sx={{ backgroundColor: '#84A98C' }}
              >
                Download All Data
              </Button>
            </Stack>
          )}
        </Box>
      </Box>
    </LocalizationProvider>
  );
};

export default Sidebar;
