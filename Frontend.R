library(shiny)
library(shinyjs)
library(leaflet)
library(ggiraph)
library(DT)
library(bs4Dash)
library(plotly)
library(flexdashboard)

# Define UI
ui <- page_sidebar(
  title = tags$div(
    "StramitDashy",
    tags$img(
      src = "https://www.fairdinkumbuilds.com.au/themes/fds/dist/images/standard/logo-animated.gif",
      height = "60px", 
      width = "60px",
      style = "display: inline-block; margin-left: 10px; vertical-align: middle;"
    )
  ),
  
  
  tags$head(
    tags$style(HTML("
    #mainTab .nav-link { font-weight: bold; color:black }
    #mainTab .nav-link[data-value='Plots'] { background-color: #84A98C; }
    #mainTab .nav-link[data-value='Outlet Sales'] { background-color: #84A98C; }
    #mainTab .nav-link[data-value='End Customer Sales'] { background-color: #84A98C; }
    #mainTab .nav-link[data-value='Lead Map'] { background-color: #84A98C; }
    #mainTab .nav-link[data-value='Leads Data by Outlet'] { background-color: #84A98C; }
    #mainTab .nav-link[data-value='Data Table'] { background-color: #84A98C; }
    #mainTab .nav-link.active { color: white; } /* White text on active tab */
  "))
  ),
  
  # Custom styling for FORECASTING tab
  tags$head(
    tags$style(HTML("
    #mainTab .nav-link[data-value='FORECASTING'] { background-color: #EBEBE4; } /* Distinct color for FORECASTING tab */
    #mainTab .nav-link[data-value='FORECASTING']:hover { background-color: #FFB300; } /* Hover effect for FORECASTING tab */
    #mainTab .nav-link.active[data-value='FORECASTING'] { background-color: #EBEBE4; color: black; } /* Active tab styling */
  "))
  ),
  
  # Sidebar panel with conditional elements based on the selected tab
  sidebar = sidebar(
    id = "menu1",
    width = 300,
    
    # Sidebar for all tabs except "Leads Data by Outlet"
    conditionalPanel(
      condition = "input.mainTab != 'Leads Data by Outlet'",
      selectInput(
        inputId = "Category",
        label = "Choose a reporting category",
        choices = c('FDB Sheds', 'SB Sheds', 'FDB Patios', 'Lifestyle', 'NZ Sheds'),
        selected = c('FDB Sheds', 'SB Sheds'),
        multiple = TRUE, 
        selectize = TRUE,
        width = '100%'
      ),
      numericInput(
        inputId = "unit_price",
        label = "Average Selling Price",
        value = 286,
        min = 200,
        max = 500,
        step = 1,
        width = '100%'
      ),
      dateRangeInput(
        inputId = "daterange1",
        label = "Date range",
        start = Sys.Date() - 360,
        end = Sys.Date(),
        width = '100%'
      ),
      actionButton(
        inputId = "refresh",
        icon = icon("refresh"),
        class = "btn-sm",
        label = "Refresh",
        width = '100%'
      ),
      actionButton(
        inputId = "loaddata",
        icon = icon("bars-progress"),
        class = "btn-sm",
        label = "Load Data",
        width = '100%'
      ),
      downloadButton(
        outputId = "downloadData",
        icon = icon("download"),
        class = "btn-sm",
        label = "Download",
        width = '100%'
      ),
      hr()
    ),
    
    # Sidebar specifically for "Leads Data by Outlet"
    conditionalPanel(
      condition = "input.mainTab == 'Leads Data by Outlet'",
      dateInput("startDate", "Select Start Date:", value = "2024-01-01"),
      dateInput("endDate", "Select End Date:", value = Sys.Date()),
      downloadButton("downloadDataFranchisee", "Download All Data")
    )
  ),
  
  # Main content area with tabs, ensuring the mainTab ID captures the selected tab
  navset_card_underline(
    id = "mainTab", # Make sure to set the id here to capture the selected tab in input.mainTab
    title = "Visualisations",
    useShinyjs(),
    
    # Define the main tabs and sub-tabs
    nav_panel("Plots",
              fluidRow(
                box(
                  label = "Current Stats",
                  width = 12,
                  fluidRow(
                    box(width = 1, textOutput('welcome', container = tags$h5)),
                    box(width = 2, title = h3("Today", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput("TodaySale")),
                    box(width = 2, title = h3("Yesterday", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput(outputId = "YesterdaySale")),
                    box(width = 2, title = h3("Week", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput(outputId = "ThisWeekSale")),
                    box(width = 2, title = h3("Last Week", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput(outputId = "LastWeekSale")),
                    box(width = 2, title = h3("Last Month", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput(outputId = "LastMonthSale"))
                  ),
                  fluidRow(
                    box(width = 5, title = h3("This Month", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput(outputId = "ThisMonthSale")),
                    box(width = 2, title = h3("AU Total Commission FY2025", align = "left", style = 'font-size:18px;color:blue;'), textOutput("Commission")),
                    box(width = 5, title = h3("Fin Year", align = "center", style = 'font-size:18px;color:blue;'), gaugeOutput(outputId = "FinYearSale"))
                  ),
                  fluidRow(
                    box(width = 5, title = h3("Number of Sheds", align = "center", style = 'font-size:18px;color:blue;'), girafeOutput(outputId = "number_sheds")),
                    box(width = 5, title = h3("$$$", align = "center", style = 'font-size:18px;color:blue;'), girafeOutput(outputId = "total_sales"))
                  )
                )
              )
    ),
    
    # Outlet Sales tab
    nav_panel("Outlet Sales",
              fluidRow(
                box(width = 1),
                box(width = 4, selectInput("Area", "State", choices = c('All', 'New South Wales', 'Victoria', 'Queensland', 'South Australia', "Tasmania", 'Western Australia', 'Northern Territory'), selected = 'All', multiple = FALSE, selectize = TRUE, width = "50%")),
                box(width = 4, selectInput("DistributorID", "Outlet", choices = c('None'), selected = 'All', multiple = FALSE, selectize = TRUE, width = "50%"))
              ),
              fluidRow(
                box(
                  width = 10, height = 16,
                  tabsetPanel(
                    tabPanel("Sales", leafletOutput("MapSale", width = "100%", height = "600px")),
                    tabPanel("Frequency", leafletOutput("MapFrequency", width = "100%", height = "600px"))
                  )
                )
              )
    ),
    
    # End Customer Sales tab
    nav_panel("End Customer Sales",
              fluidRow(
                box(width = 1),
                box(width = 4, selectInput("AusStateID", "State", choices = c('All', 'NSW', "ACT", "SA", "VIC", "TAS", "WA", "QLD", "NT"), selected = 'All', multiple = FALSE, selectize = TRUE, width = "50%")),
                box(width = 4, selectizeInput("PostCodeID", "Postcode", choices = NULL, multiple = FALSE, width = "50%"))
              ),
              fluidRow(
                box(
                  width = 10, height = 16,
                  tabsetPanel(
                    tabPanel("Sales", leafletOutput("CustomerMap", width = "100%", height = "600px")),
                    tabPanel("Frequency", leafletOutput("CustomerFrequency", width = "100%", height = "600px"))
                  )
                )
              )
    ),
    
    # Lead Map tab
    nav_panel("Lead Map",
              fluidRow(
                box(width = 1),
                box(width = 3, selectInput("LeadAusStateID", "State", choices = c('All', 'NSW', "ACT", "SA", "VIC", "TAS", "WA", "QLD", "NT"), selected = 'All', multiple = FALSE, selectize = TRUE, width = "50%")),
                box(width = 3, selectizeInput("LeadPostCodeID", "Postcode", choices = NULL, multiple = FALSE, width = "50%")),
                box(width = 3, selectizeInput("LeadStatus", "Status", choices = NULL, multiple = FALSE, width = "50%"))
              ),
              fluidRow(
                box(
                  width = 10, height = 16,
                  tabsetPanel(
                    tabPanel("Leads", leafletOutput("LeadMap", width = "100%", height = "600px"))
                  )
                )
              )
    ),
    
    # Leads Data by Outlet tab
    nav_panel("Leads Data by Outlet",
              tabsetPanel(
                tabPanel("Raw Data", textOutput("selectedDates"), DTOutput("rawdata")),
                tabPanel("App Total", downloadButton("downloadAppData", "Download App Total Data"), DTOutput("appTotal")),
                tabPanel("Web Total", downloadButton("downloadWebData", "Download Web Total Data"), DTOutput("webTotal")),
                tabPanel("Bar Charts for App and Web Total", 
                         h3("App Total Bar Chart"), plotOutput("appBarChart"), 
                         h3("Web Total Bar Chart"), plotOutput("webBarChart")),
                # New tab for monthly graph
                tabPanel("Leads Distribution Over Time",
                         h3("Leads Distribution Over Time"),
                       
                         plotOutput("leads_combined_line_chart"),
                         downloadButton("download_leads_data","Downloads Leads Data")
                )
              )
    ),
    
    # Add the new FORECASTING tab
    nav_panel("FORECASTING",
              fluidRow(
                column(12,
                       tags$div(
                         style = "text-align: center; margin-bottom: 40px;",
                         tags$img(
                           src = "https://i.ibb.co/wBRDpjX/This-section-is-under-development.png", 
                           style = "width: 40%; max-width: 400px; display: block; margin: 0 auto;" 
                         )
                       )
                )
              ),
              fluidRow(
                column(12,
                       tags$div(
                         style = "text-align: center; margin-bottom: 40px;",
                         tags$img(
                           src = "https://s13.gifyu.com/images/SJOsv.gif", 
                           style = "width: 40%; max-width: 400px; display: block; margin: 0 auto;" ,autoplay=NA,loop=NA
                         )
                       )
                )
              ),        
    ),
    # Data Table tab
    nav_panel("Data Table",
              fluidRow(
                box(width = 12, DTOutput("result"))
              )
    )
  )
)


